# airport_detection

- 模型来源
  - /home/<USER>/YOLOv6/runs/train/sp200vf_4c_250228
  - /home/<USER>/YOLOv6/runs/train/sp500rvf_ir_taji_240629/weights

- ************** 显卡驱动找不到问题解决
- [https://huazhe1995.github.io/2020/01/01/ubuntu-an-zhuang-nvidia-qu-dong-run-fang-shi/](https://huazhe1995.github.io/2020/01/01/ubuntu-an-zhuang-nvidia-qu-dong-run-fang-shi/)
- 之前系统里缺少与当前内核匹配的 NVIDIA 驱动内核模块，`dkms install` 则负责自动编译并安装对应版本的内核模块


## protobuf安装

```shell
unzip protobuf-3.11.4-source.zip
cd protobuf-3.11.4-source/cmake
cmake . -Dprotobuf_BUILD_TESTS=OFF -Dprotobuf_BUILD_SHARED_LIBS=ON
cmake --build . -- -j32
mkdir /home/<USER>/install/protobuf_3.11.4_lib
make install DESTDIR=/home/<USER>/install/protobuf_3.11.4_lib
cd /home/<USER>/install/protobuf_3.11.4_lib
cp -r ./usr/local/bin ./usr/local/include ./usr/local/lib ./
```

```shell
tar zxf TensorRT-*******.Linux.x86_64-gnu.cuda-11.6.cudnn8.4.tar.gz

/usr/src/tensorrt/bin/trtexec --onnx=best_ckpt.onnx  --minShapes=images:1x3x640x640 --optShapes=images:1x3x640x640 --maxShapes=images:16x3x640x640 --saveEngine=best_ckpt.FP32.trtmodel
/usr/src/tensorrt/bin/trtexec --onnx=yolov6s.onnx  --minShapes=images:1x3x640x640 --optShapes=images:1x3x640x640 --maxShapes=images:16x3x640x640 --saveEngine=yolov6s.FP32.trtmodel
```
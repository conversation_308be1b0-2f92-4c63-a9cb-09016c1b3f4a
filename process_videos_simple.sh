#!/bin/bash

# 简化版本的批量处理脚本
# 作者: TOT
# 日期: 2025-08-11

cd workspace

path_dir="videos"
output="results/0812_3"

# 创建输出目录
mkdir -p "${output}"

# 处理所有mp4文件
for video_file in "${path_dir}"/*.mp4; do
    if [ -f "${video_file}" ]; then
        filename=$(basename "${video_file}" .mp4)
        echo "处理文件: ${filename}"
        
        # 执行处理命令
        ./pro airport "${video_file}"
        ./pro yolo "${video_file}"
        
        # 创建输出目录并移动文件
        output_path="${output}/${filename}"
        mkdir -p "${output_path}"
        
        # 移动生成的文件
        [ -f "demo_vl_single_thread.mp4" ] && mv "demo_vl_single_thread.mp4" "${output_path}/"
        [ -f "v5.mp4" ] && mv "v5.mp4" "${output_path}/"
        [ -f "v11.mp4" ] && mv "v11.mp4" "${output_path}/"
        
        echo "完成: ${filename}"
    fi
done

echo "所有文件处理完成！"

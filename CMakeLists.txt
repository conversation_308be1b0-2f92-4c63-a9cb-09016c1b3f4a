cmake_minimum_required(VERSION 2.9)
project(pro)

option(CUDA_USE_STATIC_CUDA_RUNTIME OFF)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_BUILD_TYPE Debug)
set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/workspace)

# 如果你的opencv找不到，可以自己指定目录
# set(OpenCV_DIR   "/home/<USER>/libs/opencv-4.6.0/build")
# ============ opencv ============
find_package(OpenCV)
include_directories(${OpenCV_INCLUDE_DIRS})

set(CUDA_TOOLKIT_ROOT_DIR     "/usr/local/cuda")
set(CUDNN_DIR    "/usr/local/cuda")
set(TENSORRT_DIR "/home/<USER>/libs/tensorrt/TensorRT-8.6.1.6.Linux.x86_64-gnu.cuda-11.8/TensorRT-8.6.1.6")

# 因为protobuf，需要用特定版本，所以这里指定路径
set(PROTOBUF_DIR "/home/<USER>/libs/protobuf-all-3.11.4/install")

find_package(CUDA REQUIRED)
# find_package(OpenCV)

include_directories(
    ${PROJECT_SOURCE_DIR}/src
    ${PROJECT_SOURCE_DIR}/src/application
    ${PROJECT_SOURCE_DIR}/src/application/tools
    ${PROJECT_SOURCE_DIR}/src/tensorRT
    ${PROJECT_SOURCE_DIR}/src/tensorRT/common
    ${OpenCV_INCLUDE_DIRS}
    ${CUDA_TOOLKIT_ROOT_DIR}/include
    ${PROTOBUF_DIR}/include
    ${TENSORRT_DIR}/include
    ${CUDNN_DIR}/include
    # ${OpenCV_DIR}/include/opencv4
)

# 切记，protobuf的lib目录一定要比tensorRT目录前面，因为tensorRTlib下带有protobuf的so文件
# 这可能带来错误
link_directories(
    ${PROTOBUF_DIR}/lib
    ${TENSORRT_DIR}/lib
    ${CUDA_TOOLKIT_ROOT_DIR}/lib64
    ${CUDNN_DIR}/lib
    # ${OpenCV_DIR}/lib
)

if(CMAKE_BUILD_TYPE MATCHES Debug)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14 -Wall -O0 -Wfatal-errors -pthread -w -g")
    set(CUDA_NVCC_FLAGS "${CUDA_NVCC_FLAGS} -std=c++14 -O0 -Xcompiler -fPIC -g -w ${CUDA_GEN_CODE}")
elseif(CMAKE_BUILD_TYPE MATCHES Release)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14 -Wall -O3 -Wfatal-errors -pthread -w")
    set(CUDA_NVCC_FLAGS "${CUDA_NVCC_FLAGS} -std=c++14 -O3 -Xcompiler -fPIC -w ${CUDA_GEN_CODE}")
endif()

file(GLOB_RECURSE cpp_srcs ${PROJECT_SOURCE_DIR}/src/*.cpp)
file(GLOB_RECURSE cuda_srcs ${PROJECT_SOURCE_DIR}/src/*.cu)
cuda_add_library(plugin_list SHARED ${cuda_srcs})
target_link_libraries(plugin_list nvinfer nvinfer_plugin)
target_link_libraries(plugin_list cuda cublas cudart cudnn)
target_link_libraries(plugin_list protobuf pthread)
target_link_libraries(plugin_list ${OpenCV_LIBS})
target_link_libraries(plugin_list opencv_core opencv_imgproc opencv_videoio opencv_highgui opencv_imgcodecs)

add_executable(pro ${cpp_srcs})

# 如果提示插件找不到，请使用dlopen(xxx.so, NOW)的方式手动加载可以解决插件找不到问题
target_link_libraries(pro plugin_list)

# shared
set(cpp_srcs_shared ${cpp_srcs})

set(EXCLUDE_FILES
    ${PROJECT_SOURCE_DIR}/src/main.cpp
    ${PROJECT_SOURCE_DIR}/src/applicatopm/test.cpp
    ${PROJECT_SOURCE_DIR}/src/application/app_birdtrack.cpp
    ${PROJECT_SOURCE_DIR}/src/application/app_bytetrack.cpp
    ${PROJECT_SOURCE_DIR}/src/application/app_yolo.cpp
    ${PROJECT_SOURCE_DIR}/src/application/test_yolo_map.cpp)

foreach(EXCLUDE_FILE ${EXCLUDE_FILES})
    list(REMOVE_ITEM cpp_srcs_shared ${EXCLUDE_FILE})
endforeach()

cuda_add_library(shared SHARED ${cpp_srcs_shared} ${cuda_srcs})

target_link_libraries(shared nvinfer nvinfer_plugin)
target_link_libraries(shared cuda cublas cudart cudnn)
target_link_libraries(shared protobuf pthread)
target_link_libraries(shared ${OpenCV_LIBS})
target_link_libraries(shared opencv_core opencv_imgproc opencv_videoio opencv_highgui opencv_imgcodecs)

set_target_properties(shared PROPERTIES
    OUTPUT_NAME "birdtrack"
    LIBRARY_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/workspace)

# export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/home/<USER>/lean/TensorRT-8.5.1.7/lib
add_custom_target(
    yolo
    DEPENDS pro
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR}/workspace
    COMMAND ./pro yolo
)

add_custom_target(
    track
    DEPENDS pro
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR}/workspace
    COMMAND ./pro track
)

add_custom_target(
    birdtrack
    DEPENDS pro
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR}/workspace
    COMMAND ./pro birdtrack
)

add_custom_target(
    test_yolo_map
    DEPENDS pro
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR}/workspace
    COMMAND ./pro test_yolo_map
)

add_custom_target(
    build_shared
    DEPENDS shared
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR}/workspace
)
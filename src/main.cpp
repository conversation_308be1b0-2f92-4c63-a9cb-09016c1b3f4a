
#include <stdio.h>
#include <string.h>
#include <functional>
#include <common/ilogger.hpp>
#include <string>
using namespace std;

int app_kcf();
int app_yolo(const string& path);
int app_track();
int test_yolo_map();
int app_airport(const string& path);

int main(int argc, char** argv){
    
    const char* method = "yolo";
    const char* path = "";
    if(argc > 1){
        method = argv[1];
        path = argv[2];
    }
    else
    {
        return 0;
    }

    if(strcmp(method, "yolo") == 0){
        app_yolo(path);
    }else if(strcmp(method, "track") == 0){
        app_track();
    }else if(strcmp(method, "test_yolo_map") == 0){
        test_yolo_map();
    }else if(strcmp(method, "airport") == 0){
        app_airport(path);
    }else if(strcmp(method, "kcf") == 0){
        app_kcf();
    }else{
        printf("Unknow method: %s\n", method);
        printf(
            "Help: \n"
            "    ./pro method[yolo、test_yolo_map]\n"
            "\n"
            "    ./pro yolo\n"
            "    ./pro test_yolo_map\n"
        );
    } 
    return 0;
}

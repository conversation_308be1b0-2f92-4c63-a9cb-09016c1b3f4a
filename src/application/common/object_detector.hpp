#ifndef OBJECT_DETECTOR_HPP
#define OBJECT_DETECTOR_HPP

#include <vector>

namespace ObjectDetector{

    struct Box{
        float left, top, right, bottom, confidence;
        int class_label;
        int track_id = 0; // 默认 0，跟踪目标为 1

        Box() = default;

        Box(float left, float top, float right, float bottom, float confidence, int class_label)
        :left(left), top(top), right(right), bottom(bottom), confidence(confidence), class_label(class_label){}

        float width() const {return right - left; }
        float height() const {return bottom - top;}
        float center_x() const {return (left + right) / 2;}
        float center_y() const {return (top + bottom) / 2;}
        float area() const {return width() * height();}
    };

    typedef std::vector<Box> BoxArray;
};


#endif // OBJECT_DETECTOR_HPP
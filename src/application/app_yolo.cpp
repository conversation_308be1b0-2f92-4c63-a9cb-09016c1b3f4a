
#include <builder/trt_builder.hpp>
#include <infer/trt_infer.hpp>
#include <common/ilogger.hpp>
#include "app_yolo/yolo.hpp"
#include "tools/zmq_remote_show.hpp"

using namespace std;

static const char* cocolabels[] = {
    "0","1","2","3","4","5","6","7",
    "drone","bird",
    "fly", "fly", "fly", "UAV", "bird", "UAV", "UAV", "fly",
    "person", "bicycle", "car", "motorcycle", "airplane",
    "bus", "train", "truck", "boat", "traffic light", "fire hydrant",
    "stop sign", "parking meter", "bench", "bird", "cat", "dog", "horse",
    "sheep", "cow", "elephant", "bear", "zebra", "giraffe", "backpack",
    "umbrella", "handbag", "tie", "suitcase", "frisbee", "skis",
    "snowboard", "sports ball", "kite", "baseball bat", "baseball glove",
    "skateboard", "surfboard", "tennis racket", "bottle", "wine glass",
    "cup", "fork", "knife", "spoon", "bowl", "banana", "apple", "sandwich",
    "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake",
    "chair", "couch", "potted plant", "bed", "dining table", "toilet", "tv",
    "laptop", "mouse", "remote", "keyboard", "cell phone", "microwave",
    "oven", "toaster", "sink", "refrigerator", "book", "clock", "vase",
    "scissors", "teddy bear", "hair drier", "toothbrush"
};

// static const char* labels[] = {"aireplane", "landing gear", "drone", "bire", "balloon"};
static const char* labels[] = {
    "0","1","2","3","4","5","6","7",};

static void append_to_file(const string& file, const string& data){
    FILE* f = fopen(file.c_str(), "a+");
    if(f == nullptr){
        INFOE("Open %s failed.", file.c_str());
        return;
    }

    fprintf(f, "%s\n", data.c_str());
    fclose(f);
}

static void inference_and_performance(int deviceid, const string& engine_file, TRT::Mode mode, Yolo::Type type, const string& model_name){

    auto engine = Yolo::create_infer(
        engine_file,                // engine file
        type,                       // yolo type, Yolo::Type::V5 / Yolo::Type::X
        deviceid,                   // gpu id
        0.1f,                      // confidence threshold
        0.45f,                      // nms threshold
        Yolo::NMSMethod::ClassAgnosticGPU,   // NMS method, fast GPU / CPU
        1024,                       // max objects
        false                       // preprocess use multi stream
    );
    if(engine == nullptr){
        INFOE("Engine is nullptr");
        return;
    }

    auto files = iLogger::find_files("inference", "*.jpg;*.jpeg;*.png;*.gif;*.tif");
    vector<cv::Mat> images;
    for(int i = 0; i < files.size(); ++i){
        auto image = cv::imread(files[i]);
        images.emplace_back(image);
    }

    // warmup
    vector<shared_future<Yolo::BoxArray>> boxes_array;
    for(int i = 0; i < 10; ++i)
        boxes_array = engine->commits(images);
    boxes_array.back().get();
    boxes_array.clear();
    
    /////////////////////////////////////////////////////////
    const int ntest = 100;
    auto begin_timer = iLogger::timestamp_now_float();

    for(int i  = 0; i < ntest; ++i)
        boxes_array = engine->commits(images);
    
    // wait all result
    boxes_array.back().get();

    float inference_average_time = (iLogger::timestamp_now_float() - begin_timer) / ntest / images.size();
    auto type_name = Yolo::type_name(type);
    auto mode_name = TRT::mode_string(mode);
    INFO("%s[%s] average: %.2f ms / image, FPS: %.2f", engine_file.c_str(), type_name, inference_average_time, 1000 / inference_average_time);
    append_to_file("perf.result.log", iLogger::format("%s,%s,%s,%f", model_name.c_str(), type_name, mode_name, inference_average_time));

    string root = iLogger::format("%s_%s_%s_result", model_name.c_str(), type_name, mode_name);
    iLogger::rmtree(root);
    iLogger::mkdir(root);

    for(int i = 0; i < boxes_array.size(); ++i){

        auto& image = images[i];
        auto boxes  = boxes_array[i].get();
        
        for(auto& obj : boxes){
            // if(obj.class_label != 0)    continue;
            uint8_t b, g, r;
            tie(b, g, r) = iLogger::random_color(obj.class_label);
            cv::rectangle(image, cv::Point(obj.left, obj.top), cv::Point(obj.right, obj.bottom), cv::Scalar(b, g, r), 5);

            auto name    = cocolabels[obj.class_label];
            auto caption = iLogger::format("%s %.2f", name, obj.confidence);
            int width    = cv::getTextSize(caption, 0, 1, 2, nullptr).width + 10;
            cv::rectangle(image, cv::Point(obj.left-3, obj.top-33), cv::Point(obj.left + width, obj.top), cv::Scalar(b, g, r), -1);
            cv::putText(image, caption, cv::Point(obj.left, obj.top-5), 0, 1, cv::Scalar::all(0), 2, 16);
        }

        string file_name = iLogger::file_name(files[i], false);
        string save_path = iLogger::format("%s/%s.jpg", root.c_str(), file_name.c_str());
        INFO("Save to %s, %d object, average time %.2f ms", save_path.c_str(), boxes.size(), inference_average_time);
        cv::imwrite(save_path, image);
    }
    engine.reset();
}

static void test(Yolo::Type type, TRT::Mode mode, const string& model){

    int deviceid = 0;
    auto mode_name = TRT::mode_string(mode);
    TRT::set_device(deviceid);

    auto int8process = [=](int current, int count, const vector<string>& files, shared_ptr<TRT::Tensor>& tensor){

        INFO("Int8 %d / %d", current, count);

        for(int i = 0; i < files.size(); ++i){
            auto image = cv::imread(files[i]);
            Yolo::image_to_tensor(image, tensor, type, i);
        }
    };

    const char* name = model.c_str();
    INFO("===================== test %s %s %s ==================================", Yolo::type_name(type), mode_name, name);

    string onnx_file = iLogger::format("%s.onnx", name);
    string model_file = iLogger::format("%s.%s.trtmodel", name, mode_name);
    int test_batch_size = 2;
    
    if(not iLogger::exists(model_file)){
        TRT::compile(
            mode,                       // FP32、FP16、INT8
            test_batch_size,            // max batch size
            onnx_file,                  // source 
            model_file,                 // save to
            {},
            int8process,
            "calib_data_VL_special",
            "calibrator_entropy_VL.cache",
            TRT::Calibrator::Entropy,
            1ul << 30
        );
    }

    inference_and_performance(deviceid, model_file, mode, type, name);
}

static void test_video(const string& engine_file, const string& video_path){
    
    auto engine = Yolo::create_infer(
        engine_file,                            // engine file
        Yolo::Type::V8,                         // yolo type, Yolo::Type::V8 / Yolo::Type::X
        0,                                      // gpu id
        0.60f,                                  // confidence threshold
        0.45f,                                  // nms threshold
        Yolo::NMSMethod::ClassAgnosticGPU,               // NMS method, fast GPU / CPU
        1024,                                   // max objects
        false                                   // preprocess use multi stream
    );
    if(engine == nullptr){
        INFOE("Engine is nullptr");
        return;
    }

    cv::Mat frame;
    cv::VideoCapture cap(video_path);
    if(!cap.isOpened()){
        INFOE("Camera open failed");
        return;
    }
    // cv::VideoWriter write_vl("demo_vl.mp4", cv::VideoWriter::fourcc('m', 'p', '4', 'v'), 25.0, cv::Size(1920, 1080));
    cv::VideoWriter write_ir("demo_ir.mp4", cv::VideoWriter::fourcc('m', 'p', '4', 'v'), 25.0, cv::Size(640, 512));
    // iLogger::set_logger_save_directory("save");
    auto remote_show = create_zmq_remote_show("tcp://0.0.0.0:15556");
    int count = 1;
    while(cap.read(frame)){
        
        auto t0 = iLogger::timestamp_now_float();
        auto boxes = engine->commit(frame).get();
        INFO("boxes size = %d", boxes.size());
        for(auto& obj : boxes){
            uint8_t b, g, r;
            tie(b, g, r) = iLogger::random_color(obj.class_label);
            cv::rectangle(frame, cv::Point(obj.left, obj.top), cv::Point(obj.right, obj.bottom), cv::Scalar(b, g, r), 5);

            // auto name    = cocolabels[obj.class_label];
            auto name    = labels[obj.class_label];
            auto caption = iLogger::format("%s %.2f", name, obj.confidence);
            int width    = cv::getTextSize(caption, 0, 1, 2, nullptr).width + 10;
            cv::rectangle(frame, cv::Point(obj.left-3, obj.top-33), cv::Point(obj.left + width, obj.top), cv::Scalar(b, g, r), -1);
            cv::putText(frame, caption, cv::Point(obj.left, obj.top-5), 0, 1, cv::Scalar::all(0), 2, 16);
            // auto size = iLogger::format("count = %d, width = %.2f, height = %.2f", count, obj.width(), obj.height());
            // cv::putText(frame, size, cv::Point(20, 170), 0, 1, cv::Scalar::all(0), 2, 16);
            // INFO("count = %d, width = %.2f, height = %.2f, center_x = %.2f, center_y = %.2f", 
            //      count, obj.width(), obj.height(), (obj.left+obj.right)/2, (obj.top+obj.bottom)/2);
        }
        
        count++;
        auto fee = iLogger::timestamp_now_float() - t0;
        INFO("fee = %.2f ms, FPS = %.2f", fee, 1 / fee * 1000);
        // remote_show->post(frame);
        // write_vl.write(frame);
        // write_ir.write(frame);
        cv::imshow("frame", frame);
        int key = cv::waitKey(1);
        if(key == 27)
            break;
    }
    
    // char end_signal = 'x';
    // remote_show->post(&end_signal, 1);
    INFO("Done");
    write_ir.release();
    cap.release();
    cv::destroyAllWindows();
    engine.reset();
}


static void test_video_v11(const string& engine_file, const string& video_path){
    
    auto engine = Yolo::create_infer(
        engine_file,                            // engine file
        Yolo::Type::V8,                         // yolo type, Yolo::Type::V8 / Yolo::Type::X
        0,                                      // gpu id
        0.20f,                                  // confidence threshold
        0.45f,                                  // nms threshold
        Yolo::NMSMethod::FastGPU,               // NMS method, fast GPU / CPU
        1024,                                   // max objects
        false                                   // preprocess use multi stream
    );
    if(engine == nullptr){
        INFOE("Engine is nullptr");
        return;
    }

    cv::Mat frame;
    cv::VideoCapture cap(video_path);
    if(!cap.isOpened()){
        INFOE("Camera open failed");
        return;
    }

    // 先读取第一帧获取图像尺寸
    if(!cap.read(frame)){
        INFOE("Failed to read first frame");
        return;
    }

    // 根据实际图像尺寸创建VideoWriter
    cv::Size frame_size(frame.cols, frame.rows);
    // cv::VideoWriter write_vl("demo_vl.mp4", cv::VideoWriter::fourcc('m', 'p', '4', 'v'), 25.0, frame_size);
    cv::VideoWriter write_ir("v11.mp4", cv::VideoWriter::fourcc('m', 'p', '4', 'v'), 25.0, frame_size);
    // iLogger::set_logger_save_directory("save");
    auto remote_show = create_zmq_remote_show("tcp://0.0.0.0:15556");
    int count = 1;

    // 处理第一帧（已经读取）
    do {
        
        auto t0 = iLogger::timestamp_now_float();
        auto boxes = engine->commit(frame).get();
        INFO("boxes size = %d", boxes.size());
        for(auto& obj : boxes){
            uint8_t b, g, r;
            tie(b, g, r) = iLogger::random_color(obj.class_label);
            cv::rectangle(frame, cv::Point(obj.left, obj.top), cv::Point(obj.right, obj.bottom), cv::Scalar(b, g, r), 5);

            // auto name    = cocolabels[obj.class_label];
            auto name    = labels[obj.class_label];
            auto caption = iLogger::format("%s %.2f", name, obj.confidence);
            int width    = cv::getTextSize(caption, 0, 1, 2, nullptr).width + 10;
            cv::rectangle(frame, cv::Point(obj.left-3, obj.top-33), cv::Point(obj.left + width, obj.top), cv::Scalar(b, g, r), -1);
            cv::putText(frame, caption, cv::Point(obj.left, obj.top-5), 0, 1, cv::Scalar::all(0), 2, 16);
            // auto size = iLogger::format("count = %d, width = %.2f, height = %.2f", count, obj.width(), obj.height());
            // cv::putText(frame, size, cv::Point(20, 170), 0, 1, cv::Scalar::all(0), 2, 16);
            // INFO("count = %d, width = %.2f, height = %.2f, center_x = %.2f, center_y = %.2f", 
            //      count, obj.width(), obj.height(), (obj.left+obj.right)/2, (obj.top+obj.bottom)/2);
        }
        
        count++;
        auto fee = iLogger::timestamp_now_float() - t0;
        INFO("fee = %.2f ms, FPS = %.2f", fee, 1 / fee * 1000);
        // remote_show->post(frame);
        // write_vl.write(frame);
        write_ir.write(frame);
        // cv::imshow("frame", frame);
        // int key = cv::waitKey(1);
        // if(key == 27)
        //     break;
    } while(cap.read(frame));
    
    // char end_signal = 'x';
    // remote_show->post(&end_signal, 1);
    INFO("Done");
    write_ir.release();
    cap.release();
    cv::destroyAllWindows();
    engine.reset();
}


static void test_video_v5(const string& engine_file, const string& video_path){

    auto engine = Yolo::create_infer(
        engine_file,                            // engine file
        Yolo::Type::V5,                         // yolo type, Yolo::Type::V8 / Yolo::Type::X
        0,                                      // gpu id
        0.45f,                                  // confidence threshold
        0.45f,                                  // nms threshold
        Yolo::NMSMethod::FastGPU,               // NMS method, fast GPU / CPU
        1024,                                   // max objects
        false                                   // preprocess use multi stream
    );
    if(engine == nullptr){
        INFOE("Engine is nullptr");
        return;
    }

    cv::Mat frame;
    cv::VideoCapture cap(video_path);
    if(!cap.isOpened()){
        INFOE("Camera open failed");
        return;
    }

    // 先读取第一帧获取图像尺寸
    if(!cap.read(frame)){
        INFOE("Failed to read first frame");
        return;
    }

    // 根据实际图像尺寸创建VideoWriter
    cv::Size frame_size(frame.cols, frame.rows);
    cv::VideoWriter write_ir("v5.mp4", cv::VideoWriter::fourcc('m', 'p', '4', 'v'), 25.0, frame_size);
    // iLogger::set_logger_save_directory("save");
    auto remote_show = create_zmq_remote_show("tcp://0.0.0.0:15556");
    int count = 1;

    // 处理第一帧（已经读取）
    do {
        
        auto t0 = iLogger::timestamp_now_float();
        auto boxes = engine->commit(frame).get();
        INFO("boxes size = %d", boxes.size());
        for(auto& obj : boxes){
            uint8_t b, g, r;
            tie(b, g, r) = iLogger::random_color(obj.class_label);
            cv::rectangle(frame, cv::Point(obj.left, obj.top), cv::Point(obj.right, obj.bottom), cv::Scalar(b, g, r), 5);

            // auto name    = cocolabels[obj.class_label];
            auto name    = labels[obj.class_label];
            auto caption = iLogger::format("%s %.2f", name, obj.confidence);
            int width    = cv::getTextSize(caption, 0, 1, 2, nullptr).width + 10;
            cv::rectangle(frame, cv::Point(obj.left-3, obj.top-33), cv::Point(obj.left + width, obj.top), cv::Scalar(b, g, r), -1);
            cv::putText(frame, caption, cv::Point(obj.left, obj.top-5), 0, 1, cv::Scalar::all(0), 2, 16);
            // auto size = iLogger::format("count = %d, width = %.2f, height = %.2f", count, obj.width(), obj.height());
            // cv::putText(frame, size, cv::Point(20, 170), 0, 1, cv::Scalar::all(0), 2, 16);
            // INFO("count = %d, width = %.2f, height = %.2f, center_x = %.2f, center_y = %.2f", 
            //      count, obj.width(), obj.height(), (obj.left+obj.right)/2, (obj.top+obj.bottom)/2);
        }
        
        count++;
        auto fee = iLogger::timestamp_now_float() - t0;
        INFO("fee = %.2f ms, FPS = %.2f", fee, 1 / fee * 1000);
        // remote_show->post(frame);
        // write_vl.write(frame);
        write_ir.write(frame);
        // cv::imshow("frame", frame);
        // int key = cv::waitKey(1);
        // if(key == 27)
        //     break;
    } while(cap.read(frame));
    
    // char end_signal = 'x';
    // remote_show->post(&end_signal, 1);
    INFO("Done");
    write_ir.release();
    cap.release();
    cv::destroyAllWindows();
    engine.reset();
}

static void test_single_image(){
    
    auto engine = Yolo::create_infer(
        "best_ckpt_VL_v3.1.FP16.trtmodel",                // engine file
        Yolo::Type::V6,                         // yolo type, Yolo::Type::V8 / Yolo::Type::X
        0,                                      // gpu id
        0.60f,                                  // confidence threshold
        0.45f,                                  // nms threshold
        Yolo::NMSMethod::FastGPU,               // NMS method, fast GPU / CPU
        1024,                                   // max objects
        false                                   // preprocess use multi stream
    );
    if(engine == nullptr){
        INFOE("Engine is nullptr");
        return;
    }

    // cv::Mat image = cv::imread("inference_test/test.jpg");
    cv::Mat image = cv::imread("test.jpg");
    if(image.empty()){
        INFOE("Image is empty");
        return;
    }    

    auto boxes = engine->commit(image).get();

    for(auto& obj : boxes){
        // if(obj.class_label != 0)    continue;
        INFO("aeroplane: left = %.2f, top = %.2f, right = %.2f, bottom = %.2f, conf = %.2f", obj.left, obj.right, obj.right, obj.bottom, obj.confidence);
        // cv::Mat cropImage = image(cv::Rect(obj.left-10, obj.top-10, obj.width()+20, obj.height()+20));
        // cv::imwrite("Crop.jpg", cropImage);
        uint8_t b, g, r;
        tie(b, g, r) = iLogger::random_color(obj.class_label);
        cv::rectangle(image, cv::Point(obj.left, obj.top), cv::Point(obj.right, obj.bottom), cv::Scalar(b, g, r), 5);

        auto name    = labels[obj.class_label];
        auto caption = iLogger::format("%s %.2f", name, obj.confidence);
        int width    = cv::getTextSize(caption, 0, 1, 2, nullptr).width + 10;
        cv::rectangle(image, cv::Point(obj.left-3, obj.top-33), cv::Point(obj.left + width, obj.top), cv::Scalar(b, g, r), -1);
        cv::putText(image, caption, cv::Point(obj.left, obj.top-5), 0, 1, cv::Scalar::all(0), 2, 16);
    }
    INFO("Save to Result.jpg, %d objects", boxes.size());
    cv::imwrite("Result.jpg", image);
    engine.reset();
}

static void perf(){
    int batch = 3;
    vector<cv::Mat> images;
    for(int i = images.size(); i < batch; ++i) images.push_back(cv::imread("inference/aircraft_7.jpg"));
    INFO("images size = %d, width = %d, height = %d", images.size(), images[0].cols, images[0].rows);

    auto engine = Yolo::create_infer("best_ckpt_vl_p6_n_entropy_test.INT8.trtmodel", Yolo::Type::V6, 0);
    if(engine == nullptr){
        return;
    }

    const int ntest = 100;
    // warmup
    for(int i = 0; i < 100; ++i){
        auto boxes = engine->commit(images[0]).get();
    }

    auto begin_timer_single = iLogger::timestamp_now_float();
    for(int i = 0; i < ntest; ++i){
        auto boxes = engine->commit(images[0]).get();
    }
    INFO("BATCH1: %.2f ms", (iLogger::timestamp_now() - begin_timer_single) / ntest);
    
    // warmup
    for(int i = 0; i < 100; ++i){
        vector<shared_future<Yolo::BoxArray>> boxes_array;
        boxes_array = engine->commits(images);
        boxes_array.back().get();
    }

    auto begin_timer_batch = iLogger::timestamp_now_float();
    for(int i = 0; i < ntest; ++i){
        vector<shared_future<Yolo::BoxArray>> boxes_array;
        boxes_array = engine->commits(images);
        boxes_array.back().get();
    }
    INFO("BATCH3: %.2f ms", (iLogger::timestamp_now_float() - begin_timer_batch) / ntest / batch);
}

int app_yolo(const string& path){

    // perf();
    // test(Yolo::Type::V8, TRT::Mode::FP16, "yolo11e/yoloe-11s");
    // test(Yolo::Type::V5, TRT::Mode::FP16, "drone_bird");
    test_video_v11("trtmodel/yoloe-11s.FP16.trtmodel", path);
    test_video_v5("trtmodel/drone_bird.FP16.trtmodel", path);
    // test(Yolo::Type::V6, TRT::Mode::FP16, "best_ckpt_airplane_IR_v2.3_512x640");
    // test(Yolo::Type::V6, TRT::Mode::FP16, "best_ckpt_airplane_VL_v4_384x640");
    // test(Yolo::Type::V6, TRT::Mode::FP16, "ground_ir");
    // test(Yolo::Type::V6, TRT::Mode::FP16, "ground_vl");
    // test_video("best_ckpt_vl_p6_n_entropy_test.INT8.trtmodel", "video/vl_2.mp4");
    // test_video("yolov6s.FP16.trtmodel", "video/person.mp4");
    // test_video("best_ckpt_drone_ir.FP16.trtmodel", "videos/ir.mp4");
    // test_video("best_ckpt_drone_vl.FP16.trtmodel", "videos/20241204110907933013_vis.mp4");
    // test_video("best_ckpt_drone_ir.FP16.trtmodel", "videos/20241204110906525012_ir.mp4");
    // test_single_image();
    return 0;
}
#include "common/ilogger.hpp"
#include "app_kcf/kcftracker.hpp"

using namespace cv;
using namespace std;
using namespace kcf;

static void test_video(){

	// tracker configuration
	bool HOG 	     = false;
	bool FIXEDWINDOW = true;
	bool MULTISCALE  = true;
	bool LAB 		 = false;

	// create KCFTracker object
	KCFTracker tracker(HOG, FIXEDWINDOW, MULTISCALE, LAB);
	cv::Rect roi(23, 654, 30, 20);
	// cv::Rect roi(277, 429, 31, 30);

	VideoCapture cap("test_videos/1874620712283672576.mp4");
	// VideoCapture cap("test_videos/1874620320137220096.mp4");
	if(!cap.isOpened()){
		INFOE("unable to open video file");
		return;
	}

	VideoWriter write_vl("kcf_demo_vl.mp4", VideoWriter::fourcc('m', 'p', '4', 'v'), 25.0, <PERSON><PERSON>(1920, 1080));

	Mat frame;
	bool init_frame = true;
	while(true){

		if(!cap.read(frame)) break;

		if(init_frame){
			tracker.init(roi, frame);
			init_frame = false;
			cv::rectangle(frame, roi, cv::Scalar(0, 255, 255), 2);
		}else{
			auto box = tracker.update(frame);
			if(box.x > 0 && box.y > 0){
				cv::rectangle(frame, box, cv::Scalar(0, 255, 255), 2);
				// rectangle(frame, Point(box.x, box.y), Point(box.x+box.width, box.y+box.height), Scalar( 0, 255, 255 ), 2);
			}
			INFO("left = %d, top = %d, width = %d, height = %d", box.x, box.y, box.width, box.height);
		}

		write_vl.write(frame);
	}
	cap.release();
	write_vl.release();
}

int app_kcf(){

	test_video();
}

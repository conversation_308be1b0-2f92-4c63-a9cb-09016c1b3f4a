
#include <builder/trt_builder.hpp>
#include <infer/trt_infer.hpp>
#include <common/ilogger.hpp>
#include <opencv2/opencv.hpp>
#include "app_yolo/yolo.hpp"
#include "app_bytetrack/byte_tracker.hpp"
#include "app_bytetrack/strack.hpp"
#include "tools/zmq_remote_show.hpp"

using namespace cv;
using namespace std;

static const char* labels[] = {"aireplane", "landing gear"};

static void compile(){

    TRT::set_device(0);

    auto type = Yolo::Type::X;
    auto int8process = [=](int current, int count, const vector<string>& files, shared_ptr<TRT::Tensor>& tensor){

        INFO("Int8 %d / %d", current, count);

        for(int i = 0; i < files.size(); ++i){
            auto image = cv::imread(files[i]);
            Yolo::image_to_tensor(image, tensor, type, i);
        }
    };

    int test_batch_size = 16;

    TRT::compile(
        TRT::Mode::FP16,           
        test_batch_size,            
        "best_ckpt.onnx",               
        "best_ckpt.FP16.trtmodel", 
        {},
        int8process,
        "inference"
    );

    INFO("Done.");
}

static void test_ByteTracker(){
    
    // auto engine = Yolo::create_infer("best_ckpt_vl_p6_n.FP16.trtmodel", Yolo::Type::V6, 0);
    auto engine = Yolo::create_infer("best_ckpt_IR.FP16.trtmodel", Yolo::Type::V6, 0);

    // const string input_video_path = "video/vl.mp4";
    const string input_video_path = "video/ir.mp4";

    VideoCapture cap(input_video_path);
	if (!cap.isOpened()){
        INFOE("Could not open the video");
		return;
    }

	int frame_w = cap.get(CAP_PROP_FRAME_WIDTH);
	int frame_h = cap.get(CAP_PROP_FRAME_HEIGHT);
    int frame_fps = cap.get(CAP_PROP_FPS);
    long nFrame = static_cast<long>(cap.get(CAP_PROP_FRAME_COUNT));
    INFO("frame_w = %d, frame_h = %d, frame_fps = %d, Total frames: %ld", frame_w, frame_h, frame_fps, nFrame); 

    VideoWriter writer("demo.mp4", VideoWriter::fourcc('m', 'p', '4', 'v'), frame_fps, Size(frame_w, frame_h));
    if(!writer.isOpened()){
        INFOE("Failed to open video writer for demo.mp4");
        // 尝试使用其他编解码器
        writer.open("demo.mp4", VideoWriter::fourcc('X','V','I','D'), frame_fps, Size(frame_w, frame_h));
        if(!writer.isOpened()){
            INFOE("Failed to open video writer with XVID codec");
            return;
        }else{
            INFO("Video writer opened with XVID codec");
        }
    }else{
        INFO("Video writer opened successfully with mp4v codec");
    }

    // 跟踪类初始化，第二个参数是目标丢失后，track_id 最长保留的帧数，超过该帧数，track_id+1
    ByteTrack::BYTETracker tracker(frame_fps, 30);    // 这里最长保留 30 帧
    // BYTETracker tracker(fps, 30);

    Mat frame;
    int num_frames = 0;
    auto remote_show = create_zmq_remote_show("tcp://0.0.0.0:15556");

	while(true){
        if(!cap.read(frame))   break;
        if (num_frames % 100 == 0){
            INFO("Processing frame %d(Total frames %ld)", num_frames, nFrame);
        }
        
        auto boxes = engine->commit(frame).get();
        auto output_stracks = tracker.update(boxes, 0);

        for(auto& strack : output_stracks){
            uint8_t b, g, r;
            tie(b, g, r) = iLogger::random_color(strack.class_label);
            auto tlwh = strack.tlwh;
            cv::rectangle(frame, cv::Rect(tlwh[0], tlwh[1], tlwh[2], tlwh[3]), cv::Scalar(b, g, r), 2);

            auto name = labels[strack.class_label];
            auto caption = iLogger::format("id:%d %s %.2f", strack.track_id, name, strack.score);
            int width    = cv::getTextSize(caption, 0, 1, 2, nullptr).width + 10;
            cv::rectangle(frame, cv::Point(tlwh[0] - 3, tlwh[1] - 33), cv::Point(tlwh[0] + width, tlwh[1]), cv::Scalar(b, g, r), -1);
            cv::putText(frame, caption, cv::Point(tlwh[0], tlwh[1] - 5), 0, 1, cv::Scalar::all(0), 2, 16);
        }
        num_frames ++;
        remote_show->post(frame);
        // cv::imshow("frame", frame);
        // int key = cv::waitKey(1);
        // if(key == 27)
        //     break;
        writer.write(frame);
    }
    
    char end_signal = 'x';
    remote_show->post(&end_signal, 1);
    INFO("save done.");
    cap.release();
    engine.reset();
    return;    
}

void test_deepsort(){

    auto engine = Yolo::create_infer("best_ckpt.FP16.trtmodel", Yolo::Type::V6, 0);
    const string input_video_path = "video/9.mp4";

    VideoCapture cap(input_video_path);
	if (!cap.isOpened()){
        INFOE("Could not open the video");
		return;
    }

	int frame_w = cap.get(CAP_PROP_FRAME_WIDTH);
	int frame_h = cap.get(CAP_PROP_FRAME_HEIGHT);
    int frame_fps = cap.get(CAP_PROP_FPS);
    long nFrame = static_cast<long>(cap.get(CAP_PROP_FRAME_COUNT));
    INFO("frame_w = %d, frame_h = %d, frame_fps = %d, Total frames: %ld", frame_w, frame_h, frame_fps, nFrame); 

    VideoWriter writer("demo.mp4", VideoWriter::fourcc('m', 'p', '4', 'v'), frame_fps, Size(frame_w, frame_h));    
}

int app_track(){

    test_ByteTracker();
    // test_deepsort();
    return 0;
}

#ifndef airport_HPP
#define airport_HPP

#include <vector>
#include <string>
#include <memory>
#include <future>
#include <opencv2/opencv.hpp>
#include "nn_algorithm_sp100vf.h"
#include "app_yolo/yolo.hpp"
#include "common/object_detector.hpp"

namespace airport{

    ///////////////////////////////////////////////////////////
    struct VideoResult{
        std::vector<sp100vf_alg_target> box_array;       // 检测框
        unsigned int stream_id;                          // 视频流id
    };
    typedef std::vector<VideoResult> VideoResultArray;   // 多路流结果

    ///////////////////////////////////////////////////////////
    struct Config{
        bool visible_enabled;             // 可见光算法初始化使能
        bool ir_enabled;                  // 热成像算法初始化使能
        bool track_enabled;               // 跟踪算法使能        
        float conf_thresh;                // 置信度阈值 
        float conf_yoloe_thresh;                // 置信度阈值
        float nms_thresh;                 // NMS 阈值
        bool log_save_enabled;            // 日志保存使能
        int log_level;                    // 日志等级, 1->Error 2->Warning 3->Info 4->Debug
        std::string log_save_path;        // 日志保存路径, 指定文件夹
        bool blob_enabled;                // blob 提取算法使能
        float iou_match_thresh;           // 跟踪器 iou 匹配阈值
        // 距离匹配参数（可选）：默认仍为 IoU
        std::string match_metric;         // 匹配度量："iou" | "center" | "hybrid"
        std::string dist_norm_mode;       // 距离归一化："bbox" | "none"
        float dist_match_thresh;          // 第一阶段关联的距离阈值（center/hybrid 生效）
        float dist_match_thresh_rtracked; // 第二阶段关联的距离阈值（center/hybrid 生效）
        float dist_match_thresh_unconfirmed; // 第三阶段关联的距离阈值（center/hybrid 生效）
        float hybrid_alpha;               // 混合权重，越小越依赖距离

        int stable_track_thresh;          // 跟踪器稳定阈值
        int blob_crop_width;              // blob 提取算法裁剪图像宽度
        int blob_crop_height;             // blob 提取算法裁剪图像高度
        bool use_v6_e = true;                    // 是否使用v6+yoloe的混合方案
        int frame_rate = 30;                     // 视频帧率
        int track_buffer = 30;                   // 跟踪丢失后保留的帧数

        Config() = default;
    };

    ///////////////////////////////////////////////////////////
    class Monitor{
    public:
        virtual void update_config(const Config& config) = 0;
        // virtual bool bird_track(std::vector<cv::Mat>& images, VideoResultArray& result_array) = 0;
        virtual bool airport_detection(cv::Mat& image, VideoResult& result, bool is_visible) = 0;
    };

    std::shared_ptr<Monitor> create_monitor();

    ///////////////////////////////////////////////////////////
    class TrackProcessor{
    public:
        virtual void update_config(Config& config) = 0;
        // virtual bool process(std::vector<cv::Mat>& images, VideoResultArray& result_array) = 0;
        virtual bool process(cv::Mat& image, VideoResult& result, bool is_visible) = 0;
    };
    std::shared_ptr<TrackProcessor> create_track(Config& config);
    
};  // namespace airport

#endif  // airport_HPP
#ifndef NN_ALGORITHM_SP100VF_H
#define NN_ALGORITHM_SP100VF_H

#include <cstdint>

#ifdef __cplusplus
#include <vector>
#endif

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_NUM_TARGET_IMAGE   (100)     // 单次检测时目标的最大数量

struct sp100vf_alg_pre_cfg
{
    int n_frames_for_alarm     = 3;      // 多帧告警参数，连续 n_frames_for_alarm 帧有目标才上传目标
    float confidence_threshold = 0.75;   // 多帧告警时，至少有一帧置信度大于该阈值
    int log_print              = 1;      // 是否打印日志
    int log_write              = 1;      // 是否保存日志
    int image_width            = 1920;   // 预设图像宽
    int image_high             = 1080;   // 预设图像高
    char model_dir[1024];                // 检测模型存放目录
    char ncnn_model_dir[1024];           // ncnn模型存放目录
    char ncnn_param_dir[1024];           // ncnn参数存放目录
    char log_save_dir[1024];             // 日志文件保存目录
};

struct sp100vf_alg_input
{
    uint8_t* image_date;                 // 图像数据指针的列表
    int camera_id;                       // 图像对应相机ID的列表(奇数近焦，偶数远焦)
    int image_width;          			 // 实际图像宽
    int image_high;            			 // 实际图像高
};

struct sp100vf_alg_target
{
    int64_t target_id;                   // 图像目标ID
    int     camera_id;                   // 图像相机ID
    int     list_id;                     // 索引值  
    //int     class_id;                    // 图像目标ID, 0: human, 1: car, 2: airport, 3: bird 4:UAV 5：空飘物 
    int     class_id;                    // 图像目标ID, 0: human, 1: car, 2: airport, 3: bird 4:UAV  5：战斗机 6：直升机 7：空飘物
    float   bbox[4];                     // 图像目标在时频图上的位置
    float   confidence;                  // 图像目标类别置信度
    int     bird_class_id;               // 鸟类id "Crow","Eagle","Geese","Magpie","Pigeon","Sparrow","Swallow","unknow"
};

struct sp100vf_alg_ver_info
{
    uint32_t program_version;           // 检测算法版本号
    char     program_date[16];          // 检测算法更新日期

    uint32_t model_version;             // 检测模型版本号
    char     model_date[16];            // 检测模型更新日期
};

/**
 * @brief nn_algorithm_init      - 初始化算法.
 * @param  p_pre_cfg             - input, 预设置配置.
 * @return 成功：返回句柄, 失败：nullptr.
*/
void* nn_algorithm_init(sp100vf_alg_pre_cfg *pre_cfg);

/**
 * @brief nn_algorithm_version   - 算法版本信息.
 * @param  p_alg_handle  	     - input, 算法初始化成功后的句柄.
 * @param  p_ver_info  	         - output, 输出算法与模型版本信息.
 * @return 成功：>=0, 失败：<0.
*/
int nn_algorithm_version( sp100vf_alg_ver_info *p_ver_info);

/**
 * @brief nn_algorithm_close   - 关闭算法计算, 释放资源.
 * @param  p_alg_handle  	   - input, 算法初始化成功后的句柄.
 * @return 成功：>=0, 失败：<0.
*/
int nn_algorithm_close(void* handle);


#ifdef __cplusplus
}
#endif

#ifdef __cplusplus
/**
 * @brief nn_algorithm_detect    - 图像目标检测.
 * @param  p_alg_handle  	     - input, 算法初始化成功后的句柄.
 * @param  p_img_input  	     - input, 图像信息输入.
 * @param  p_img_input  	     - output, 图像目标输出列表.
 * @return 成功：>=0, 失败：<0.
*/
std::vector<sp100vf_alg_target> nn_algorithm_detect(void* handle, sp100vf_alg_input *p_img_input);
#endif

#endif // NN_ALGORITHM_SP100VF_H
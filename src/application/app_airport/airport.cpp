#include <mutex>
#include <algorithm>
#include "utils.hpp"
#include "airport.hpp"
#include "app_yolo/yolo.hpp"
#include "app_bytetrack/byte_tracker.hpp"
#include <common/ilogger.hpp>
#include <numeric>

namespace airport{

    using namespace cv;
    using namespace std;

    class MonitorImpl : public Monitor {
    public:
        
        ~MonitorImpl() {}

        ///////////////////////////////////////////////////////////
        virtual bool startup(){
            track_processor_ = create_track(config_);
            if(!track_processor_){
                INFOE("monitor init failed");
                return false;
            }
            return true;
        }

        // virtual bool bird_track(vector<cv::Mat>& images, VideoResultArray& result_array) override{
        //     return track_processor_->process(images, result_array);
        // }

        virtual bool airport_detection(cv::Mat& image, VideoResult& result, bool is_visible) override{
            return track_processor_->process(image, result, is_visible);
        }

        virtual void update_config(const Config& config) override{
            this->config_ = config;
            switch(config_.log_level){
                case 1: iLogger::set_log_level(iLogger::LogLevel::Error); break;
                case 2: iLogger::set_log_level(iLogger::LogLevel::Warning); break;
                case 3: iLogger::set_log_level(iLogger::LogLevel::Info); break;
                case 4: iLogger::set_log_level(iLogger::LogLevel::Debug); break;
                default: iLogger::set_log_level(iLogger::LogLevel::Info); break;
            }
            if(config_.log_save_enabled){
                INFO("log save enabled, save_path = %s", config_.log_save_path.c_str());
                iLogger::set_logger_save_directory(config_.log_save_path);
            }else{
                INFO("log save not enabled, the log will not be saved");
            }

            auto ground_vl_engine  = airport::EngineManager::GetGroundVlEngine();
            auto sky_vl_engine     = airport::EngineManager::GetSkyVlEngine();
            // auto bird_vl_engine    = airport::EngineManager::GetBirdVlEngine();
            auto ground_ir_engine  = airport::EngineManager::GetGroundIrEngine();
            auto sky_ir_engine     = airport::EngineManager::GetSkyIrEngine();
            auto yoloe_engine      = airport::EngineManager::GetYoloeEngine();
            ground_vl_engine->set_params(config_.conf_thresh, config_.nms_thresh);
            sky_vl_engine->set_params(config_.conf_thresh, config_.nms_thresh);
            // bird_vl_engine->set_params(config_.conf_thresh, config_.nms_thresh);
            ground_ir_engine->set_params(config_.conf_thresh, config_.nms_thresh);
            sky_ir_engine->set_params(config_.conf_thresh, config_.nms_thresh);
            yoloe_engine->set_params(config_.conf_yoloe_thresh, config_.nms_thresh);
            track_processor_->update_config(config_);
        }

    private:
        Config config_; 
        shared_ptr<TrackProcessor> track_processor_;
    };

    shared_ptr<Monitor> create_monitor(){
        static shared_ptr<MonitorImpl> instance = nullptr;
        if(!instance){
            instance = make_shared<MonitorImpl>();
            if(!instance->startup()){
                instance.reset();
            }
        }

        return instance;
    }
};
#include "utils.hpp"
#include <fstream>
#include <numeric>
#include <common/json.hpp>
#include <common/ilogger.hpp>

namespace airport{

    using namespace std;

    ///////////////////////////////////////////////////////////
    shared_ptr<Yolo::Infer> EngineManager::GetGroundVlEngine(){
        if(ground_vl_engine_ == nullptr){      // First check without lock for performance
            std::lock_guard<std::mutex> lock(mutex_);
            if(ground_vl_engine_ == nullptr){  // Second check with lock for safety
                ground_vl_engine_ = CreateEngine(ground_vl_engine_file_, Yolo::Type::V6);
            }
        }
        return ground_vl_engine_;
    }

    shared_ptr<Yolo::Infer> EngineManager::GetSkyVlEngine(){
        if(sky_vl_engine_ == nullptr){
            std::lock_guard<std::mutex> lock(mutex_);
            if(sky_vl_engine_ == nullptr){
                sky_vl_engine_ = CreateEngine(sky_vl_engine_file_, Yolo::Type::V5);
            }
        }
        return sky_vl_engine_;
    }

    shared_ptr<Yolo::Infer> EngineManager::GetGroundVlThreadEngine(){
        if(ground_vl_thread_engine_ == nullptr){
            std::lock_guard<std::mutex> lock(mutex);
            if(ground_vl_thread_engine_ == nullptr){
                ground_vl_thread_engine_ = CreateEngine(ground_vl_engine_file_, Yolo::Type::V6);
            }
        }
        return ground_vl_thread_engine_;
    }

    shared_ptr<Yolo::Infer> EngineManager::GetSkyVlThreadEngine(){
        if(sky_vl_thread_engine_ == nullptr){
            std::lock_guard<std::mutex> lock(mutex);
            if(sky_vl_thread_engine_ == nullptr){
                sky_vl_thread_engine_ = CreateEngine(sky_vl_engine_file_, Yolo::Type::V5);
            }
        }
        return sky_vl_thread_engine_;
    }    

    shared_ptr<Yolo::Infer> EngineManager::GetBirdVlEngine(){
        if(bird_vl_engine_ == nullptr){
            std::lock_guard<std::mutex> lock(mutex_);
            if(bird_vl_engine_ == nullptr){
                bird_vl_engine_ = CreateEngine(bird_vl_engine_file_, Yolo::Type::V6);
            }
        }
        return bird_vl_engine_;
    }

    shared_ptr<Yolo::Infer> EngineManager::GetGroundIrEngine(){
        if(ground_ir_engine_ == nullptr){
            std::lock_guard<std::mutex> lock(mutex_);
            if(ground_ir_engine_ == nullptr){
                ground_ir_engine_ = CreateEngine(ground_ir_engine_file_, Yolo::Type::V6);
            }
        }
        return ground_ir_engine_;
    }

    shared_ptr<Yolo::Infer> EngineManager::GetSkyIrEngine(){
        if(sky_ir_engine_ == nullptr){
            std::lock_guard<std::mutex> lock(mutex_);
            if(sky_ir_engine_ == nullptr){
                sky_ir_engine_ = CreateEngine(sky_ir_engine_file_, Yolo::Type::V6);
            }
        }
        return sky_ir_engine_;
    }

    shared_ptr<Yolo::Infer> EngineManager::GetYoloeEngine(){
        if(yoloe_engine_ == nullptr){
            std::lock_guard<std::mutex> lock(mutex_);
            if(yoloe_engine_ == nullptr){
                yoloe_engine_ = CreateEngine(yoloe_engine_file_, Yolo::Type::V8);
            }
        }
        return yoloe_engine_;
    }

    shared_ptr<Yolo::Infer> EngineManager::GetGroundIrThreadEngine(){
        if(ground_ir_thread_engine_ == nullptr){
            std::lock_guard<std::mutex> lock(mutex_);
            if(ground_ir_thread_engine_ == nullptr){
                ground_ir_thread_engine_ = CreateEngine(ground_ir_engine_file_, Yolo::Type::V6);
            }
        }
        return ground_ir_thread_engine_;
    }

    shared_ptr<Yolo::Infer> EngineManager::GetSkyIrThreadEngine(){
        if(sky_ir_thread_engine_ == nullptr){
            std::lock_guard<std::mutex> lock(mutex_);
            if(sky_ir_thread_engine_ == nullptr){
                sky_ir_thread_engine_ = CreateEngine(sky_ir_engine_file_, Yolo::Type::V6);
            }
        }
        return sky_ir_thread_engine_;
    }

    void EngineManager::SetEngineFiles(
        const std::string& ground_vl_engine_file, const std::string& sky_vl_engine_file,
        const std::string& bird_vl_engine_file, const std::string& ground_ir_engine_file,
        const std::string& sky_ir_engine_file, const std::string& yoloe_engine_file){
        std::lock_guard<std::mutex> lock(mutex_);
        ground_vl_engine_file_  = ground_vl_engine_file;
        sky_vl_engine_file_     = sky_vl_engine_file;
        bird_vl_engine_file_    = bird_vl_engine_file;
        ground_ir_engine_file_  = ground_ir_engine_file;
        sky_ir_engine_file_     = sky_ir_engine_file;
        yoloe_engine_file_      = yoloe_engine_file;
        ground_vl_engine_.reset();
        sky_vl_engine_.reset();
        bird_vl_engine_.reset();
        ground_ir_engine_.reset();
        sky_ir_engine_.reset();
        yoloe_engine_.reset();
    }

    void EngineManager::ReleaseEngines(){
        std::lock_guard<std::mutex> lock(mutex_);
        ground_vl_engine_.reset();
        sky_vl_engine_.reset();
        bird_vl_engine_.reset();
        ground_ir_engine_.reset();
        sky_ir_engine_.reset();
        yoloe_engine_.reset();
    }

    shared_ptr<Yolo::Infer> EngineManager::CreateEngine(const string& engine_file){
        auto engine = Yolo::create_infer(engine_file, Yolo::Type::V6, 0);
        if(engine == nullptr){
            INFOE("Failed to create engine from file: %s", engine_file.c_str());
        }
        return engine;
    }

    shared_ptr<Yolo::Infer> EngineManager::CreateEngine(const string& engine_file, Yolo::Type type){
        auto engine = Yolo::create_infer(
            engine_file,                // engine file
            type,                       // yolo type, Yolo::Type::V5 / Yolo::Type::X
            0,                   // gpu id
            0.7f,                      // confidence threshold
            0.45f,                      // nms threshold
            Yolo::NMSMethod::ClassAgnosticGPU,   // NMS method, fast GPU / CPU
            1024,                       // max objects
            false                       // preprocess use multi stream
        );
        // auto engine = Yolo::create_infer(engine_file, type, 0);
        if(engine == nullptr){
            INFOE("Failed to create engine from file: %s", engine_file.c_str());
        }
        INFO("Create engine from file: %s success, type = %s, confidence = %f", engine_file.c_str(), "YOLOE", 0.1);
        return engine;
    }
   
    std::mutex EngineManager::mutex_;
    shared_ptr<Yolo::Infer> EngineManager::ground_vl_engine_ = nullptr;
    shared_ptr<Yolo::Infer> EngineManager::sky_vl_engine_    = nullptr;
    shared_ptr<Yolo::Infer> EngineManager::bird_vl_engine_   = nullptr;
    shared_ptr<Yolo::Infer> EngineManager::ground_ir_engine_ = nullptr;
    shared_ptr<Yolo::Infer> EngineManager::sky_ir_engine_    = nullptr;
    shared_ptr<Yolo::Infer> EngineManager::yoloe_engine_     = nullptr;
    string EngineManager::ground_vl_engine_file_;
    string EngineManager::sky_vl_engine_file_;
    string EngineManager::bird_vl_engine_file_;
    string EngineManager::ground_ir_engine_file_;
    string EngineManager::sky_ir_engine_file_;
    string EngineManager::yoloe_engine_file_;

    shared_ptr<Yolo::Infer> EngineManager::ground_vl_thread_engine_ = nullptr;
    shared_ptr<Yolo::Infer> EngineManager::sky_vl_thread_engine_ = nullptr;
    shared_ptr<Yolo::Infer> EngineManager::ground_ir_thread_engine_ = nullptr;
    shared_ptr<Yolo::Infer> EngineManager::sky_ir_thread_engine_ = nullptr;

    ///////////////////////////////////////////////////////////
    // void print_params(const Parameter& params){
    //     INFO("visible_enabled = %d", params.visible_enabled);
    //     INFO("infrared_enabled = %d", params.ir_enabled);
    //     INFO("track_enabled = %d", params.track_enabled);
    //     INFO("conf_thresh = %.2f", params.conf_thresh);
    //     INFO("nms_thresh = %.2f", params.nms_thresh);
    //     // INFO("blob_enabled = %d", params.blob_enabled);
    // }
    
    void print_config(const Config& config){
        INFO("visible_enabled = %d", config.visible_enabled);
        INFO("infrared_enabled = %d", config.ir_enabled);
        INFO("track_enabled = %d", config.track_enabled);
        INFO("conf_thresh = %.2f", config.conf_thresh);
        INFO("conf_yoloe_thresh = %.2f", config.conf_yoloe_thresh);
        INFO("nms_thresh = %.2f", config.nms_thresh);
        INFO("log_level = %d", config.log_level);
        INFO("log_save_path = %s", config.log_save_path.c_str());
        INFO("log_save_enabled = %d", config.log_save_enabled);
        INFO("blob_enabled = %d", config.blob_enabled);
        INFO("iou_match_thresh = %.2f", config.iou_match_thresh);
        INFO("match_metric = %s", config.match_metric.c_str());
        INFO("dist_norm_mode = %s", config.dist_norm_mode.c_str());
        INFO("dist_match_thresh = %.4f", config.dist_match_thresh);
        INFO("dist_match_thresh_rtracked = %.4f", config.dist_match_thresh_rtracked);
        INFO("dist_match_thresh_unconfirmed = %.4f", config.dist_match_thresh_unconfirmed);
        INFO("hybrid_alpha = %.2f", config.hybrid_alpha);
        INFO("stable_track_thresh = %d", config.stable_track_thresh);
        INFO("blob_crop_width = %d", config.blob_crop_width);
        INFO("blob_crop_height = %d", config.blob_crop_height);
        INFO("use_v6_e = %d", config.use_v6_e);
        INFO("frame_rate = %d", config.frame_rate);
        INFO("track_buffer = %d", config.track_buffer);
    }

    // Config create_config(const Parameter& params){
    //     airport::Config config;
    //     config.visible_enabled = params.visible_enabled;
    //     config.ir_enabled      = params.ir_enabled;
    //     config.track_enabled   = params.track_enabled;
    //     config.conf_thresh     = params.conf_thresh;
    //     config.nms_thresh      = params.nms_thresh;
    //     // config.blob_enabled    = params.blob_enabled;
    //     return config;
    // }

    /*
     * 函数名称: parse_json
     * 输入: const std::string& filename - 配置文件路径; Config& config - 输出配置对象
     * 输出: 无
     * 简介: 从 JSON 配置文件读取算法参数，填充到 Config 结构体
     * 详细流程:
     *   1) 打开并解析 JSON 文件，若失败则记录错误并返回
     *   2) 逐项读取字段（如不存在则采用缺省值）
     *   3) 将读取的值写入 config 对象
     * 编写人: TOT
     */
    void parse_json(const std::string& filename, Config& config){
        std::ifstream ifs(filename);
        if(!ifs.is_open()){
            INFOE("Failed to open %s", filename.c_str());
            return;
        }
        Json::Value value;
        Json::CharReaderBuilder builder;
        JSONCPP_STRING errs;
        if(!Json::parseFromStream(builder, ifs, &value, &errs)){
            INFOE("Failed to parse %s: %s", filename.c_str(), errs.c_str());
            return;
        }

        config.visible_enabled  = value.get("visible_enabled", true).asBool();
        config.ir_enabled       = value.get("ir_enabled", false).asBool();
        config.track_enabled    = value.get("track_enabled", true).asBool();
        config.conf_thresh      = value.get("conf_thresh", 0.25).asFloat();
        config.conf_yoloe_thresh      = value.get("conf_yoloe_thresh", 0.1).asFloat();
        config.nms_thresh       = value.get("nms_thresh", 0.45).asFloat();
        config.log_level        = value.get("log_level", 3).asInt();
        config.log_save_path    = value.get("log_save_path", "algo_log").asString();
        config.log_save_enabled = value.get("log_save_enabled", false).asBool();
        config.blob_enabled     = value.get("blob_enabled", false).asBool();
        config.iou_match_thresh = value.get("iou_match_thresh", 0.55).asFloat();
        config.match_metric      = value.get("match_metric", "iou").asString();
        config.dist_norm_mode    = value.get("dist_norm_mode", "bbox").asString();
        config.dist_match_thresh = value.get("dist_match_thresh", 0.050f).asFloat();
        config.dist_match_thresh_rtracked = value.get("dist_match_thresh_rtracked", 0.040f).asFloat();
        config.dist_match_thresh_unconfirmed = value.get("dist_match_thresh_unconfirmed", 0.060f).asFloat();
        config.hybrid_alpha      = value.get("hybrid_alpha", 0.30f).asFloat();
        config.stable_track_thresh = value.get("stable_track_thresh", 15).asInt();
        config.blob_crop_width  = value.get("blob_crop_width", 500).asInt();
        config.blob_crop_height = value.get("blob_crop_height", 500).asInt();
        config.use_v6_e         = value.get("use_v6_e", false).asBool();
        config.frame_rate       = value.get("frame_rate", 30).asInt();
        config.track_buffer     = value.get("track_buffer", 30).asInt();
    }

    ///////////////////////////////////////////////////////////
    // static bool isValidFrame(const VideoFrame& frame){
    //     if(frame.data_buffer == nullptr){
    //         INFOE("Image data buffer is empty, stream_id = %d", frame.stream_id);
    //         return false;
    //     }

    //     int expected_buffer_size = frame.width * frame.height * 3;
    //     if(frame.data_size != expected_buffer_size){
    //         INFOE("Image data size does not match, stream_id = %d, expected_size = %d, actual_size = %d",
    //               frame.stream_id, expected_buffer_size, frame.data_size);
    //         return false;
    //     }

    //     if(frame.width <= 0 || frame.height <= 0){
    //         INFOE("Width and height must be greater than 0");
    //         return false;
    //     }

    //     return true;
    // }

    // bool load_images(vector<cv::Mat>& images, const VideoFrameArray& frames, const vector<unsigned int>& indexes){
    //     for(int i = 0; i < frames.size; ++i){
    //         const VideoFrame& frame = frames.frames[i];
    //         if(!indexes.empty() && std::find(indexes.begin(), indexes.end(), frame.stream_id) == indexes.end()){
    //             continue;
    //         }
    //         if(!isValidFrame(frame)){
    //             return false;
    //         }

    //         cv::Mat image(frame.height, frame.width, CV_8UC3, frame.data_buffer);
    //         images.emplace_back(std::move(image));
    //     }

    //     return true;
    // }

    // static Box expand_box(const Box& old_box, int stream_id){

    //     int expand_pixels = (stream_id == 0) ? 15 : 10;
    //     int image_width   = (stream_id == 0) ? 1920 : 640;
    //     int image_height  = (stream_id == 0) ? 1080 : 512;
        
    //     int new_x = old_box.x - expand_pixels;
    //     int new_y = old_box.y - expand_pixels;
    //     int new_width  = old_box.width + 2 * expand_pixels;
    //     int new_height = old_box.height + 2 * expand_pixels;

    //     new_x = std::max(0, new_x);
    //     new_y = std::max(0, new_y);
    //     new_width  = std::min(image_width - new_x, new_width);
    //     new_height = std::min(image_height - new_y, new_height);

    //     Box new_box = old_box;
    //     new_box.x = new_x;
    //     new_box.y = new_y;
    //     new_box.width  = new_width;
    //     new_box.height = new_height;

    //     return new_box;
    // }

    // void process_results(const VideoResultArray& result_array, StreamResultArray& results){
    //     for(int i = 0; i < result_array.size(); ++i){
    //         const auto& videoResult    = result_array[i];
    //         auto& streamResult         = results.results[i];
    //         streamResult.stream_id     = videoResult.stream_id;
    //         streamResult.alarm_type    = videoResult.alarm_type;
    //         streamResult.camera_zoom   = videoResult.camera_zoom;
    //         // streamResult.box_size      = videoResult.box_array.size();

    //         // if(videoResult.box_array.size() >= 1){
    //         //     streamResult.box_size = 1;
    //         // }else{
    //         //     streamResult.box_size = 0;
    //         // }

    //         // for(int j = 0; j < videoResult.box_array.size(); ++j){
    //         //     auto& box = videoResult.box_array[j];
    //         //     INFO("stream_id = %d, left = %d, top = %d, width = %d, height = %d, conf = %.2f, label = %d, track_id = %d",
    //         //          streamResult.stream_id, box.x, box.y, box.width, box.height, box.confidence, box.class_label, box.track_id);
    //         //     if(box.track_id == 0)
    //         //         continue;
    //         //     streamResult.box_array[0] = expand_box(box, streamResult.stream_id);                
    //         // }

    //         streamResult.box_size      = std::min((uint8_t)videoResult.box_array.size(), (uint8_t)BOX_MAX);  // 确保不超过最大框数
    //         INFO("stream_id = %d, alarm_type = %d, camera_zoom = %d, box_size = %d", 
    //              videoResult.stream_id, videoResult.alarm_type, videoResult.camera_zoom, streamResult.box_size);
            
    //         for(int j = 0; j < streamResult.box_size; ++j){
    //             auto& box = videoResult.box_array[j];
    //             INFO("left = %d, top = %d, width = %d, height = %d, conf = %.2f, label = %d, track_id = %d",
    //                  box.x, box.y, box.width, box.height, box.confidence, box.class_label, box.track_id);

    //             if(box.track_id == 1){
    //                 streamResult.box_array[j] = expand_box(box, streamResult.stream_id);
    //             }else{
    //                 streamResult.box_array[j] = box;
    //             }
    //         }  
    //     }

    //     results.size = result_array.size();      
    // }

    // void reset_results(const VideoFrameArray& frames, StreamResultArray& results){
    //     for(int i = 0; i < results.size; ++i){
    //         results.results[i].stream_id   = frames.frames[i].stream_id;
    //         results.results[i].alarm_type  = AlarmType::No_Alarm;
    //         results.results[i].box_size    = 0;
    //         results.results[i].camera_zoom = 0;
    //     }
    // }

    // ///////////////////////////////////////////////////////////
    // const char* alarm_name(AlarmType alarm){
    //     switch(alarm){
    //         case AlarmType::No_Alarm: return "No_Alarm";
    //         case AlarmType::Target_Lost: return "Target_Lost";
    //         default: return "Unknown";
    //     }
    // }

    // const TargetType label_name(int label){
    //     switch(label){
    //     case 0: return TargetType::Bird;
    //     default:
    //         return TargetType::Unknown;
    //     }
    // }

    // float iou(const Box& a, const Box& b){
    //     float cleft   = max(a.x, b.x);
    //     float ctop    = max(a.y, b.y);
    //     float cright  = min(a.x + a.width, b.x + b.width);
    //     float cbottom = min(a.y + a.height, b.y + b.height);

    //     float c_area = max(cright - cleft, 0.0f) * max(cbottom - ctop, 0.0f);
    //     if(c_area == 0.0f)
    //         return 0.0f;

    //     float a_area = max(0.0f, static_cast<float>(a.width) * a.height);
    //     float b_area = max(0.0f, static_cast<float>(b.width) * b.height);
    //     return c_area / (a_area + b_area - c_area);
    // }

    float iou(const ObjectDetector::Box& a, const ObjectDetector::Box& b){
        float cleft 	= max(a.left, b.left);
        float ctop 		= max(a.top, b.top);
        float cright 	= min(a.right, b.right);
        float cbottom 	= min(a.bottom, b.bottom);
        
        float c_area = max(cright - cleft, 0.0f) * max(cbottom - ctop, 0.0f);
        if(c_area == 0.0f)
            return 0.0f;
        
        float a_area = max(0.0f, a.right - a.left) * max(0.0f, a.bottom - a.top);
        float b_area = max(0.0f, b.right - b.left) * max(0.0f, b.bottom - b.top);
        return c_area / (a_area + b_area - c_area);
    }

    /**
     * 函数名称: cpu_nms_class_agnostic_airport
     * 输入: boxes - 检测框数组；threshold - IoU 阈值
     * 输出: 进行跨类别抑制后的检测框数组
     * 简介: 在 CPU 上执行不区分类别的 NMS，所有框均参与相互抑制
     * 详细流程:
     *  1) 按 confidence 从高到低排序
     *  2) 依次选择当前最高分框 a
     *  3) 对后续所有框 b 计算 IoU，若 >= 阈值则移除（不判断类别）
     *  编写人: TOT
     */
    ObjectDetector::BoxArray cpu_nms_class_agnostic_airport(ObjectDetector::BoxArray& boxes, float threshold){
        std::sort(boxes.begin(), boxes.end(), [](const ObjectDetector::Box& a, const ObjectDetector::Box& b){
            return a.confidence > b.confidence;
        });

        ObjectDetector::BoxArray output;
        output.reserve(boxes.size());

        std::vector<bool> remove_flags(boxes.size());
        for(size_t i = 0; i < boxes.size(); ++i){
            if(remove_flags[i]) continue;
            const auto& a = boxes[i];
            output.emplace_back(a);
            for(size_t j = i + 1; j < boxes.size(); ++j){
                if(remove_flags[j]) continue;
                const auto& b = boxes[j];
                if(iou(a, b) >= threshold)
                    remove_flags[j] = true;
            }
        }
        return output;
    }

    void sortBoxes(ObjectDetector::BoxArray& boxes) {
        std::sort(boxes.begin(), boxes.end(), [](const ObjectDetector::Box& a, const ObjectDetector::Box& b) {
            if (a.class_label != b.class_label)
                return a.class_label < b.class_label;
            return a.confidence > b.confidence;  // 在 class_label 相同的情况下按 confidence 降序排序
        });
    }

    void constrainBox(ObjectDetector::Box& box, float width, float height){
        box.left   = std::max(0.0f, box.left);
        box.top    = std::max(0.0f, box.top);
        box.right  = std::min(width, box.right);
        box.bottom = std::min(height, box.bottom);
    }

    cv::Rect crop_region(const ObjectDetector::Box& box, const cv::Mat& image, int crop_width, int crop_height){
        int center_x = (int)(std::round(box.center_x()));
        int center_y = (int)(std::round(box.center_y()));

        int left = std::max(center_x - crop_width  / 2, 0);
        int top  = std::max(center_y - crop_height / 2, 0);

        left = std::min(left, image.cols - crop_width);
        top  = std::min(top, image.rows - crop_height);

        if(left + crop_width > image.cols){
            left = image.cols - crop_width;
        }
        if(top + crop_height > image.rows){
            top = image.rows - crop_height;
        }

        left = std::max(left, 0);
        top  = std::max(top, 0);

        return cv::Rect(left, top, crop_width, crop_height);
    }
};
#ifndef UTILS_HPP
#define UTILS_HPP

#include "airport.hpp"
#include "nn_algorithm_sp100vf.h"
#include "app_yolo/yolo.hpp"
#include <common/json.hpp>

namespace airport{

    ///////////////////////////////////////////////////////////
    class EngineManager{
    public:
        EngineManager(const EngineManager&) = delete;
        EngineManager& operator=(const EngineManager&) = delete;

        static std::shared_ptr<Yolo::Infer> GetGroundVlEngine();
        static std::shared_ptr<Yolo::Infer> GetSkyVlEngine();
        static std::shared_ptr<Yolo::Infer> GetBirdVlEngine();
        static std::shared_ptr<Yolo::Infer> GetGroundIrEngine();
        static std::shared_ptr<Yolo::Infer> GetSkyIrEngine();
        static std::shared_ptr<Yolo::Infer> GetYoloeEngine();

        static std::shared_ptr<Yolo::Infer> GetGroundVlThreadEngine();
        static std::shared_ptr<Yolo::Infer> GetSkyVlThreadEngine();
        static std::shared_ptr<Yolo::Infer> GetGroundIrThreadEngine();
        static std::shared_ptr<Yolo::Infer> GetSkyIrThreadEngine();

        static void SetEngineFiles(
            const std::string& ground_vl_engine_file, const std::string& sky_vl_engine_file,
            const std::string& bird_vl_engine_file, const std::string& ground_ir_engine_file,
            const std::string& sky_ir_engine_file, const std::string& yoloe_engine_file);
        static void ReleaseEngines();

    private:
        EngineManager() =  default;
        static std::shared_ptr<Yolo::Infer> CreateEngine(const std::string& engine_file);
        static std::shared_ptr<Yolo::Infer> CreateEngine(const std::string& engine_file, Yolo::Type type);

    private:
        static std::mutex mutex_;
        static std::shared_ptr<Yolo::Infer> ground_vl_engine_;
        static std::shared_ptr<Yolo::Infer> sky_vl_engine_;
        static std::shared_ptr<Yolo::Infer> bird_vl_engine_;
        static std::shared_ptr<Yolo::Infer> ground_ir_engine_;
        static std::shared_ptr<Yolo::Infer> sky_ir_engine_;
        static std::shared_ptr<Yolo::Infer> yoloe_engine_;
        static std::string ground_vl_engine_file_;
        static std::string sky_vl_engine_file_;
        static std::string bird_vl_engine_file_;
        static std::string ground_ir_engine_file_;
        static std::string sky_ir_engine_file_;
        static std::string yoloe_engine_file_;

        static std::shared_ptr<Yolo::Infer> ground_vl_thread_engine_;
        static std::shared_ptr<Yolo::Infer> sky_vl_thread_engine_;
        static std::shared_ptr<Yolo::Infer> ground_ir_thread_engine_;
        static std::shared_ptr<Yolo::Infer> sky_ir_thread_engine_;
    };

    ///////////////////////////////////////////////////////////
    void print_config(const Config& config);
    // void print_params(const Parameter& params);
    // Config create_config(const Parameter& params);
    void parse_json(const std::string& filename, Config& config);
    // void parse_json(const std::string& value, Config& config);

    ///////////////////////////////////////////////////////////
    // void print_result(const VideoResult& result);
    // bool load_images(std::vector<cv::Mat>& images, const VideoFrameArray& frames, const std::vector<unsigned int>& indexes);
    // void process_results(const VideoResultArray& result_array, StreamResultArray& results);
    // void reset_results(const VideoFrameArray& frames, StreamResultArray& results);

    ///////////////////////////////////////////////////////////
    // const char* alarm_name(AlarmType alarm);
    // const TargetType label_name(int label);
    // float iou(const Box& a, const Box& b);
    float iou(const ObjectDetector::Box& a, const ObjectDetector::Box& b);

    // 函数声明: 不区分类别的CPU NMS（机场模块）
    // 输入: boxes - 检测框数组；threshold - IoU 阈值
    // 输出: 进行跨类别抑制后的检测框数组
    ObjectDetector::BoxArray cpu_nms_class_agnostic_airport(ObjectDetector::BoxArray& boxes, float threshold);

    void sortBoxes(ObjectDetector::BoxArray& boxes);
    void constrainBox(ObjectDetector::Box& box, float width, float height);
    cv::Rect crop_region(const ObjectDetector::Box& box, const cv::Mat& image, int crop_width, int crop_height);

};  // namespace airport

#endif  // UTILS_HPP
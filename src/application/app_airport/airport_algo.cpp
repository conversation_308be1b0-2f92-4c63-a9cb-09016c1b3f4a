#include <fstream>
#include <common/json.hpp>
#include <common/ilogger.hpp>
#include "utils.hpp"
#include "airport.hpp"
#include "nn_algorithm_sp100vf.h"
#include <vector>

using namespace std;

enum Result {
    FAILED  = 0,
    SUCCESS = 1
};

static int g_dummy_handle = 0;

///////////////////////////////////////////////////////////
extern "C" int nn_algorithm_version(sp100vf_alg_ver_info* p_ver_info) {

    uint32_t model_version      = 1;
    const char model_date[16]   = "20250810";
    uint32_t program_version    = 1;
    const char program_date[16] = "202508111";

    // model info
    p_ver_info->model_version = model_version;
    strncpy(p_ver_info->model_date, model_date, sizeof(p_ver_info->model_date) - 1);
    p_ver_info->model_date[sizeof(p_ver_info->model_date) - 1] = '\0';

    // program info
    p_ver_info->program_version = program_version;
    strncpy(p_ver_info->program_date, program_date, sizeof(p_ver_info->program_date) - 1);
    p_ver_info->program_date[sizeof(p_ver_info->program_date) - 1] = '\0';

    return Result::SUCCESS;
}

///////////////////////////////////////////////////////////
extern "C" void* nn_algorithm_init(sp100vf_alg_pre_cfg* pre_cfg){

    std::ifstream ifs("config.json");
    if(!ifs.is_open()){
        INFOE("Failed to open config.json");
        return nullptr;
    }

    Json::Value value;
    Json::CharReaderBuilder builder;
    JSONCPP_STRING errs;
    if(!Json::parseFromStream(builder, ifs, &value, &errs)){
        INFOE("Failed to parse config.json: %s", errs.c_str());
        return nullptr;
    }

    const std::vector<std::string> required_keys = {
        "ground_vl_weight_file", "sky_vl_weight_file",
        "bird_vl_weight_file", "ground_ir_engine_file",
        "sky_ir_engine_file", "yoloe_engine_file"
    };
    for(const auto& key : required_keys){
        if(!value.isMember(key)){
            INFOE("Missing necessary configuration item: %s", key.c_str());
            return nullptr;
        }
    }

    const std::string ground_vl_weight_file = value["ground_vl_weight_file"].asString();
    const std::string sky_vl_weight_file    = value["sky_vl_weight_file"].asString();
    const std::string bird_vl_weight_file   = value["bird_vl_weight_file"].asString();
    const std::string ground_ir_engine_file = value["ground_ir_engine_file"].asString();
    const std::string sky_ir_engine_file    = value["sky_ir_engine_file"].asString();
    const std::string yoloe_engine_file     = value["yoloe_engine_file"].asString();

    INFO("ground_vl_weight_file = %s", ground_vl_weight_file.c_str());
    INFO("sky_vl_weight_file = %s", sky_vl_weight_file.c_str());
    INFO("bird_vl_weight_file = %s", bird_vl_weight_file.c_str());
    INFO("ground_ir_engine_file = %s", ground_ir_engine_file.c_str());
    INFO("sky_ir_engine_file_ = %s", sky_ir_engine_file.c_str());
    INFO("yoloe_engine_file = %s", yoloe_engine_file.c_str());

    airport::EngineManager::SetEngineFiles(
        ground_vl_weight_file, sky_vl_weight_file,
        bird_vl_weight_file, ground_ir_engine_file,
        sky_ir_engine_file, yoloe_engine_file
    );

    auto yoloe_engine      = airport::EngineManager::GetYoloeEngine();
    INFO("yoloe_engine = %p", yoloe_engine.get());
    auto ground_vl_engine  = airport::EngineManager::GetGroundVlEngine();
    INFO("ground_vl_engine = %p", ground_vl_engine.get());
    auto sky_vl_engine     = airport::EngineManager::GetSkyVlEngine();
    INFO("sky_vl_engine = %p", sky_vl_engine.get());
    // auto bird_vl_engine    = airport::EngineManager::GetBirdVlEngine();
    auto ground_ir_engine  = airport::EngineManager::GetGroundIrEngine();
    INFO("ground_ir_engine = %p", ground_ir_engine.get());
    auto sky_ir_engine     = airport::EngineManager::GetSkyIrEngine();
    INFO("sky_ir_engine = %p", sky_ir_engine.get());

    // auto ground_vl_thread_engine = airport::EngineManager::GetGroundVlThreadEngine();
    // auto sky_vl_thread_engine    = airport::EngineManager::GetSkyVlThreadEngine();
    // auto ground_ir_thread_engine = airport::EngineManager::GetGroundIrThreadEngine();
    // auto sky_ir_thread_engine    = airport::EngineManager::GetSkyIrThreadEngine();

    if(ground_vl_engine== nullptr || sky_vl_engine == nullptr ||
       ground_ir_engine == nullptr || sky_ir_engine == nullptr ||
       yoloe_engine == nullptr){
        INFOE("Failed to create engine");
        return nullptr;
    }

    // Json parser
    airport::Config config;
    airport::parse_json("config.json", config);
    airport::print_config(config);

    auto monitor = airport::create_monitor();
    if(monitor){
        monitor->update_config(config);
        INFO("algorithm params set success");
    }else{
        INFOE("Monitor instance is not valid, update config failed");
        return nullptr;
    }

    INFO("algorithm init success");
    INFO("===== algorithm version = 1.0.250604.1 =====");

    return static_cast<void*>(&g_dummy_handle);
}

///////////////////////////////////////////////////////////
// extern "C" 
#ifdef __cplusplus
std::vector<sp100vf_alg_target> nn_algorithm_detect(void* handle, sp100vf_alg_input* frame){
    INFO("2025.08.11.10:30 v1.0.0");

    if(frame->image_date == nullptr){
        INFOE("Input frame is empty");
        return {};
    }

    auto monitor = airport::create_monitor();
    if(monitor == nullptr){
        INFOE("Create monitor failed");
        return {};
    }
    
    cv::Mat image(frame->image_high, frame->image_width, CV_8UC3, frame->image_date);
    INFO("image size = %d x %d", frame->image_width, frame->image_high);
    bool is_visible = true;
    if(frame->image_width == 640 && frame->image_high == 512){
        is_visible = false;
    }

    airport::VideoResult result;
    result.box_array.reserve(MAX_NUM_TARGET_IMAGE);
    result.stream_id = frame->camera_id;

    auto t0 = iLogger::timestamp_now_float();
    if(monitor->airport_detection(image, result, is_visible)){
        // airport::process_results(result_array, results);
        auto fee = iLogger::timestamp_now_float() - t0;
        INFO("stream_id = %d, algorithm fee = %.2f ms, fps = %.2f", result.stream_id, fee, 1000.0 / fee);
        INFOD("==========================ariplane detection done==========================");    
        return result.box_array;
    }else{
        // airport::reset_results(frames, results);
        INFOD("==========================airplane detection failed==========================");    
        return {};
    }
}
#endif

///////////////////////////////////////////////////////////
extern "C" int nn_algorithm_close(void* handle){
    airport::EngineManager::ReleaseEngines();
    return Result::SUCCESS;
}
#include <map>
#include <deque>
#include "utils.hpp"
#include "airport.hpp"
#include <common/ilogger.hpp>
#include "app_bytetrack/byte_tracker.hpp"

namespace airport{

    using namespace cv;
    using namespace std;

    struct TrackState{
        int tracked_id = 0;
        bool initial_frame = true;
        // std::set<int> filtered_ids;
        int stable_track_count = 0;
        int max_detected_boxes = 0;
        ObjectDetector::Box last_tracked_box;
    };

    template<typename T, typename... Args>
    std::unique_ptr<T> make_unique(Args&&... args) {
        return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
    }

    class TrackProcessorImpl : public TrackProcessor{
    public:

        ~TrackProcessorImpl() {}
        virtual bool startup(Config& config){

            tracker_vl_ = make_unique<ByteTrack::BYTETracker>(config.frame_rate, config.track_buffer);
            tracker_ir_ = make_unique<ByteTrack::BYTETracker>(config.frame_rate, config.track_buffer);
            // blob_processor_ = simpleblob::create_blob();

            if(!tracker_vl_ || !tracker_ir_){
                INFOE("track processor init failed");
                return false;
            }
            return true;
        }

        virtual void update_config(Config& config){
            config_ = config;
            tracker_vl_->update_config(config_.iou_match_thresh, config.frame_rate, config.track_buffer);
            tracker_ir_->update_config(config_.iou_match_thresh, config.frame_rate, config.track_buffer);
            tracker_vl_->set_match_params(config_.match_metric, config_.dist_norm_mode,
                                          config_.dist_match_thresh, config_.dist_match_thresh_rtracked,
                                          config_.dist_match_thresh_unconfirmed, config_.hybrid_alpha);
            tracker_ir_->set_match_params(config_.match_metric, config_.dist_norm_mode,
                                          config_.dist_match_thresh, config_.dist_match_thresh_rtracked,
                                          config_.dist_match_thresh_unconfirmed, config_.hybrid_alpha);
        }

        virtual bool is_osd_area(ObjectDetector::Box& blob_box, int stream_id){
            const auto& filterRects = filterRectsMap_.at(stream_id);
            int x      = (int)(blob_box.left);
            int y      = (int)(blob_box.top);
            int width  = (int)(blob_box.width());
            int height = (int)(blob_box.height());
            cv::Rect blob_box_rect(x, y, width, height);
            for(const auto& filterRect : filterRects){
                if((blob_box_rect & filterRect).area() > 0){
                    return true;
                }
            }
            return false;
        }

        virtual void detectbirdInROI(ObjectDetector::BoxArray& boxes, ObjectDetector::BoxArray& blob_boxes, cv::Mat& cropped_image,
                                     cv::Rect& crop_roi_region, int stream_id, int bird_count, float iou_thresh
        ){

            int left = crop_roi_region.x;
            int top  = crop_roi_region.y;

            for(auto& blob_box : blob_boxes){
                // airport::constrainBox(blob_box, cropped_image.cols, cropped_image.rows);
                blob_box.left   += left;
                blob_box.top    += top;
                blob_box.right  += left;
                blob_box.bottom += top;

                if(is_osd_area(blob_box, stream_id)){
                    continue;
                }

                bool is_new = false;
                if(bird_count == 0){
                    is_new = true;
                }else{
                    is_new = std::none_of(boxes.begin(), boxes.end(), [&blob_box, &iou_thresh](const ObjectDetector::Box& box){
                        return iou(blob_box, box) > iou_thresh;
                    });
                }
                if(is_new){
                    boxes.emplace_back(std::move(blob_box));
                }
            }
        }

        virtual void updateTrackState(ObjectDetector::BoxArray& boxes, VideoResult& result, int image_center_x, int image_center_y){

            vector<ByteTrack::STrack> stracks{};
            int stream_id     = result.stream_id;
            auto& tracker     = stream_id == 0 ? tracker_vl_ : tracker_ir_;
            TrackState& state = stream_id == 0 ? vl_state_ : ir_state_;
            stracks = tracker->update(boxes, stream_id);

            if(stracks.empty()){
                // 没有关联上, Tracker 没有赋予 ID, 导致有框但 ID 为 0, AlarmType 为 Target_Lost
                // if(result.box_array.size() == 0){
                // }
                // result.alarm_type = AlarmType::Target_Lost;
                if(state.tracked_id != 0){   // 当跟踪目标丢失时清空过滤列表，准备重新选择
                    // state.filtered_ids.clear();
                    state.tracked_id = 0;
                    state.stable_track_count = 0;
                    state.max_detected_boxes = 0;
                }
                return;
            }

            std::sort(stracks.begin(), stracks.end(), [](const ByteTrack::STrack& a, const ByteTrack::STrack& b){
                return a.score > b.score;
            });

            int new_track_id   = -1;
            if(state.tracked_id == 0){   // 如果没有正在跟踪的目标，需要选择一个
                if(state.initial_frame){
                    // 第一帧:选择距离图像中心最近的目标
                    new_track_id = chooseTrackTarget(stracks, state, image_center_x, image_center_y);
                    state.initial_frame = false;
                }else{
                    // 目标丢失:选择距离上一个跟踪目标丢失前最近的新目标
                    int last_box_center_x = (int)(std::round(state.last_tracked_box.center_x()));
                    int last_box_center_y = (int)(std::round(state.last_tracked_box.center_y()));
                    new_track_id = chooseTrackTarget(stracks, state, last_box_center_x, last_box_center_y);
                }
                if(new_track_id != -1){
                    state.tracked_id = new_track_id;
                    INFO("Find new track target, stream_id = %d, tracked_id = %d", stream_id, state.tracked_id);
                    // 添加未被选择为跟踪目标的其它目标到过滤列表
                    // for(auto& strack : stracks){
                    //     if(strack.track_id != state.tracked_id){
                    //         state.filtered_ids.insert(strack.track_id);
                    //     }
                    // }
                }else{
                    // 没有有效的跟踪目标可选，设置告警状态并返回
                    // result.alarm_type = AlarmType::Target_Lost;
                    INFOW("No valid track target found, all potential targets are filtered.");
                    // return;
                }
            }else{  // 有正在跟踪的目标
                // 检查当前跟踪的目标是否仍在跟踪中
                bool target_found = false;
                for(auto& strack : stracks){
                    if(strack.track_id == state.tracked_id){
                        target_found = true;
                        state.last_tracked_box = ObjectDetector::Box(strack.tlbr[0], strack.tlbr[1], strack.tlbr[2], strack.tlbr[3], strack.score, 0);
                        // state.stable_track_count++;
                    }
                    // 0->不存在过滤列表中 1->存在过滤列表中
                    // if(strack.track_id == state.tracked_id && state.filtered_ids.count(strack.track_id) == 0){
                    //     target_found = true;
                    //     // 更新
                    //     state.last_tracked_box = ObjectDetector::Box(strack.tlbr[0], strack.tlbr[1], strack.tlbr[2], strack.tlbr[3], strack.score, 0);
                    // }else if((state.filtered_ids.find(strack.track_id) == state.filtered_ids.end()) && boxes.size() > 1){
                    //     // 新出现的目标也加入过滤列表(数量大于2)，旧的目标
                    //     state.filtered_ids.insert(strack.track_id);
                    // }
                }
                if(!target_found){
                    // 当前跟踪的目标不在跟踪中，重新选择跟踪目标
                    int last_box_center_x = (int)(std::round(state.last_tracked_box.center_x()));
                    int last_box_center_y = (int)(std::round(state.last_tracked_box.center_y()));
                    int retrack_id = chooseTrackTarget(stracks, state, last_box_center_x, last_box_center_y);
                    for(auto& strack : stracks){
                        if(strack.track_id == retrack_id){
                            state.tracked_id = retrack_id;
                            state.last_tracked_box = ObjectDetector::Box(strack.tlbr[0], strack.tlbr[1], strack.tlbr[2], strack.tlbr[3], strack.score, 0);
                        }
                    }

                    // result.alarm_type = AlarmType::Target_Lost;
                    // state.tracked_id = 0;
                    // state.filtered_ids.clear();
                    // return;
                }
            }

            // 更新目标信息到结果
            for(int i = 0; i < stracks.size(); ++i){
                auto& strack          = stracks[i];
                auto& tlbr            = strack.tlbr;
                auto& result_box      = result.box_array[i];
                // result_box.x          = std::max((int)(tlbr[0]), 0);
                // result_box.y          = std::max((int)(tlbr[1]), 0);
                // result_box.width      = (int)(tlbr[2] - tlbr[0]);
                // result_box.height     = (int)(tlbr[3] - tlbr[1]);
                // result_box.confidence = strack.score;
                // if(strack.track_id == state.tracked_id){
                //     result_box.track_id = 1;  // 固定返回结果中的跟踪目标 id 为 1
                //     state.stable_track_count++;
                // }
            }

                // 回写 ByteTrack 轨迹ID到本帧检测框，并维护类别历史
                {
                    auto& hist_map = (result.stream_id == 0) ? class_hist_vl_ : class_hist_ir_;

                    for(auto& s : stracks){
                        // 以IoU最大匹配的方式，将轨迹与boxes关联
                        ObjectDetector::Box tb(s.tlbr[0], s.tlbr[1], s.tlbr[2], s.tlbr[3], s.score, s.class_label);
                        int best_index = -1; float best_iou = 0.0f;
                        for(int i = 0; i < (int)boxes.size(); ++i){
                            float ov = iou(tb, boxes[i]);
                            if(ov > best_iou){ best_iou = ov; best_index = i; }
                        }
                        if(best_index >= 0 && best_iou > 0.10f){
                            boxes[best_index].track_id = s.track_id;
                        }

                        // 维护每条轨迹的最近10帧类别历史
                        auto& dq = hist_map[s.track_id];
                        dq.push_back(s.class_label);
                        if((int)dq.size() > kClassSmoothWindow) dq.pop_front();
                    }
                }

        }

        virtual void detectTrack(Mat& image, shared_ptr<Yolo::Infer>& engine, VideoResult& result){
            ObjectDetector::BoxArray boxes;
            if(config_.blob_enabled){
                // blob_processor_->process(image, result.stream_id, boxes, false);
                INFO("Blob extraction detect %d targets, stream_id = %d", boxes.size(), result.stream_id);
            }else{
                boxes = engine->commit(image).get();
                //  0: human, 1: car, 2: airport, 3: bird 4:UAV  5：战斗机 6：直升机 7：空飘物
                // {"fly", "fly", "fly", "UAV", "bird", "UAV", "UAV", "fly"};
                for(auto& b : boxes){
                    if(b.class_label == 4){
                        b.class_label = 3;
                    }
                    else if (b.class_label == 3 || b.class_label == 5 || b.class_label == 6) {
                        b.class_label = 4;
                    }
                    else {
                        b.class_label = 7;
                    }
                }

                INFO("stream_id = %d, boxes size = %d", result.stream_id, boxes.size());
            }

            // auto boxes = engine->commit(image).get();
            // // ObjectDetector::BoxArray boxes;
            // INFO("stream_id = %d, boxes size = %d", result.stream_id, boxes.size());

            // if(boxes.empty() && config_.blob_enabled){
            //     blob_processor_->process(image, result.stream_id, boxes, false);
            //     INFO("Blob extraction detect %d targets, stream_id = %d", boxes.size(), result.stream_id);
            // }

            auto stream_id = result.stream_id;
            auto& state    = stream_id == 0 ? vl_state_ : ir_state_;
            if(boxes.size() < state.max_detected_boxes){
                if(state.stable_track_count > config_.stable_track_thresh && !config_.blob_enabled){
                    INFO("stream_id = %d, possible miss detection detected, invoking blob extraction.", stream_id);
                    auto crop = airport::crop_region(state.last_tracked_box, image, config_.blob_crop_width, config_.blob_crop_height);
                    auto cropped_image = image(crop).clone();
                    // cv::imwrite("cropped_image.jpg", cropped_image);
                    ObjectDetector::BoxArray blob_boxes;
                    // blob_processor_->process(cropped_image, stream_id, blob_boxes, true, false);
                    int bird_count = boxes.size();
                    this->detectbirdInROI(boxes, blob_boxes, cropped_image, crop, stream_id, bird_count, 0.45);
                    INFO("stream_id = %d, after blob extraction boxes size = %d", stream_id, boxes.size());
                }
            }else{
                state.max_detected_boxes = boxes.size();
            }

            airport::sortBoxes(boxes);
            processResult(boxes, image, result);
        }

        // 函数名称: detectionVl
        // 输入: cv::Mat& image, VideoResult& result
        // 输出: void
        // 简介: 可见光双模型(ground_vl, sky_vl)检测并拼装结果
        // 详细流程: 1) 获取两个可见光引擎 2) 分别推理 3) 组装 person/vehicle 与 airplane 类别 4) 写入 result
        // 编写人: TOT
        virtual void detectionVl(cv::Mat& image, VideoResult& result){
            auto ground_vl_engine = airport::EngineManager::GetGroundVlEngine();
            auto sky_vl_engine    = airport::EngineManager::GetSkyVlEngine();

            auto t0_ground = iLogger::timestamp_now_float();
            auto boxes_ground = ground_vl_engine->commit(image).get();
            auto fee_ground = iLogger::timestamp_now_float() - t0_ground;
            INFO("algorithm fee = %.2f ms, fps = %.2f", fee_ground, 1000.0 / fee_ground);

            auto t0_sky = iLogger::timestamp_now_float();
            auto boxes_sky    = sky_vl_engine->commit(image).get();
            auto fee_sky = iLogger::timestamp_now_float() - t0_sky;
            INFO("algorithm fee = %.2f ms, fps = %.2f", fee_sky, 1000.0 / fee_sky);

            INFO("boxes_ground size = %d, boxes_sky size = %d", boxes_ground.size(), boxes_sky.size());

            // process person、car and bird classes
            // 0->person, 1->car, 2->catdog, 3->bird
            for(auto& box : boxes_ground){
                if(box.class_label == 2)
                    continue;
                sp100vf_alg_target alg_target;
                alg_target.target_id  = 0;
                alg_target.camera_id  = result.stream_id;
                alg_target.list_id    = 0;
                alg_target.class_id   = box.class_label;
                alg_target.bird_class_id = 7;
                alg_target.bbox[0]    = box.left;
                alg_target.bbox[1]    = box.top;
                alg_target.bbox[2]    = box.right;
                alg_target.bbox[3]    = box.bottom;
                alg_target.confidence = box.confidence;
                result.box_array.emplace_back(alg_target);
                INFO("gound engine -> stream_id = %d, left = %.2f, top = %.2f, right = %.2f, bottom = %.2f, conf = %.2f, label = %d",
                     result.stream_id, box.left, box.top, box.right, box.bottom, box.confidence, box.class_label);
            }

            // process airplane classes
            for(auto& box : boxes_sky){
                // 0->airplane, 1->landing gear, 2->drone, 3->bird, 4->balloon
                if(box.class_label != 0)
                    continue;
                sp100vf_alg_target alg_target;
                alg_target.target_id  = 0;
                alg_target.camera_id  = result.stream_id;
                alg_target.list_id    = 0;
                alg_target.class_id   = box.class_label + 2;
                alg_target.bird_class_id = 7;
                alg_target.bbox[0]    = box.left;
                alg_target.bbox[1]    = box.top;
                alg_target.bbox[2]    = box.right;
                alg_target.bbox[3]    = box.bottom;
                alg_target.confidence = box.confidence;
                result.box_array.emplace_back(alg_target);
                INFO("sky engine -> stream_id = %d, left = %.2f, top = %.2f, right = %.2f, bottom = %.2f, conf = %.2f, label = %d",
                     result.stream_id, box.left, box.top, box.right, box.bottom, box.confidence, alg_target.class_id);
            }
        }

        // 函数名称: detectionVLYoloE
        // 输入: cv::Mat& image, VideoResult& result
        // 输出: void
        // 简介: 使用单个yoloe可见光模型完成目标检测并写入结果
        // 详细流程: 1) 获取yoloe引擎 2) 推理得到boxes 3) 逐个转换为sp100vf_alg_target并填充result
        // 编写人: TOT
        void detectionVLYoloE(cv::Mat& image, VideoResult& result){
            auto yoloe_engine = airport::EngineManager::GetYoloeEngine();
            auto t0 = iLogger::timestamp_now_float();
            auto boxes = yoloe_engine->commit(image).get();
            auto fee = iLogger::timestamp_now_float() - t0;
            INFO("yoloe algorithm fee = %.2f ms, fps = %.2f", fee, 1000.0 / fee);
            INFO("yoloe boxes size = %d", boxes.size());
            //  0: human, 1: car, 2: airport, 3: bird 4:UAV  5：战斗机 6：直升机 7：空飘物
            // {"fly", "fly", "fly", "UAV", "bird", "UAV", "UAV", "fly"};
            for(auto& b : boxes){
                if(b.class_label == 4){
                    b.class_label = 3;
                }
                else if (b.class_label == 3 || b.class_label == 5 || b.class_label == 6) {
                    b.class_label = 4;
                }
                else {
                    b.class_label = 7;
                }
            }
            for(auto& box : boxes){
                sp100vf_alg_target alg_target;
                alg_target.target_id  = 0;
                alg_target.camera_id  = result.stream_id;
                alg_target.list_id    = 0;
                alg_target.class_id   = box.class_label;
                alg_target.bird_class_id = 7;
                alg_target.bbox[0]    = box.left;
                alg_target.bbox[1]    = box.top;
                alg_target.bbox[2]    = box.right;
                alg_target.bbox[3]    = box.bottom;
                alg_target.confidence = box.confidence;
                result.box_array.emplace_back(alg_target);
                INFO("yoloe -> stream_id = %d, left = %.2f, top = %.2f, right = %.2f, bottom = %.2f, conf = %.2f, label = %d",
                     result.stream_id, box.left, box.top, box.right, box.bottom, box.confidence, alg_target.class_id);
            }
        }

        void detectionVlandYoloE(cv::Mat& image, VideoResult& result){
            // 采用双模型推理
            // 1) 可见光模型提供无人机和鸟类两个类别
            // 2）yoloe模型提供飞飘物一个类别
            auto sky_vl_engine = airport::EngineManager::GetSkyVlEngine();
            auto yoloe_vl_engine    = airport::EngineManager::GetYoloeEngine();

            auto t0_ground = iLogger::timestamp_now_float();
            auto boxes_vl = sky_vl_engine->commit(image).get();
            auto fee_ground = iLogger::timestamp_now_float() - t0_ground;
            INFO("algorithm fee vl = %.2f ms, fps = %.2f", fee_ground, 1000.0 / fee_ground);

            auto t0_sky = iLogger::timestamp_now_float();
            auto boxes_yoloe    = yoloe_vl_engine->commit(image).get();
            auto fee_sky = iLogger::timestamp_now_float() - t0_sky;
            INFO("algorithm fee yoloe = %.2f ms, fps = %.2f", fee_sky, 1000.0 / fee_sky);

            INFO("vl size = %d, yoloe size = %d", boxes_vl.size(), boxes_yoloe.size());

            ObjectDetector::BoxArray boxes = postprocess(boxes_vl, boxes_yoloe);

            for(auto& box : boxes){
                INFO("drone_bird engine -> left = %.2f, top = %.2f, right = %.2f, bottom = %.2f, conf = %.2f, label = %d",
                     box.left, box.top, box.right, box.bottom, box.confidence, box.class_label);
                sp100vf_alg_target alg_target;
                alg_target.target_id  = 0;
                alg_target.camera_id  = result.stream_id;
                alg_target.list_id    = 0;
                alg_target.class_id   = box.class_label;
                alg_target.bird_class_id = 7;
                alg_target.bbox[0]    = box.left;
                alg_target.bbox[1]    = box.top;
                alg_target.bbox[2]    = box.right;
                alg_target.bbox[3]    = box.bottom;
                alg_target.confidence = box.confidence;
                result.box_array.emplace_back(alg_target);
            }

        }

        // 函数名称: detectTrackVlandYoloE
        // 输入: cv::Mat& image, VideoResult& result
        // 输出: void
        // 简介: 使用可见光双模型(sky_vl + yoloe)进行检测并复用跟踪骨架，输出与detectTrack一致
        // 详细流程:
        //   1) 获取 sky_vl 与 yoloe 引擎，分别对同一帧图像推理
        //   2) 合并两路检测框：sky_vl 原样写入；yoloe 仅保留飞飘物相关类别并映射为统一类别ID
        //   3) 复用 detectTrack 的漏检兜底逻辑（稳定跟踪后检测数下降时进行 ROI blob 检查）
        //   4) 调用 processResult 进入统一的跟踪与输出流程（包含主目标标记与类别平滑）
        void detectTrackVlandYoloE(cv::Mat& image, VideoResult& result){

            // 1) 双模型推理
            auto sky_vl_engine   = airport::EngineManager::GetSkyVlEngine();
            auto yoloe_vl_engine = airport::EngineManager::GetYoloeEngine();

            auto t0_vl = iLogger::timestamp_now_float();
            auto boxes_vl = sky_vl_engine->commit(image).get();
            auto fee_vl = iLogger::timestamp_now_float() - t0_vl;
            INFO("algorithm fee vl = %.2f ms, fps = %.2f", fee_vl, 1000.0 / fee_vl);

            auto t0_e = iLogger::timestamp_now_float();
            auto boxes_yoloe = yoloe_vl_engine->commit(image).get();
            auto fee_e = iLogger::timestamp_now_float() - t0_e;
            INFO("algorithm fee yoloe = %.2f ms, fps = %.2f", fee_e, 1000.0 / fee_e);

            ObjectDetector::BoxArray boxes = postprocess(boxes_vl, boxes_yoloe);
             
            // 3) 复用 detectTrack 的漏检兜底逻辑
            auto stream_id = result.stream_id;
            auto& state    = stream_id == 0 ? vl_state_ : ir_state_;
            if(boxes.size() < state.max_detected_boxes){
                if(state.stable_track_count > config_.stable_track_thresh && !config_.blob_enabled){
                    INFO("stream_id = %d, possible miss detection detected, invoking blob extraction.", stream_id);
                    auto crop = airport::crop_region(state.last_tracked_box, image, config_.blob_crop_width, config_.blob_crop_height);
                    auto cropped_image = image(crop).clone();
                    ObjectDetector::BoxArray blob_boxes;
                    int bird_count = boxes.size();
                    this->detectbirdInROI(boxes, blob_boxes, cropped_image, crop, stream_id, bird_count, 0.45);
                    INFO("stream_id = %d, after blob extraction boxes size = %d", stream_id, boxes.size());
                }
            }else{
                state.max_detected_boxes = boxes.size();
            }

            // 4) 统一排序与结果输出
            airport::sortBoxes(boxes);
            processResult(boxes, image, result);
        }


        virtual void detectionIr(cv::Mat& image, VideoResult& result){

            // shared_ptr<Yolo::Infer> ground_ir_engine;
            // shared_ptr<Yolo::Infer> sky_ir_engine;

            // if(result.stream_id == 1){
            //     ground_ir_engine = airport::EngineManager::GetGroundIrEngine();
            //     sky_ir_engine    = airport::EngineManager::GetSkyIrEngine();
            // }else{
            //     ground_ir_engine = airport::EngineManager::GetGroundIrThreadEngine();
            //     sky_ir_engine    = airport::EngineManager::GetSkyIrThreadEngine();
            // }

            auto ground_ir_engine = airport::EngineManager::GetGroundIrEngine();
            auto sky_ir_engine    = airport::EngineManager::GetSkyIrEngine();

            auto boxes_ground = ground_ir_engine->commit(image).get();
            auto boxes_sky    = sky_ir_engine->commit(image).get();

            INFO("boxes_ground size = %d, boxes_sky size = %d", boxes_ground.size(), boxes_sky.size());

            // process person and car
            for(auto& box : boxes_ground){
                // 0->person, 1->car
                sp100vf_alg_target alg_target;
                alg_target.target_id  = 0;
                alg_target.camera_id  = result.stream_id;
                alg_target.list_id    = 0;
                alg_target.class_id   = box.class_label;
                alg_target.bird_class_id = 7;
                alg_target.bbox[0]    = box.left;
                alg_target.bbox[1]    = box.top;
                alg_target.bbox[2]    = box.right;
                alg_target.bbox[3]    = box.bottom;
                alg_target.confidence = box.confidence;
                result.box_array.emplace_back(alg_target);
                INFO("gound engine -> stream_id = %d, left = %.2f, top = %.2f, right = %.2f, bottom = %.2f, conf = %.2f, label = %d",
                     result.stream_id, box.left, box.top, box.right, box.bottom, box.confidence, box.class_label);
            }

            // process airplane
            for(auto& box : boxes_sky){
                // 0->airplane, 1->landing gear, 2->drone, 3->bird, 4->balloon
                if(box.class_label != 0)
                    continue;

                sp100vf_alg_target alg_target;
                alg_target.target_id  = 0;
                alg_target.camera_id  = result.stream_id;
                alg_target.list_id    = 0;
                alg_target.class_id   = box.class_label + 2;
                alg_target.bird_class_id = 7;
                alg_target.bbox[0]    = box.left;
                alg_target.bbox[1]    = box.top;
                alg_target.bbox[2]    = box.right;
                alg_target.bbox[3]    = box.bottom;
                alg_target.confidence = box.confidence;
                result.box_array.emplace_back(alg_target);
                INFO("sky engine -> stream_id = %d, left = %.2f, top = %.2f, right = %.2f, bottom = %.2f, conf = %.2f, label = %d",
                     result.stream_id, box.left, box.top, box.right, box.bottom, box.confidence, alg_target.class_id);
            }
        }

        virtual bool process(cv::Mat& image, VideoResult& result, bool is_visible) override{

            if(!config_.visible_enabled && !config_.ir_enabled){
                INFOE("Both visible mode and infrared mode are not enabled when aircraft track");
                return false;
            }

            // if((config_.visible_enabled && images[0].empty()) || (config_.ir_enabled && images[1].empty())){
            //     INFOE("Image is empty when aircraft track");
            //     return false;
            // }

            // auto engine_vl = airport::EngineManager::GetVlEngine();
            // auto engine_ir = airport::EngineManager::GetIrEngine();

            // 是否采用跟踪模式
            if(config_.track_enabled){
                // 跟踪模式：复用 detectTrack/processResult 骨架
                if(config_.visible_enabled && is_visible == true){
                    // 采用yoloe和yolov6双模型的方案
                    if(config_.use_v6_e){
                        INFO("detectTrackVlandYoloE");
                        detectTrackVlandYoloE(image, result);
                    }else{
                        INFO("detectTrack");
                        auto yoloe_engine = airport::EngineManager::GetYoloeEngine();
                        detectTrack(image, yoloe_engine, result);
                    }
                }
                if(config_.ir_enabled && is_visible == false){
                    // 红外侧为简化实现，选用 sky_ir 引擎进入统一的 detectTrack 流程
                    auto sky_ir_engine = airport::EngineManager::GetSkyIrEngine();
                    detectTrack(image, sky_ir_engine, result);
                }
            }else{
                // 非跟踪模式
                if(config_.visible_enabled && is_visible == true){
                    // 采用yoloe和yolov6双模型的方案
                    if(config_.use_v6_e){
                        INFO("detectionVlandYoloE");
                        detectionVlandYoloE(image, result);
                    }else{
                        INFO("detectionVLYoloE");
                        detectionVLYoloE(image, result); // 采用yoloe单个模型实现
                    }
                }

                if(config_.ir_enabled && is_visible == false){
                    detectionIr(image, result);
                }
            }
            return true;
        }

        ObjectDetector::BoxArray postprocess(ObjectDetector::BoxArray &boxes_vl, ObjectDetector::BoxArray &boxes_yoloe)
        {
            // 打印两个结果
            for (auto &box : boxes_vl)
            {
                INFO("vl -> left = %.2f, top = %.2f, right = %.2f, bottom = %.2f, conf = %.2f, label = %d",
                     box.left, box.top, box.right, box.bottom, box.confidence, box.class_label);
            }
            for (auto &box : boxes_yoloe)
            {
                INFO("yoloe -> left = %.2f, top = %.2f, right = %.2f, bottom = %.2f, conf = %.2f, label = %d",
                     box.left, box.top, box.right, box.bottom, box.confidence, box.class_label);
            }
            ObjectDetector::BoxArray boxes;
            // process person、car and bird classes
            // 0: human, 1: car, 2: airport, 3: bird 4:UAV  5：战斗机 6：直升机 7：空飘物
            // 0->drone(4), 1->bird(3)
            for (auto &box : boxes_vl)
            {
                // drone
                if (box.class_label == 0)
                    box.class_label = 4;
                // bird
                else if (box.class_label == 1)
                    box.class_label = 3;
            }
    
            const int AFO_index[] = {0, 1, 2};  // 输出7
            const int bird_index[] = {4, 7};  // 输出3
            const int UAV_index[] = {3, 5, 6};  // 输出4

            // 如果检测到鸟类或者无人机，则将其置信度增加0.6
            for (auto &box : boxes_yoloe)
            {
                // 临时增加
                if (std::find(std::begin(bird_index), std::end(bird_index), box.class_label) != std::end(bird_index))
                {
                    box.confidence += 0.6;
                    box.class_label = 3;
                    if (box.confidence > 1.0)
                        box.confidence = 1.0;
                }
                // 临时增加
                else if (std::find(std::begin(UAV_index), std::end(UAV_index), box.class_label) != std::end(UAV_index))
                {
                    box.confidence += 0.6;
                    box.class_label = 4;
                    if (box.confidence > 1.0)
                        box.confidence = 1.0;
                }
                else if (std::find(std::begin(AFO_index), std::end(AFO_index), box.class_label) != std::end(AFO_index))
                {
                    box.class_label = 7;
                }
            }
            // 进行无类别的nms后会优先保留鸟类和无人机
            boxes_yoloe = cpu_nms_class_agnostic_airport(boxes_yoloe, 0.5);
            // 增加剩余的AFO的置信度
            for (auto &box : boxes_yoloe)
            {

                if (box.class_label == 3){
                    box.confidence -= 0.6;
                }
                else if (box.class_label == 4){
                    box.confidence -= 0.6;
                }
                else if (box.class_label == 7)
                {
                    box.confidence += 0.8;
                    if (box.confidence > 1.0)
                        box.confidence = 1.0;
                }
            }

            // "fly", "fly", "fly", "UAV", "bird", "UAV", "UAV", "fly"
            // names = ["balloon", "hot air balloon","kite","powered paraglider", "bird", "drone", "UAV","fly"]
            // 头文件类别 0: human, 1: car, 2: airport, 3: bird 4:UAV  5：战斗机 6：直升机 7：空飘物



            // 直接合并boxes_vl和boxes_yoloe
            for (auto &box : boxes_vl)
            {
                boxes.emplace_back(box);
            }

            // 直接合并boxes_vl和boxes_yoloe
            for (auto &box : boxes_yoloe)
            {
                boxes.emplace_back(box);
            }

            // 进行无类别nms
            boxes = cpu_nms_class_agnostic_airport(boxes, 0.5);
            return boxes;
        }

    private:

        void processStream(Mat& image, shared_ptr<Yolo::Infer>& engine, VideoResultArray& result_array, int stream_id){
            VideoResult result;
            // result.camera_zoom = 0;
            // result.stream_id   = stream_id;
            // result.alarm_type  = AlarmType::No_Alarm;
            detectTrack(image, engine, result);

            result_array.emplace_back(result);
        }

        void processResult(ObjectDetector::BoxArray& boxes, const Mat& image, VideoResult& result){
            // 统一将检测框写入 result；若启用跟踪则额外标记主目标(target_id=1)并进行类别多数票平滑
            result.box_array.clear();
            result.box_array.reserve(boxes.size());

            int tracked_index = -1;
            if(config_.track_enabled){
                int image_center_x = image.cols / 2;
                int image_center_y = image.rows / 2;
                updateTrackState(boxes, result, image_center_x, image_center_y);

                // 基于 last_tracked_box 在当前 boxes 中寻找 IoU 最大者作为主目标
                const TrackState& state = (result.stream_id == 0) ? vl_state_ : ir_state_;
                if(state.tracked_id != 0 && !boxes.empty()){
                    float best_iou = 0.0f;
                    for(int i = 0; i < (int)boxes.size(); ++i){
                        auto tmp = iou(state.last_tracked_box, boxes[i]);
                        if(tmp > best_iou){
                            best_iou = tmp;
                            tracked_index = i;
                        }
                    }
                }
            }

            for(int i = 0; i < (int)boxes.size(); ++i){
                auto box = boxes[i];
                airport::constrainBox(box, image.cols, image.rows);

                // 类别多数票平滑：若存在轨迹ID且有历史，则用历史多数类作为输出类别
                int final_class = box.class_label;
                if(config_.track_enabled && box.track_id > 0){
                    auto& hist_map = (result.stream_id == 0) ? class_hist_vl_ : class_hist_ir_;
                    auto it = hist_map.find(box.track_id);
                    if(it != hist_map.end() && !it->second.empty()){
                        final_class = majority_class(it->second);
                    }
                }

                sp100vf_alg_target alg_target;
                alg_target.target_id  = (i == tracked_index) ? 1 : 0;  // 主跟踪目标标记为1，其余为0
                alg_target.camera_id  = result.stream_id;
                alg_target.list_id    = box.track_id;   // 承载轨迹ID
                alg_target.class_id   = final_class;
                alg_target.bird_class_id = 7;
                alg_target.bbox[0]    = box.left;
                alg_target.bbox[1]    = box.top;
                alg_target.bbox[2]    = box.right;
                alg_target.bbox[3]    = box.bottom;
                alg_target.confidence = box.confidence;
                result.box_array.emplace_back(alg_target);
            }
        }

    private:

        int chooseTrackTarget(const vector<ByteTrack::STrack>& stracks, TrackState& state, int center_x, int center_y){
            int closest_track_id = -1;
            float min_distance = std::numeric_limits<float>::max();
            for(const auto& strack : stracks){
                // if(!state.initial_frame && state.filtered_ids.count(strack.track_id)){
                //     continue;
                // }

                int box_center_x = (strack.tlbr[0] + strack.tlbr[2]) / 2;
                int box_center_y = (strack.tlbr[1] + strack.tlbr[3]) / 2;
                float distance = std::sqrt(std::pow(box_center_x - center_x, 2) + std::pow(box_center_y - center_y, 2));
                if(distance < min_distance){
                    min_distance = distance;
                    closest_track_id  = strack.track_id;
                    // [0] -> left; [1] -> top; [2] -> right; [3] -> bottom
                    state.last_tracked_box = ObjectDetector::Box(strack.tlbr[0], strack.tlbr[1], strack.tlbr[2], strack.tlbr[3], strack.score, 0);
                }
            }
            return closest_track_id;
        }

    private:
        Config config_;
        TrackState vl_state_, ir_state_;
        std::unique_ptr<ByteTrack::BYTETracker> tracker_vl_, tracker_ir_;
        const std::map<int, vector<cv::Rect>> filterRectsMap_ =
        {
            {0, {  // 可见光图像 1920x1080
                cv::Rect(1, 1, 593-1, 55-1),            // (1,1,593,55)
                cv::Rect(33, 987, 575-33, 1080-987)     // (33,987,575,1080)
            }},
            {1, {  // 红外图像 640x512
                cv::Rect(16, 29, 227-16, 49-29),        // (16,29,227,49)
                cv::Rect(462, 447, 537-462, 464-447)    // (462,447,537,464)
            }}
        };

        // 最近10帧类别平滑窗口大小
        static constexpr int kClassSmoothWindow = 100;
        // 可见光/红外分别维护的类别历史缓存
        std::map<int, std::deque<int>> class_hist_vl_;
        std::map<int, std::deque<int>> class_hist_ir_;

        // 统计最近窗口内出现次数最多的类别
        static int majority_class(const std::deque<int>& q){
            std::map<int,int> cnt;
            for(int c : q) ++cnt[c];
            int best = -1, num = -1;
            for(const auto& kv : cnt){
                if(kv.second > num){ num = kv.second; best = kv.first; }
            }
            return best;
        }

        // 统计最近窗口内出现次数最多的类别
        static int majority_class_(const std::deque<int>& q){
            std::map<int,int> cnt;
            for(int c : q) ++cnt[c];
            int best = -1, num = -1;
            for(const auto& kv : cnt){
                if(kv.second > num){ num = kv.second; best = kv.first; }
            }
            return best;
        }

    };

    shared_ptr<TrackProcessor> create_track(Config& config){
        static shared_ptr<TrackProcessorImpl> instance = nullptr;
        if(!instance){
            instance = make_shared<TrackProcessorImpl>();
            if(!instance->startup(config)){
                instance.reset();
            }
        }
        return instance;
    }

};
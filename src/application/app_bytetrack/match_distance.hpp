#ifndef BYTE_MATCH_DISTANCE_HPP
#define BYTE_MATCH_DISTANCE_HPP

#include <vector>
#include <cmath>
#include <algorithm>
#include "strack.hpp"

namespace ByteTrack{

// 函数名称: build_center_distance
// 输入: 轨迹/检测的 tlbr 序列；是否混合；混合权重；归一化模式
// 输出: 代价矩阵（越小越好）
// 简介: 计算中心点欧式距离；如启用混合，与 IoU 成本线性混合
// 详细流程:
//   1) 计算中心与尺寸
//   2) 距离按配置归一化（bbox 或不归一）
//   3) 若 use_hybrid=true，则 cost = alpha*(1-IoU) + (1-alpha)*d
// 编写人: TOT
inline std::vector<std::vector<float>> build_center_distance(
    const std::vector<std::vector<float>>& atlbrs,
    const std::vector<std::vector<float>>& btlbrs,
    bool use_hybrid,
    float hybrid_alpha,
    const std::string& norm_mode,
    const std::vector<std::vector<float>>* iou_cost_ptr
){
    int na = atlbrs.size();
    int nb = btlbrs.size();
    std::vector<std::vector<float>> cost(na, std::vector<float>(nb, 0.0f));
    for(int i=0;i<na;i++){
        float acx = (atlbrs[i][0] + atlbrs[i][2]) * 0.5f;
        float acy = (atlbrs[i][1] + atlbrs[i][3]) * 0.5f;
        float aw  = atlbrs[i][2] - atlbrs[i][0];
        float ah  = atlbrs[i][3] - atlbrs[i][1];
        float anorm = (norm_mode == "bbox") ? std::max(1.0f, std::max(aw, ah)) : 1.0f;
        for(int j=0;j<nb;j++){
            float bcx = (btlbrs[j][0] + btlbrs[j][2]) * 0.5f;
            float bcy = (btlbrs[j][1] + btlbrs[j][3]) * 0.5f;
            float dx = (acx - bcx) / anorm;
            float dy = (acy - bcy) / anorm;
            float d  = std::sqrt(dx*dx + dy*dy);
            if(use_hybrid && iou_cost_ptr){
                cost[i][j] = hybrid_alpha * (*iou_cost_ptr)[i][j] + (1.0f - hybrid_alpha) * d;
            }else{
                cost[i][j] = d;
            }
        }
    }
    return cost;
}

} // namespace ByteTrack

#endif // BYTE_MATCH_DISTANCE_HPP


#include "byte_tracker.hpp"
#include "match_distance.hpp"
#include <algorithm>
#include <cmath>

namespace ByteTrack{

// 中心距离/混合代价（指针版本）
std::vector<std::vector<float>> BYTETracker::center_distance(std::vector<STrack *> &atracks, std::vector<STrack> &btracks, int &dist_size, int &dist_size_size)
{
    std::vector<std::vector<float>> cost_matrix;
    if (atracks.size() * btracks.size() == 0)
    {
        dist_size = atracks.size();
        dist_size_size = btracks.size();
        return cost_matrix;
    }
    std::vector<std::vector<float>> atlbrs, btlbrs;
    for (int i = 0; i < atracks.size(); i++) atlbrs.push_back(atracks[i]->tlbr);
    for (int i = 0; i < btracks.size(); i++) btlbrs.push_back(btracks[i].tlbr);
    dist_size = atracks.size();
    dist_size_size = btracks.size();
    bool use_hybrid = (match_metric_ == "hybrid");
    std::vector<std::vector<float>> iou_cost;
    if(use_hybrid){ iou_cost = iou_distance(atracks, btracks, dist_size, dist_size_size); }
    cost_matrix = build_center_distance(atlbrs, btlbrs, use_hybrid, hybrid_alpha_, dist_norm_mode_, use_hybrid? &iou_cost : nullptr);
    return cost_matrix;
}

// 中心距离/混合代价（值版本）
std::vector<std::vector<float>> BYTETracker::center_distance(std::vector<STrack> &atracks, std::vector<STrack> &btracks)
{
    std::vector<std::vector<float>> atlbrs, btlbrs;
    for (int i = 0; i < atracks.size(); i++) atlbrs.push_back(atracks[i].tlbr);
    for (int i = 0; i < btracks.size(); i++) btlbrs.push_back(btracks[i].tlbr);
    bool use_hybrid = (match_metric_ == "hybrid");
    std::vector<std::vector<float>> iou_cost;
    if(use_hybrid){ iou_cost = iou_distance(atracks, btracks); }
    return build_center_distance(atlbrs, btlbrs, use_hybrid, hybrid_alpha_, dist_norm_mode_, use_hybrid? &iou_cost : nullptr);
}

} // namespace ByteTrack


#ifndef _BYTE_TRACKER_HPP_
#define _BYTE_TRACKER_HPP_

#include "strack.hpp"
#include "track_util.hpp"
#include <common/object_detector.hpp>
#include <string>
#include "match_distance.hpp"

namespace ByteTrack
{
	using namespace ObjectDetector;

	class BYTETracker
	{
	public:
		BYTETracker(int frame_rate = 30, int track_buffer = 30);
		~BYTETracker();

		vector<STrack> update(const BoxArray &objects, int stream_id);
		Scalar get_color(int idx);

		void update_config(float iou_match_thresh, int frame_rate, int track_buffer);
		// 匹配度量和阈值设置（可选）
		void set_match_params(const std::string& metric, const std::string& norm_mode,
						   float thr_main, float thr_rtracked, float thr_unconf, float hybrid_alpha);

	private:
		vector<STrack *> joint_stracks(vector<STrack *> &tlista, vector<STrack> &tlistb);
		vector<STrack> joint_stracks(vector<STrack> &tlista, vector<STrack> &tlistb);

		vector<STrack> sub_stracks(vector<STrack> &tlista, vector<STrack> &tlistb);
		void remove_duplicate_stracks(vector<STrack> &resa, vector<STrack> &resb, vector<STrack> &stracksa, vector<STrack> &stracksb);

		void linear_assignment(vector<vector<float>> &cost_matrix, int cost_matrix_size, int cost_matrix_size_size, float thresh,
								vector<vector<int>> &matches, vector<int> &unmatched_a, vector<int> &unmatched_b);
		vector<vector<float>> iou_distance(vector<STrack *> &atracks, vector<STrack> &btracks, int &dist_size, int &dist_size_size);

		// 中心距离/混合代价
		vector<vector<float>> center_distance(vector<STrack *> &atracks, vector<STrack> &btracks, int &dist_size, int &dist_size_size);
		vector<vector<float>> center_distance(vector<STrack> &atracks, vector<STrack> &btracks);

		vector<vector<float>> iou_distance(vector<STrack> &atracks, vector<STrack> &btracks);
		vector<vector<float>> ious(vector<vector<float>> &atlbrs, vector<vector<float>> &btlbrs);

		double lapjv(const vector<vector<float>> &cost, vector<int> &rowsol, vector<int> &colsol,
						bool extend_cost = false, float cost_limit = LONG_MAX, bool return_cost = true);

	private:
		float track_thresh;
		float high_thresh;
		float match_thresh;

		// 匹配参数
		std::string match_metric_ = "iou";   // iou | center | hybrid
		std::string dist_norm_mode_ = "bbox"; // bbox | none
		float dist_match_thresh_ = 0.050f;
		float dist_match_thresh_rtracked_ = 0.040f;
		float dist_match_thresh_unconfirmed_ = 0.060f;
		float hybrid_alpha_ = 0.30f;

		int frame_id;
		int max_time_lost;

		vector<STrack> tracked_stracks;
		vector<STrack> lost_stracks;
		vector<STrack> removed_stracks;
		KalmanFilter kalman_filter;
	};
}

#endif // _BYTE_TRACKER_HPP_
#include "app_airport/nn_algorithm_sp100vf.h"
#include <common/ilogger.hpp>
#include "tools/zmq_remote_show.hpp"
#include <opencv2/opencv.hpp>
#include <thread>
#include <vector>

using namespace cv;
using namespace std;

// static const char* labels[] = {"person", "car", "airplane", "bird"};
// static const char* labels[] = {"fly", "fly", "fly", "UAV", "bird", "UAV", "UAV", "fly"};
// static const char* labels[] = {"drone", "bird", "fly", "UAV", "bird", "UAV", "UAV", "fly"};
// 0: human, 1: car, 2: airport, 3: bird 4:UAV  5：战斗机 6：直升机 7：空飘物
static const char* labels[] = {"human", "car", "airport", "bird", "UAV", "UAV1", "UAV2", "AFO"};

static void to_frame(sp100vf_alg_input& input, const Mat& image, int stream_id){
    input.image_date  = image.data;
    input.image_width = image.cols;
    input.image_high  = image.rows;
    input.camera_id   = stream_id;
}

static void visualization(const vector<sp100vf_alg_target>& result, Mat& image, string& save_path){

    for(auto& target : result){

        auto left   = (int)target.bbox[0];
        auto top    = (int)target.bbox[1];
        auto right  = (int)target.bbox[2];
        auto bottom = (int)target.bbox[3];
        auto width  = right - left;
        auto height = bottom - top;

        uint8_t b, g, r;
        tie(b, g, r) = iLogger::random_color(target.class_id);
        cv::rectangle(image, cv::Rect(left, top, width, height), cv::Scalar(b, g, r), 5);

        auto name = labels[target.class_id];
        auto caption = iLogger::format("%s %.2f", name, target.confidence);
        int width_font = cv::getTextSize(caption, 0, 1, 2, nullptr).width + 10;
        cv::rectangle(image, cv::Point(left-3, top-33), cv::Point(left + width_font, top), cv::Scalar(b, g, r), -1);
        cv::putText(image, caption, cv::Point(left, top-5), 0, 1, cv::Scalar::all(0), 2, 16);
    }

    cv::imwrite(save_path, image);
}

void detectWorker0(sp100vf_alg_input& input, cv::Mat& image, int max_iters){

    void* handle = nullptr;
    for(int i = 0; i < max_iters; ++i){
        auto result = nn_algorithm_detect(handle, &input);
        INFO("iter = %d, stream_id = %d, boxes size = %d", i, input.camera_id, result.size());
        if(i == (max_iters - 1)){
            string save_path = "image_vl_1.jpg";
            visualization(result, image, save_path);
            INFO("image_vl_1.jpg save done");
        }
    }
}

void detectWorker1(sp100vf_alg_input& input, cv::Mat& image, int max_iters){

    void* handle = nullptr;
    for(int i = 0; i < max_iters; ++i){
        auto result = nn_algorithm_detect(handle, &input);
        INFO("iter = %d, stream_id = %d, boxes size = %d", i, input.camera_id, result.size());
        if(i == (max_iters - 1)){
            string save_path = "image_vl_2.jpg";
            visualization(result, image, save_path);
            INFO("image_vl_2.jpg save done");
        }
    }
}

static void app_airport_video(){

    ///////////////////////////////////////////////////////////
    // 算法版本获取
    sp100vf_alg_ver_info version_info;
    if(!nn_algorithm_version(&version_info)){
        INFOE("faile to obtain algorithm version");
        return;
    }
    INFO("algorithm version = %s", version_info.program_date);

    ///////////////////////////////////////////////////////////
    //  算法初始化
    sp100vf_alg_pre_cfg thread_cfg;
    if(!nn_algorithm_init(&thread_cfg)){
        INFOE("algorithm init failed");
        return;
    }

    ///////////////////////////////////////////////////////////
    //  vl
    sp100vf_alg_input input_vl_1, input_vl_2;
    Mat image_vl_1 = cv::imread("inference/1.jpg");
    // Mat image_vl_1 = cv::imread("inference/vl_car.jpg");
    Mat image_vl_2 = cv::imread("inference/1.jpg");
    if(image_vl_1.empty() || image_vl_2.empty()){
        INFOE("could not load vl image");
        return;
    }

    // stream_id = 0, 2
    to_frame(input_vl_1, image_vl_1, 0);
    to_frame(input_vl_2, image_vl_2, 2);

    // ir
    sp100vf_alg_input input_ir_1, input_ir_2;
    Mat image_ir_1 = cv::imread("inference/ir_airplane.jpg");
    Mat image_ir_2 = cv::imread("inference/ir_airplane.jpg");
    if(image_ir_1.empty() || image_ir_2.empty()){
        INFOE("could not load ir image");
        return;
    }

    // stream_id = 1, 3
    to_frame(input_ir_1, image_ir_1, 1);
    to_frame(input_ir_2, image_ir_2, 3);

    // VideoCapture cap_vl("videos/vl.mp4");
    // if(!cap_vl.isOpened()){
    //     INFOE("Could not open the video/vl.mp4");
    //     return;
    // }
    // VideoCapture cap_ir("videos/ir.mp4");
    // if(!cap_ir.isOpened()){
    //     INFOE("Could not open the video/ir.mp4");
    //     return;
    // }

    // VideoWriter write_vl("demo_vl.mp4", VideoWriter::fourcc('m', 'p', '4', 'v'), 25.0, Size(1920, 1080));
    // VideoWriter write_ir("demo_ir.mp4", VideoWriter::fourcc('m', 'p', '4', 'v'), 25.0, Size(640, 512));
    // auto remote_show = create_zmq_remote_show("tcp://0.0.0.0:15556");

    // detection


    const int max_iters = 100;
    std::thread t0(detectWorker0, std::ref(input_vl_1), std::ref(image_vl_1), max_iters);
    // std::thread t1(detectWorker1, std::ref(input_vl_2), std::ref(image_vl_2), max_iters);

    t0.join();
    // t1.join();

    ///////////////////////////////////////////////////////////
    //  算法资源释放
    void* handle = nullptr;
    nn_algorithm_close(handle);
}

/*
 * 函数名称: app_airport_video_single_thread
 * 输入: 无
 * 输出: 无
 * 简介: 单线程读取视频并逐帧进行目标检测推理，参考 app_airport_video，不使用静态图像模拟。
 * 详细流程:
 *   1) 获取并打印算法版本信息
 *   2) 初始化算法运行环境
 *   3) 打开视频文件 videos/vl.mp4
 *   4) 循环读取每帧，封装为算法输入并调用推理接口，打印结果并在帧上绘制可视化
 *   5) 将绘制后的帧写入输出视频 demo_vl_single_thread.mp4（若写入器初始化成功）
 *   6) 推理完成后关闭算法资源
 */
static void app_airport_video_single_thread(const string& path){

    // 1) 算法版本获取
    sp100vf_alg_ver_info version_info;
    if(!nn_algorithm_version(&version_info)){
        INFOE("faile to obtain algorithm version");
        return;
    }
    INFO("algorithm version = %s", version_info.program_date);

    // 2) 算法初始化
    sp100vf_alg_pre_cfg thread_cfg;
    if(!nn_algorithm_init(&thread_cfg)){
        INFOE("algorithm init failed");
        return;
    }

    // 3) 打开视频（可见光示例）
    cv::VideoCapture cap_vl(path);
    if(!cap_vl.isOpened()){
        INFOE("Could not open the %s", path.c_str());
        void* handle_err = nullptr;
        nn_algorithm_close(handle_err);
        return;
    }

    // 输出视频写入器（可选）
    double fps = cap_vl.get(cv::CAP_PROP_FPS);
    if(!(fps > 0.0)) fps = 25.0;
    int frame_w = static_cast<int>(cap_vl.get(cv::CAP_PROP_FRAME_WIDTH));
    int frame_h = static_cast<int>(cap_vl.get(cv::CAP_PROP_FRAME_HEIGHT));
    cv::Size frame_size(frame_w, frame_h);
    cv::VideoWriter writer;
    writer.open("demo_vl_single_thread.mp4", cv::VideoWriter::fourcc('m','p','4','v'), fps, frame_size);
    if(writer.isOpened()){
        INFO("video writer opened, fps=%.2f, size=%dx%d", fps, frame_w, frame_h);
    }else{
        INFO("video writer not opened, will skip writing");
    }

    // 4) 循环读取每帧并推理
    void* handle = nullptr;
    sp100vf_alg_input input_vl;

    cv::Mat frame;
    int frame_id = 0;
    while(true){
        if(!cap_vl.read(frame)){
            INFO("video read finished");
            break;
        }

        // 封装为算法输入，stream_id 固定为 0
        to_frame(input_vl, frame, 0);

        // 推理
        auto result = nn_algorithm_detect(handle, &input_vl);
        INFO("frame = %d, stream_id = %d, boxes size = %d", frame_id, input_vl.camera_id, (int)result.size());

        // 在帧上绘制检测结果
        for(auto& target : result){
            int left   = (int)target.bbox[0];
            int top    = (int)target.bbox[1];
            int right  = (int)target.bbox[2];
            int bottom = (int)target.bbox[3];
            int width  = right - left;
            int height = bottom - top;
            uint8_t b, g, r;
            tie(b, g, r) = iLogger::random_color(target.class_id);
            cv::rectangle(frame, cv::Rect(left, top, width, height), cv::Scalar(b, g, r), 3);
            const char* name = labels[target.class_id];
            auto caption = iLogger::format("%s %d %.2f", name, target.list_id, target.confidence);
            int width_font = cv::getTextSize(caption, 0, 0.8, 2, nullptr).width + 8;
            cv::rectangle(frame, cv::Point(left-2, top-24), cv::Point(left + width_font, top), cv::Scalar(b, g, r), -1);
            cv::putText(frame, caption, cv::Point(left, top-6), 0, 0.8, cv::Scalar::all(0), 2, 16);
        }

        // 写入输出视频
        if(writer.isOpened()){
            writer.write(frame);
        }

        ++frame_id;
    }

    // 5) 释放资源
    nn_algorithm_close(handle);
}


int app_airport(const string& path){
    // app_airport_video();
    app_airport_video_single_thread(path);
    return 0;
}
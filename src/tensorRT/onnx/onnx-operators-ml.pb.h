// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: onnx-operators-ml.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_onnx_2doperators_2dml_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_onnx_2doperators_2dml_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3011000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3011004 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "onnx-ml.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_onnx_2doperators_2dml_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_onnx_2doperators_2dml_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_onnx_2doperators_2dml_2eproto;
namespace onnx {
class OperatorProto;
class OperatorProtoDefaultTypeInternal;
extern OperatorProtoDefaultTypeInternal _OperatorProto_default_instance_;
class OperatorSetProto;
class OperatorSetProtoDefaultTypeInternal;
extern OperatorSetProtoDefaultTypeInternal _OperatorSetProto_default_instance_;
}  // namespace onnx
PROTOBUF_NAMESPACE_OPEN
template<> ::onnx::OperatorProto* Arena::CreateMaybeMessage<::onnx::OperatorProto>(Arena*);
template<> ::onnx::OperatorSetProto* Arena::CreateMaybeMessage<::onnx::OperatorSetProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace onnx {

// ===================================================================

class OperatorProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.OperatorProto) */ {
 public:
  OperatorProto();
  virtual ~OperatorProto();

  OperatorProto(const OperatorProto& from);
  OperatorProto(OperatorProto&& from) noexcept
    : OperatorProto() {
    *this = ::std::move(from);
  }

  inline OperatorProto& operator=(const OperatorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OperatorProto& operator=(OperatorProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OperatorProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OperatorProto* internal_default_instance() {
    return reinterpret_cast<const OperatorProto*>(
               &_OperatorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(OperatorProto& a, OperatorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OperatorProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OperatorProto* New() const final {
    return CreateMaybeMessage<OperatorProto>(nullptr);
  }

  OperatorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OperatorProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OperatorProto& from);
  void MergeFrom(const OperatorProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OperatorProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.OperatorProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2doperators_2dml_2eproto);
    return ::descriptor_table_onnx_2doperators_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpTypeFieldNumber = 1,
    kDocStringFieldNumber = 10,
    kSinceVersionFieldNumber = 2,
    kStatusFieldNumber = 3,
  };
  // optional string op_type = 1;
  bool has_op_type() const;
  private:
  bool _internal_has_op_type() const;
  public:
  void clear_op_type();
  const std::string& op_type() const;
  void set_op_type(const std::string& value);
  void set_op_type(std::string&& value);
  void set_op_type(const char* value);
  void set_op_type(const char* value, size_t size);
  std::string* mutable_op_type();
  std::string* release_op_type();
  void set_allocated_op_type(std::string* op_type);
  private:
  const std::string& _internal_op_type() const;
  void _internal_set_op_type(const std::string& value);
  std::string* _internal_mutable_op_type();
  public:

  // optional string doc_string = 10;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  void set_doc_string(const std::string& value);
  void set_doc_string(std::string&& value);
  void set_doc_string(const char* value);
  void set_doc_string(const char* value, size_t size);
  std::string* mutable_doc_string();
  std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional int64 since_version = 2;
  bool has_since_version() const;
  private:
  bool _internal_has_since_version() const;
  public:
  void clear_since_version();
  ::PROTOBUF_NAMESPACE_ID::int64 since_version() const;
  void set_since_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_since_version() const;
  void _internal_set_since_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // optional .onnx.OperatorStatus status = 3;
  bool has_status() const;
  private:
  bool _internal_has_status() const;
  public:
  void clear_status();
  ::onnx::OperatorStatus status() const;
  void set_status(::onnx::OperatorStatus value);
  private:
  ::onnx::OperatorStatus _internal_status() const;
  void _internal_set_status(::onnx::OperatorStatus value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.OperatorProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::PROTOBUF_NAMESPACE_ID::int64 since_version_;
  int status_;
  friend struct ::TableStruct_onnx_2doperators_2dml_2eproto;
};
// -------------------------------------------------------------------

class OperatorSetProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.OperatorSetProto) */ {
 public:
  OperatorSetProto();
  virtual ~OperatorSetProto();

  OperatorSetProto(const OperatorSetProto& from);
  OperatorSetProto(OperatorSetProto&& from) noexcept
    : OperatorSetProto() {
    *this = ::std::move(from);
  }

  inline OperatorSetProto& operator=(const OperatorSetProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OperatorSetProto& operator=(OperatorSetProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OperatorSetProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OperatorSetProto* internal_default_instance() {
    return reinterpret_cast<const OperatorSetProto*>(
               &_OperatorSetProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(OperatorSetProto& a, OperatorSetProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OperatorSetProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OperatorSetProto* New() const final {
    return CreateMaybeMessage<OperatorSetProto>(nullptr);
  }

  OperatorSetProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OperatorSetProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OperatorSetProto& from);
  void MergeFrom(const OperatorSetProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OperatorSetProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.OperatorSetProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2doperators_2dml_2eproto);
    return ::descriptor_table_onnx_2doperators_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperatorFieldNumber = 8,
    kFunctionsFieldNumber = 9,
    kMagicFieldNumber = 1,
    kIrVersionPrereleaseFieldNumber = 3,
    kDomainFieldNumber = 4,
    kDocStringFieldNumber = 6,
    kIrBuildMetadataFieldNumber = 7,
    kIrVersionFieldNumber = 2,
    kOpsetVersionFieldNumber = 5,
  };
  // repeated .onnx.OperatorProto operator = 8;
  int operator__size() const;
  private:
  int _internal_operator__size() const;
  public:
  void clear_operator_();
  ::onnx::OperatorProto* mutable_operator_(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorProto >*
      mutable_operator_();
  private:
  const ::onnx::OperatorProto& _internal_operator_(int index) const;
  ::onnx::OperatorProto* _internal_add_operator_();
  public:
  const ::onnx::OperatorProto& operator_(int index) const;
  ::onnx::OperatorProto* add_operator_();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorProto >&
      operator_() const;

  // repeated .onnx.FunctionProto functions = 9;
  int functions_size() const;
  private:
  int _internal_functions_size() const;
  public:
  void clear_functions();
  ::onnx::FunctionProto* mutable_functions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto >*
      mutable_functions();
  private:
  const ::onnx::FunctionProto& _internal_functions(int index) const;
  ::onnx::FunctionProto* _internal_add_functions();
  public:
  const ::onnx::FunctionProto& functions(int index) const;
  ::onnx::FunctionProto* add_functions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto >&
      functions() const;

  // optional string magic = 1;
  bool has_magic() const;
  private:
  bool _internal_has_magic() const;
  public:
  void clear_magic();
  const std::string& magic() const;
  void set_magic(const std::string& value);
  void set_magic(std::string&& value);
  void set_magic(const char* value);
  void set_magic(const char* value, size_t size);
  std::string* mutable_magic();
  std::string* release_magic();
  void set_allocated_magic(std::string* magic);
  private:
  const std::string& _internal_magic() const;
  void _internal_set_magic(const std::string& value);
  std::string* _internal_mutable_magic();
  public:

  // optional string ir_version_prerelease = 3;
  bool has_ir_version_prerelease() const;
  private:
  bool _internal_has_ir_version_prerelease() const;
  public:
  void clear_ir_version_prerelease();
  const std::string& ir_version_prerelease() const;
  void set_ir_version_prerelease(const std::string& value);
  void set_ir_version_prerelease(std::string&& value);
  void set_ir_version_prerelease(const char* value);
  void set_ir_version_prerelease(const char* value, size_t size);
  std::string* mutable_ir_version_prerelease();
  std::string* release_ir_version_prerelease();
  void set_allocated_ir_version_prerelease(std::string* ir_version_prerelease);
  private:
  const std::string& _internal_ir_version_prerelease() const;
  void _internal_set_ir_version_prerelease(const std::string& value);
  std::string* _internal_mutable_ir_version_prerelease();
  public:

  // optional string domain = 4;
  bool has_domain() const;
  private:
  bool _internal_has_domain() const;
  public:
  void clear_domain();
  const std::string& domain() const;
  void set_domain(const std::string& value);
  void set_domain(std::string&& value);
  void set_domain(const char* value);
  void set_domain(const char* value, size_t size);
  std::string* mutable_domain();
  std::string* release_domain();
  void set_allocated_domain(std::string* domain);
  private:
  const std::string& _internal_domain() const;
  void _internal_set_domain(const std::string& value);
  std::string* _internal_mutable_domain();
  public:

  // optional string doc_string = 6;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  void set_doc_string(const std::string& value);
  void set_doc_string(std::string&& value);
  void set_doc_string(const char* value);
  void set_doc_string(const char* value, size_t size);
  std::string* mutable_doc_string();
  std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional string ir_build_metadata = 7;
  bool has_ir_build_metadata() const;
  private:
  bool _internal_has_ir_build_metadata() const;
  public:
  void clear_ir_build_metadata();
  const std::string& ir_build_metadata() const;
  void set_ir_build_metadata(const std::string& value);
  void set_ir_build_metadata(std::string&& value);
  void set_ir_build_metadata(const char* value);
  void set_ir_build_metadata(const char* value, size_t size);
  std::string* mutable_ir_build_metadata();
  std::string* release_ir_build_metadata();
  void set_allocated_ir_build_metadata(std::string* ir_build_metadata);
  private:
  const std::string& _internal_ir_build_metadata() const;
  void _internal_set_ir_build_metadata(const std::string& value);
  std::string* _internal_mutable_ir_build_metadata();
  public:

  // optional int64 ir_version = 2;
  bool has_ir_version() const;
  private:
  bool _internal_has_ir_version() const;
  public:
  void clear_ir_version();
  ::PROTOBUF_NAMESPACE_ID::int64 ir_version() const;
  void set_ir_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_ir_version() const;
  void _internal_set_ir_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // optional int64 opset_version = 5;
  bool has_opset_version() const;
  private:
  bool _internal_has_opset_version() const;
  public:
  void clear_opset_version();
  ::PROTOBUF_NAMESPACE_ID::int64 opset_version() const;
  void set_opset_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_opset_version() const;
  void _internal_set_opset_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.OperatorSetProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorProto > operator__;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto > functions_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr magic_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ir_version_prerelease_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr domain_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ir_build_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 ir_version_;
  ::PROTOBUF_NAMESPACE_ID::int64 opset_version_;
  friend struct ::TableStruct_onnx_2doperators_2dml_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// OperatorProto

// optional string op_type = 1;
inline bool OperatorProto::_internal_has_op_type() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool OperatorProto::has_op_type() const {
  return _internal_has_op_type();
}
inline void OperatorProto::clear_op_type() {
  op_type_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& OperatorProto::op_type() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorProto.op_type)
  return _internal_op_type();
}
inline void OperatorProto::set_op_type(const std::string& value) {
  _internal_set_op_type(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorProto.op_type)
}
inline std::string* OperatorProto::mutable_op_type() {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorProto.op_type)
  return _internal_mutable_op_type();
}
inline const std::string& OperatorProto::_internal_op_type() const {
  return op_type_.GetNoArena();
}
inline void OperatorProto::_internal_set_op_type(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  op_type_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void OperatorProto::set_op_type(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  op_type_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.OperatorProto.op_type)
}
inline void OperatorProto::set_op_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  op_type_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.OperatorProto.op_type)
}
inline void OperatorProto::set_op_type(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  op_type_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.OperatorProto.op_type)
}
inline std::string* OperatorProto::_internal_mutable_op_type() {
  _has_bits_[0] |= 0x00000001u;
  return op_type_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OperatorProto::release_op_type() {
  // @@protoc_insertion_point(field_release:onnx.OperatorProto.op_type)
  if (!_internal_has_op_type()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return op_type_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OperatorProto::set_allocated_op_type(std::string* op_type) {
  if (op_type != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  op_type_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_type);
  // @@protoc_insertion_point(field_set_allocated:onnx.OperatorProto.op_type)
}

// optional int64 since_version = 2;
inline bool OperatorProto::_internal_has_since_version() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool OperatorProto::has_since_version() const {
  return _internal_has_since_version();
}
inline void OperatorProto::clear_since_version() {
  since_version_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OperatorProto::_internal_since_version() const {
  return since_version_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OperatorProto::since_version() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorProto.since_version)
  return _internal_since_version();
}
inline void OperatorProto::_internal_set_since_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000004u;
  since_version_ = value;
}
inline void OperatorProto::set_since_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_since_version(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorProto.since_version)
}

// optional .onnx.OperatorStatus status = 3;
inline bool OperatorProto::_internal_has_status() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool OperatorProto::has_status() const {
  return _internal_has_status();
}
inline void OperatorProto::clear_status() {
  status_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::onnx::OperatorStatus OperatorProto::_internal_status() const {
  return static_cast< ::onnx::OperatorStatus >(status_);
}
inline ::onnx::OperatorStatus OperatorProto::status() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorProto.status)
  return _internal_status();
}
inline void OperatorProto::_internal_set_status(::onnx::OperatorStatus value) {
  assert(::onnx::OperatorStatus_IsValid(value));
  _has_bits_[0] |= 0x00000008u;
  status_ = value;
}
inline void OperatorProto::set_status(::onnx::OperatorStatus value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorProto.status)
}

// optional string doc_string = 10;
inline bool OperatorProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool OperatorProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void OperatorProto::clear_doc_string() {
  doc_string_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& OperatorProto::doc_string() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorProto.doc_string)
  return _internal_doc_string();
}
inline void OperatorProto::set_doc_string(const std::string& value) {
  _internal_set_doc_string(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorProto.doc_string)
}
inline std::string* OperatorProto::mutable_doc_string() {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorProto.doc_string)
  return _internal_mutable_doc_string();
}
inline const std::string& OperatorProto::_internal_doc_string() const {
  return doc_string_.GetNoArena();
}
inline void OperatorProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void OperatorProto::set_doc_string(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.OperatorProto.doc_string)
}
inline void OperatorProto::set_doc_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.OperatorProto.doc_string)
}
inline void OperatorProto::set_doc_string(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.OperatorProto.doc_string)
}
inline std::string* OperatorProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000002u;
  return doc_string_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OperatorProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:onnx.OperatorProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return doc_string_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OperatorProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  doc_string_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string);
  // @@protoc_insertion_point(field_set_allocated:onnx.OperatorProto.doc_string)
}

// -------------------------------------------------------------------

// OperatorSetProto

// optional string magic = 1;
inline bool OperatorSetProto::_internal_has_magic() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool OperatorSetProto::has_magic() const {
  return _internal_has_magic();
}
inline void OperatorSetProto::clear_magic() {
  magic_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& OperatorSetProto::magic() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetProto.magic)
  return _internal_magic();
}
inline void OperatorSetProto::set_magic(const std::string& value) {
  _internal_set_magic(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorSetProto.magic)
}
inline std::string* OperatorSetProto::mutable_magic() {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorSetProto.magic)
  return _internal_mutable_magic();
}
inline const std::string& OperatorSetProto::_internal_magic() const {
  return magic_.GetNoArena();
}
inline void OperatorSetProto::_internal_set_magic(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  magic_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void OperatorSetProto::set_magic(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  magic_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.OperatorSetProto.magic)
}
inline void OperatorSetProto::set_magic(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  magic_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.OperatorSetProto.magic)
}
inline void OperatorSetProto::set_magic(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  magic_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.OperatorSetProto.magic)
}
inline std::string* OperatorSetProto::_internal_mutable_magic() {
  _has_bits_[0] |= 0x00000001u;
  return magic_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OperatorSetProto::release_magic() {
  // @@protoc_insertion_point(field_release:onnx.OperatorSetProto.magic)
  if (!_internal_has_magic()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return magic_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OperatorSetProto::set_allocated_magic(std::string* magic) {
  if (magic != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  magic_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), magic);
  // @@protoc_insertion_point(field_set_allocated:onnx.OperatorSetProto.magic)
}

// optional int64 ir_version = 2;
inline bool OperatorSetProto::_internal_has_ir_version() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool OperatorSetProto::has_ir_version() const {
  return _internal_has_ir_version();
}
inline void OperatorSetProto::clear_ir_version() {
  ir_version_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000020u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OperatorSetProto::_internal_ir_version() const {
  return ir_version_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OperatorSetProto::ir_version() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetProto.ir_version)
  return _internal_ir_version();
}
inline void OperatorSetProto::_internal_set_ir_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000020u;
  ir_version_ = value;
}
inline void OperatorSetProto::set_ir_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_ir_version(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorSetProto.ir_version)
}

// optional string ir_version_prerelease = 3;
inline bool OperatorSetProto::_internal_has_ir_version_prerelease() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool OperatorSetProto::has_ir_version_prerelease() const {
  return _internal_has_ir_version_prerelease();
}
inline void OperatorSetProto::clear_ir_version_prerelease() {
  ir_version_prerelease_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& OperatorSetProto::ir_version_prerelease() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetProto.ir_version_prerelease)
  return _internal_ir_version_prerelease();
}
inline void OperatorSetProto::set_ir_version_prerelease(const std::string& value) {
  _internal_set_ir_version_prerelease(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorSetProto.ir_version_prerelease)
}
inline std::string* OperatorSetProto::mutable_ir_version_prerelease() {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorSetProto.ir_version_prerelease)
  return _internal_mutable_ir_version_prerelease();
}
inline const std::string& OperatorSetProto::_internal_ir_version_prerelease() const {
  return ir_version_prerelease_.GetNoArena();
}
inline void OperatorSetProto::_internal_set_ir_version_prerelease(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  ir_version_prerelease_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void OperatorSetProto::set_ir_version_prerelease(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  ir_version_prerelease_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.OperatorSetProto.ir_version_prerelease)
}
inline void OperatorSetProto::set_ir_version_prerelease(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  ir_version_prerelease_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.OperatorSetProto.ir_version_prerelease)
}
inline void OperatorSetProto::set_ir_version_prerelease(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  ir_version_prerelease_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.OperatorSetProto.ir_version_prerelease)
}
inline std::string* OperatorSetProto::_internal_mutable_ir_version_prerelease() {
  _has_bits_[0] |= 0x00000002u;
  return ir_version_prerelease_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OperatorSetProto::release_ir_version_prerelease() {
  // @@protoc_insertion_point(field_release:onnx.OperatorSetProto.ir_version_prerelease)
  if (!_internal_has_ir_version_prerelease()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return ir_version_prerelease_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OperatorSetProto::set_allocated_ir_version_prerelease(std::string* ir_version_prerelease) {
  if (ir_version_prerelease != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  ir_version_prerelease_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ir_version_prerelease);
  // @@protoc_insertion_point(field_set_allocated:onnx.OperatorSetProto.ir_version_prerelease)
}

// optional string ir_build_metadata = 7;
inline bool OperatorSetProto::_internal_has_ir_build_metadata() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool OperatorSetProto::has_ir_build_metadata() const {
  return _internal_has_ir_build_metadata();
}
inline void OperatorSetProto::clear_ir_build_metadata() {
  ir_build_metadata_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000010u;
}
inline const std::string& OperatorSetProto::ir_build_metadata() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetProto.ir_build_metadata)
  return _internal_ir_build_metadata();
}
inline void OperatorSetProto::set_ir_build_metadata(const std::string& value) {
  _internal_set_ir_build_metadata(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorSetProto.ir_build_metadata)
}
inline std::string* OperatorSetProto::mutable_ir_build_metadata() {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorSetProto.ir_build_metadata)
  return _internal_mutable_ir_build_metadata();
}
inline const std::string& OperatorSetProto::_internal_ir_build_metadata() const {
  return ir_build_metadata_.GetNoArena();
}
inline void OperatorSetProto::_internal_set_ir_build_metadata(const std::string& value) {
  _has_bits_[0] |= 0x00000010u;
  ir_build_metadata_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void OperatorSetProto::set_ir_build_metadata(std::string&& value) {
  _has_bits_[0] |= 0x00000010u;
  ir_build_metadata_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.OperatorSetProto.ir_build_metadata)
}
inline void OperatorSetProto::set_ir_build_metadata(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000010u;
  ir_build_metadata_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.OperatorSetProto.ir_build_metadata)
}
inline void OperatorSetProto::set_ir_build_metadata(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000010u;
  ir_build_metadata_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.OperatorSetProto.ir_build_metadata)
}
inline std::string* OperatorSetProto::_internal_mutable_ir_build_metadata() {
  _has_bits_[0] |= 0x00000010u;
  return ir_build_metadata_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OperatorSetProto::release_ir_build_metadata() {
  // @@protoc_insertion_point(field_release:onnx.OperatorSetProto.ir_build_metadata)
  if (!_internal_has_ir_build_metadata()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000010u;
  return ir_build_metadata_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OperatorSetProto::set_allocated_ir_build_metadata(std::string* ir_build_metadata) {
  if (ir_build_metadata != nullptr) {
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  ir_build_metadata_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ir_build_metadata);
  // @@protoc_insertion_point(field_set_allocated:onnx.OperatorSetProto.ir_build_metadata)
}

// optional string domain = 4;
inline bool OperatorSetProto::_internal_has_domain() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool OperatorSetProto::has_domain() const {
  return _internal_has_domain();
}
inline void OperatorSetProto::clear_domain() {
  domain_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& OperatorSetProto::domain() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetProto.domain)
  return _internal_domain();
}
inline void OperatorSetProto::set_domain(const std::string& value) {
  _internal_set_domain(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorSetProto.domain)
}
inline std::string* OperatorSetProto::mutable_domain() {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorSetProto.domain)
  return _internal_mutable_domain();
}
inline const std::string& OperatorSetProto::_internal_domain() const {
  return domain_.GetNoArena();
}
inline void OperatorSetProto::_internal_set_domain(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void OperatorSetProto::set_domain(std::string&& value) {
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.OperatorSetProto.domain)
}
inline void OperatorSetProto::set_domain(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.OperatorSetProto.domain)
}
inline void OperatorSetProto::set_domain(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.OperatorSetProto.domain)
}
inline std::string* OperatorSetProto::_internal_mutable_domain() {
  _has_bits_[0] |= 0x00000004u;
  return domain_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OperatorSetProto::release_domain() {
  // @@protoc_insertion_point(field_release:onnx.OperatorSetProto.domain)
  if (!_internal_has_domain()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  return domain_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OperatorSetProto::set_allocated_domain(std::string* domain) {
  if (domain != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  domain_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), domain);
  // @@protoc_insertion_point(field_set_allocated:onnx.OperatorSetProto.domain)
}

// optional int64 opset_version = 5;
inline bool OperatorSetProto::_internal_has_opset_version() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool OperatorSetProto::has_opset_version() const {
  return _internal_has_opset_version();
}
inline void OperatorSetProto::clear_opset_version() {
  opset_version_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000040u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OperatorSetProto::_internal_opset_version() const {
  return opset_version_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OperatorSetProto::opset_version() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetProto.opset_version)
  return _internal_opset_version();
}
inline void OperatorSetProto::_internal_set_opset_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000040u;
  opset_version_ = value;
}
inline void OperatorSetProto::set_opset_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_opset_version(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorSetProto.opset_version)
}

// optional string doc_string = 6;
inline bool OperatorSetProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool OperatorSetProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void OperatorSetProto::clear_doc_string() {
  doc_string_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000008u;
}
inline const std::string& OperatorSetProto::doc_string() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetProto.doc_string)
  return _internal_doc_string();
}
inline void OperatorSetProto::set_doc_string(const std::string& value) {
  _internal_set_doc_string(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorSetProto.doc_string)
}
inline std::string* OperatorSetProto::mutable_doc_string() {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorSetProto.doc_string)
  return _internal_mutable_doc_string();
}
inline const std::string& OperatorSetProto::_internal_doc_string() const {
  return doc_string_.GetNoArena();
}
inline void OperatorSetProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000008u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void OperatorSetProto::set_doc_string(std::string&& value) {
  _has_bits_[0] |= 0x00000008u;
  doc_string_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.OperatorSetProto.doc_string)
}
inline void OperatorSetProto::set_doc_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000008u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.OperatorSetProto.doc_string)
}
inline void OperatorSetProto::set_doc_string(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000008u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.OperatorSetProto.doc_string)
}
inline std::string* OperatorSetProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000008u;
  return doc_string_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OperatorSetProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:onnx.OperatorSetProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000008u;
  return doc_string_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OperatorSetProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  doc_string_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string);
  // @@protoc_insertion_point(field_set_allocated:onnx.OperatorSetProto.doc_string)
}

// repeated .onnx.OperatorProto operator = 8;
inline int OperatorSetProto::_internal_operator__size() const {
  return operator__.size();
}
inline int OperatorSetProto::operator__size() const {
  return _internal_operator__size();
}
inline void OperatorSetProto::clear_operator_() {
  operator__.Clear();
}
inline ::onnx::OperatorProto* OperatorSetProto::mutable_operator_(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorSetProto.operator)
  return operator__.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorProto >*
OperatorSetProto::mutable_operator_() {
  // @@protoc_insertion_point(field_mutable_list:onnx.OperatorSetProto.operator)
  return &operator__;
}
inline const ::onnx::OperatorProto& OperatorSetProto::_internal_operator_(int index) const {
  return operator__.Get(index);
}
inline const ::onnx::OperatorProto& OperatorSetProto::operator_(int index) const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetProto.operator)
  return _internal_operator_(index);
}
inline ::onnx::OperatorProto* OperatorSetProto::_internal_add_operator_() {
  return operator__.Add();
}
inline ::onnx::OperatorProto* OperatorSetProto::add_operator_() {
  // @@protoc_insertion_point(field_add:onnx.OperatorSetProto.operator)
  return _internal_add_operator_();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorProto >&
OperatorSetProto::operator_() const {
  // @@protoc_insertion_point(field_list:onnx.OperatorSetProto.operator)
  return operator__;
}

// repeated .onnx.FunctionProto functions = 9;
inline int OperatorSetProto::_internal_functions_size() const {
  return functions_.size();
}
inline int OperatorSetProto::functions_size() const {
  return _internal_functions_size();
}
inline ::onnx::FunctionProto* OperatorSetProto::mutable_functions(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorSetProto.functions)
  return functions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto >*
OperatorSetProto::mutable_functions() {
  // @@protoc_insertion_point(field_mutable_list:onnx.OperatorSetProto.functions)
  return &functions_;
}
inline const ::onnx::FunctionProto& OperatorSetProto::_internal_functions(int index) const {
  return functions_.Get(index);
}
inline const ::onnx::FunctionProto& OperatorSetProto::functions(int index) const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetProto.functions)
  return _internal_functions(index);
}
inline ::onnx::FunctionProto* OperatorSetProto::_internal_add_functions() {
  return functions_.Add();
}
inline ::onnx::FunctionProto* OperatorSetProto::add_functions() {
  // @@protoc_insertion_point(field_add:onnx.OperatorSetProto.functions)
  return _internal_add_functions();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto >&
OperatorSetProto::functions() const {
  // @@protoc_insertion_point(field_list:onnx.OperatorSetProto.functions)
  return functions_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace onnx

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_onnx_2doperators_2dml_2eproto

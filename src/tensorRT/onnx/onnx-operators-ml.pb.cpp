// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: onnx-operators-ml.proto

#include "onnx-operators-ml.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_FunctionProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2doperators_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_OperatorProto_onnx_2doperators_2dml_2eproto;
namespace onnx {
class OperatorProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<OperatorProto> _instance;
} _OperatorProto_default_instance_;
class OperatorSetProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<OperatorSetProto> _instance;
} _OperatorSetProto_default_instance_;
}  // namespace onnx
static void InitDefaultsscc_info_OperatorProto_onnx_2doperators_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_OperatorProto_default_instance_;
    new (ptr) ::onnx::OperatorProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::OperatorProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_OperatorProto_onnx_2doperators_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_OperatorProto_onnx_2doperators_2dml_2eproto}, {}};

static void InitDefaultsscc_info_OperatorSetProto_onnx_2doperators_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_OperatorSetProto_default_instance_;
    new (ptr) ::onnx::OperatorSetProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::OperatorSetProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_OperatorSetProto_onnx_2doperators_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_OperatorSetProto_onnx_2doperators_2dml_2eproto}, {
      &scc_info_OperatorProto_onnx_2doperators_2dml_2eproto.base,
      &scc_info_FunctionProto_onnx_2dml_2eproto.base,}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_onnx_2doperators_2dml_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_onnx_2doperators_2dml_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_onnx_2doperators_2dml_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_onnx_2doperators_2dml_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorProto, op_type_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorProto, since_version_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorProto, status_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorProto, doc_string_),
  0,
  2,
  3,
  1,
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, magic_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, ir_version_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, ir_version_prerelease_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, ir_build_metadata_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, domain_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, opset_version_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, doc_string_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, operator__),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetProto, functions_),
  0,
  5,
  1,
  4,
  2,
  6,
  3,
  ~0u,
  ~0u,
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 9, sizeof(::onnx::OperatorProto)},
  { 13, 27, sizeof(::onnx::OperatorSetProto)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_OperatorProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_OperatorSetProto_default_instance_),
};

const char descriptor_table_protodef_onnx_2doperators_2dml_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\027onnx-operators-ml.proto\022\004onnx\032\ronnx-ml"
  ".proto\"q\n\rOperatorProto\022\017\n\007op_type\030\001 \001(\t"
  "\022\025\n\rsince_version\030\002 \001(\003\022$\n\006status\030\003 \001(\0162"
  "\024.onnx.OperatorStatus\022\022\n\ndoc_string\030\n \001("
  "\t\"\371\001\n\020OperatorSetProto\022\r\n\005magic\030\001 \001(\t\022\022\n"
  "\nir_version\030\002 \001(\003\022\035\n\025ir_version_prerelea"
  "se\030\003 \001(\t\022\031\n\021ir_build_metadata\030\007 \001(\t\022\016\n\006d"
  "omain\030\004 \001(\t\022\025\n\ropset_version\030\005 \001(\003\022\022\n\ndo"
  "c_string\030\006 \001(\t\022%\n\010operator\030\010 \003(\0132\023.onnx."
  "OperatorProto\022&\n\tfunctions\030\t \003(\0132\023.onnx."
  "FunctionProto"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_onnx_2doperators_2dml_2eproto_deps[1] = {
  &::descriptor_table_onnx_2dml_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_onnx_2doperators_2dml_2eproto_sccs[2] = {
  &scc_info_OperatorProto_onnx_2doperators_2dml_2eproto.base,
  &scc_info_OperatorSetProto_onnx_2doperators_2dml_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_onnx_2doperators_2dml_2eproto_once;
static bool descriptor_table_onnx_2doperators_2dml_2eproto_initialized = false;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_onnx_2doperators_2dml_2eproto = {
  &descriptor_table_onnx_2doperators_2dml_2eproto_initialized, descriptor_table_protodef_onnx_2doperators_2dml_2eproto, "onnx-operators-ml.proto", 413,
  &descriptor_table_onnx_2doperators_2dml_2eproto_once, descriptor_table_onnx_2doperators_2dml_2eproto_sccs, descriptor_table_onnx_2doperators_2dml_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_onnx_2doperators_2dml_2eproto::offsets,
  file_level_metadata_onnx_2doperators_2dml_2eproto, 2, file_level_enum_descriptors_onnx_2doperators_2dml_2eproto, file_level_service_descriptors_onnx_2doperators_2dml_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_onnx_2doperators_2dml_2eproto = (  ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_onnx_2doperators_2dml_2eproto), true);
namespace onnx {

// ===================================================================

void OperatorProto::InitAsDefaultInstance() {
}
class OperatorProto::_Internal {
 public:
  using HasBits = decltype(std::declval<OperatorProto>()._has_bits_);
  static void set_has_op_type(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_since_version(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_status(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_doc_string(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

OperatorProto::OperatorProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.OperatorProto)
}
OperatorProto::OperatorProto(const OperatorProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  op_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_op_type()) {
    op_type_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.op_type_);
  }
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_doc_string()) {
    doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
  }
  ::memcpy(&since_version_, &from.since_version_,
    static_cast<size_t>(reinterpret_cast<char*>(&status_) -
    reinterpret_cast<char*>(&since_version_)) + sizeof(status_));
  // @@protoc_insertion_point(copy_constructor:onnx.OperatorProto)
}

void OperatorProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_OperatorProto_onnx_2doperators_2dml_2eproto.base);
  op_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&since_version_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&status_) -
      reinterpret_cast<char*>(&since_version_)) + sizeof(status_));
}

OperatorProto::~OperatorProto() {
  // @@protoc_insertion_point(destructor:onnx.OperatorProto)
  SharedDtor();
}

void OperatorProto::SharedDtor() {
  op_type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void OperatorProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const OperatorProto& OperatorProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_OperatorProto_onnx_2doperators_2dml_2eproto.base);
  return *internal_default_instance();
}


void OperatorProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.OperatorProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      op_type_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      doc_string_.ClearNonDefaultToEmptyNoArena();
    }
  }
  if (cached_has_bits & 0x0000000cu) {
    ::memset(&since_version_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&status_) -
        reinterpret_cast<char*>(&since_version_)) + sizeof(status_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* OperatorProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string op_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_op_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.OperatorProto.op_type");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int64 since_version = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_since_version(&has_bits);
          since_version_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.OperatorStatus status = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::onnx::OperatorStatus_IsValid(val))) {
            _internal_set_status(static_cast<::onnx::OperatorStatus>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(3, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string doc_string = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_doc_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.OperatorProto.doc_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OperatorProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.OperatorProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string op_type = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_op_type().data(), static_cast<int>(this->_internal_op_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.OperatorProto.op_type");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_op_type(), target);
  }

  // optional int64 since_version = 2;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_since_version(), target);
  }

  // optional .onnx.OperatorStatus status = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_status(), target);
  }

  // optional string doc_string = 10;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_doc_string().data(), static_cast<int>(this->_internal_doc_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.OperatorProto.doc_string");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_doc_string(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.OperatorProto)
  return target;
}

size_t OperatorProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.OperatorProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional string op_type = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_op_type());
    }

    // optional string doc_string = 10;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_doc_string());
    }

    // optional int64 since_version = 2;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_since_version());
    }

    // optional .onnx.OperatorStatus status = 3;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OperatorProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.OperatorProto)
  GOOGLE_DCHECK_NE(&from, this);
  const OperatorProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<OperatorProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.OperatorProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.OperatorProto)
    MergeFrom(*source);
  }
}

void OperatorProto::MergeFrom(const OperatorProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.OperatorProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      op_type_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.op_type_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
    }
    if (cached_has_bits & 0x00000004u) {
      since_version_ = from.since_version_;
    }
    if (cached_has_bits & 0x00000008u) {
      status_ = from.status_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void OperatorProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.OperatorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OperatorProto::CopyFrom(const OperatorProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.OperatorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OperatorProto::IsInitialized() const {
  return true;
}

void OperatorProto::InternalSwap(OperatorProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  op_type_.Swap(&other->op_type_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  doc_string_.Swap(&other->doc_string_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(since_version_, other->since_version_);
  swap(status_, other->status_);
}

::PROTOBUF_NAMESPACE_ID::Metadata OperatorProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void OperatorSetProto::InitAsDefaultInstance() {
}
class OperatorSetProto::_Internal {
 public:
  using HasBits = decltype(std::declval<OperatorSetProto>()._has_bits_);
  static void set_has_magic(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_ir_version(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_ir_version_prerelease(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_ir_build_metadata(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_domain(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_opset_version(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_doc_string(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
};

void OperatorSetProto::clear_functions() {
  functions_.Clear();
}
OperatorSetProto::OperatorSetProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.OperatorSetProto)
}
OperatorSetProto::OperatorSetProto(const OperatorSetProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      operator__(from.operator__),
      functions_(from.functions_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  magic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_magic()) {
    magic_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.magic_);
  }
  ir_version_prerelease_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_ir_version_prerelease()) {
    ir_version_prerelease_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.ir_version_prerelease_);
  }
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_domain()) {
    domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
  }
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_doc_string()) {
    doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
  }
  ir_build_metadata_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_ir_build_metadata()) {
    ir_build_metadata_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.ir_build_metadata_);
  }
  ::memcpy(&ir_version_, &from.ir_version_,
    static_cast<size_t>(reinterpret_cast<char*>(&opset_version_) -
    reinterpret_cast<char*>(&ir_version_)) + sizeof(opset_version_));
  // @@protoc_insertion_point(copy_constructor:onnx.OperatorSetProto)
}

void OperatorSetProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_OperatorSetProto_onnx_2doperators_2dml_2eproto.base);
  magic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ir_version_prerelease_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ir_build_metadata_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&ir_version_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&opset_version_) -
      reinterpret_cast<char*>(&ir_version_)) + sizeof(opset_version_));
}

OperatorSetProto::~OperatorSetProto() {
  // @@protoc_insertion_point(destructor:onnx.OperatorSetProto)
  SharedDtor();
}

void OperatorSetProto::SharedDtor() {
  magic_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ir_version_prerelease_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  domain_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ir_build_metadata_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void OperatorSetProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const OperatorSetProto& OperatorSetProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_OperatorSetProto_onnx_2doperators_2dml_2eproto.base);
  return *internal_default_instance();
}


void OperatorSetProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.OperatorSetProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  operator__.Clear();
  functions_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      magic_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      ir_version_prerelease_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000004u) {
      domain_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000008u) {
      doc_string_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000010u) {
      ir_build_metadata_.ClearNonDefaultToEmptyNoArena();
    }
  }
  if (cached_has_bits & 0x00000060u) {
    ::memset(&ir_version_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&opset_version_) -
        reinterpret_cast<char*>(&ir_version_)) + sizeof(opset_version_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* OperatorSetProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string magic = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_magic();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.OperatorSetProto.magic");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int64 ir_version = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_ir_version(&has_bits);
          ir_version_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string ir_version_prerelease = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_ir_version_prerelease();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.OperatorSetProto.ir_version_prerelease");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string domain = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_domain();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.OperatorSetProto.domain");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int64 opset_version = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_opset_version(&has_bits);
          opset_version_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string doc_string = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_doc_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.OperatorSetProto.doc_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string ir_build_metadata = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_ir_build_metadata();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.OperatorSetProto.ir_build_metadata");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.OperatorProto operator = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_operator_(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.FunctionProto functions = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_functions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OperatorSetProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.OperatorSetProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string magic = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_magic().data(), static_cast<int>(this->_internal_magic().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.OperatorSetProto.magic");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_magic(), target);
  }

  // optional int64 ir_version = 2;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_ir_version(), target);
  }

  // optional string ir_version_prerelease = 3;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_ir_version_prerelease().data(), static_cast<int>(this->_internal_ir_version_prerelease().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.OperatorSetProto.ir_version_prerelease");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_ir_version_prerelease(), target);
  }

  // optional string domain = 4;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_domain().data(), static_cast<int>(this->_internal_domain().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.OperatorSetProto.domain");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_domain(), target);
  }

  // optional int64 opset_version = 5;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(5, this->_internal_opset_version(), target);
  }

  // optional string doc_string = 6;
  if (cached_has_bits & 0x00000008u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_doc_string().data(), static_cast<int>(this->_internal_doc_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.OperatorSetProto.doc_string");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_doc_string(), target);
  }

  // optional string ir_build_metadata = 7;
  if (cached_has_bits & 0x00000010u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_ir_build_metadata().data(), static_cast<int>(this->_internal_ir_build_metadata().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.OperatorSetProto.ir_build_metadata");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_ir_build_metadata(), target);
  }

  // repeated .onnx.OperatorProto operator = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_operator__size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(8, this->_internal_operator_(i), target, stream);
  }

  // repeated .onnx.FunctionProto functions = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_functions_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(9, this->_internal_functions(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.OperatorSetProto)
  return target;
}

size_t OperatorSetProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.OperatorSetProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .onnx.OperatorProto operator = 8;
  total_size += 1UL * this->_internal_operator__size();
  for (const auto& msg : this->operator__) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.FunctionProto functions = 9;
  total_size += 1UL * this->_internal_functions_size();
  for (const auto& msg : this->functions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    // optional string magic = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_magic());
    }

    // optional string ir_version_prerelease = 3;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_ir_version_prerelease());
    }

    // optional string domain = 4;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_domain());
    }

    // optional string doc_string = 6;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_doc_string());
    }

    // optional string ir_build_metadata = 7;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_ir_build_metadata());
    }

    // optional int64 ir_version = 2;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_ir_version());
    }

    // optional int64 opset_version = 5;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_opset_version());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OperatorSetProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.OperatorSetProto)
  GOOGLE_DCHECK_NE(&from, this);
  const OperatorSetProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<OperatorSetProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.OperatorSetProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.OperatorSetProto)
    MergeFrom(*source);
  }
}

void OperatorSetProto::MergeFrom(const OperatorSetProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.OperatorSetProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  operator__.MergeFrom(from.operator__);
  functions_.MergeFrom(from.functions_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      magic_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.magic_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      ir_version_prerelease_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.ir_version_prerelease_);
    }
    if (cached_has_bits & 0x00000004u) {
      _has_bits_[0] |= 0x00000004u;
      domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
    }
    if (cached_has_bits & 0x00000008u) {
      _has_bits_[0] |= 0x00000008u;
      doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
    }
    if (cached_has_bits & 0x00000010u) {
      _has_bits_[0] |= 0x00000010u;
      ir_build_metadata_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.ir_build_metadata_);
    }
    if (cached_has_bits & 0x00000020u) {
      ir_version_ = from.ir_version_;
    }
    if (cached_has_bits & 0x00000040u) {
      opset_version_ = from.opset_version_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void OperatorSetProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.OperatorSetProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OperatorSetProto::CopyFrom(const OperatorSetProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.OperatorSetProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OperatorSetProto::IsInitialized() const {
  return true;
}

void OperatorSetProto::InternalSwap(OperatorSetProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  operator__.InternalSwap(&other->operator__);
  functions_.InternalSwap(&other->functions_);
  magic_.Swap(&other->magic_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  ir_version_prerelease_.Swap(&other->ir_version_prerelease_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  domain_.Swap(&other->domain_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  doc_string_.Swap(&other->doc_string_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  ir_build_metadata_.Swap(&other->ir_build_metadata_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(ir_version_, other->ir_version_);
  swap(opset_version_, other->opset_version_);
}

::PROTOBUF_NAMESPACE_ID::Metadata OperatorSetProto::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace onnx
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::onnx::OperatorProto* Arena::CreateMaybeMessage< ::onnx::OperatorProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::OperatorProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::OperatorSetProto* Arena::CreateMaybeMessage< ::onnx::OperatorSetProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::OperatorSetProto >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

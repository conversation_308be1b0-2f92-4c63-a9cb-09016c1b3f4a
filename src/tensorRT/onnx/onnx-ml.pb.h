// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: onnx-ml.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_onnx_2dml_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_onnx_2dml_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3011000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3011004 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_onnx_2dml_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[22]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_onnx_2dml_2eproto;
namespace onnx {
class AttributeProto;
class AttributeProtoDefaultTypeInternal;
extern AttributeProtoDefaultTypeInternal _AttributeProto_default_instance_;
class FunctionProto;
class FunctionProtoDefaultTypeInternal;
extern FunctionProtoDefaultTypeInternal _FunctionProto_default_instance_;
class GraphProto;
class GraphProtoDefaultTypeInternal;
extern GraphProtoDefaultTypeInternal _GraphProto_default_instance_;
class ModelProto;
class ModelProtoDefaultTypeInternal;
extern ModelProtoDefaultTypeInternal _ModelProto_default_instance_;
class NodeProto;
class NodeProtoDefaultTypeInternal;
extern NodeProtoDefaultTypeInternal _NodeProto_default_instance_;
class OperatorSetIdProto;
class OperatorSetIdProtoDefaultTypeInternal;
extern OperatorSetIdProtoDefaultTypeInternal _OperatorSetIdProto_default_instance_;
class SparseTensorProto;
class SparseTensorProtoDefaultTypeInternal;
extern SparseTensorProtoDefaultTypeInternal _SparseTensorProto_default_instance_;
class StringStringEntryProto;
class StringStringEntryProtoDefaultTypeInternal;
extern StringStringEntryProtoDefaultTypeInternal _StringStringEntryProto_default_instance_;
class TensorAnnotation;
class TensorAnnotationDefaultTypeInternal;
extern TensorAnnotationDefaultTypeInternal _TensorAnnotation_default_instance_;
class TensorProto;
class TensorProtoDefaultTypeInternal;
extern TensorProtoDefaultTypeInternal _TensorProto_default_instance_;
class TensorProto_Segment;
class TensorProto_SegmentDefaultTypeInternal;
extern TensorProto_SegmentDefaultTypeInternal _TensorProto_Segment_default_instance_;
class TensorShapeProto;
class TensorShapeProtoDefaultTypeInternal;
extern TensorShapeProtoDefaultTypeInternal _TensorShapeProto_default_instance_;
class TensorShapeProto_Dimension;
class TensorShapeProto_DimensionDefaultTypeInternal;
extern TensorShapeProto_DimensionDefaultTypeInternal _TensorShapeProto_Dimension_default_instance_;
class TrainingInfoProto;
class TrainingInfoProtoDefaultTypeInternal;
extern TrainingInfoProtoDefaultTypeInternal _TrainingInfoProto_default_instance_;
class TypeProto;
class TypeProtoDefaultTypeInternal;
extern TypeProtoDefaultTypeInternal _TypeProto_default_instance_;
class TypeProto_Map;
class TypeProto_MapDefaultTypeInternal;
extern TypeProto_MapDefaultTypeInternal _TypeProto_Map_default_instance_;
class TypeProto_Opaque;
class TypeProto_OpaqueDefaultTypeInternal;
extern TypeProto_OpaqueDefaultTypeInternal _TypeProto_Opaque_default_instance_;
class TypeProto_Optional;
class TypeProto_OptionalDefaultTypeInternal;
extern TypeProto_OptionalDefaultTypeInternal _TypeProto_Optional_default_instance_;
class TypeProto_Sequence;
class TypeProto_SequenceDefaultTypeInternal;
extern TypeProto_SequenceDefaultTypeInternal _TypeProto_Sequence_default_instance_;
class TypeProto_SparseTensor;
class TypeProto_SparseTensorDefaultTypeInternal;
extern TypeProto_SparseTensorDefaultTypeInternal _TypeProto_SparseTensor_default_instance_;
class TypeProto_Tensor;
class TypeProto_TensorDefaultTypeInternal;
extern TypeProto_TensorDefaultTypeInternal _TypeProto_Tensor_default_instance_;
class ValueInfoProto;
class ValueInfoProtoDefaultTypeInternal;
extern ValueInfoProtoDefaultTypeInternal _ValueInfoProto_default_instance_;
}  // namespace onnx
PROTOBUF_NAMESPACE_OPEN
template<> ::onnx::AttributeProto* Arena::CreateMaybeMessage<::onnx::AttributeProto>(Arena*);
template<> ::onnx::FunctionProto* Arena::CreateMaybeMessage<::onnx::FunctionProto>(Arena*);
template<> ::onnx::GraphProto* Arena::CreateMaybeMessage<::onnx::GraphProto>(Arena*);
template<> ::onnx::ModelProto* Arena::CreateMaybeMessage<::onnx::ModelProto>(Arena*);
template<> ::onnx::NodeProto* Arena::CreateMaybeMessage<::onnx::NodeProto>(Arena*);
template<> ::onnx::OperatorSetIdProto* Arena::CreateMaybeMessage<::onnx::OperatorSetIdProto>(Arena*);
template<> ::onnx::SparseTensorProto* Arena::CreateMaybeMessage<::onnx::SparseTensorProto>(Arena*);
template<> ::onnx::StringStringEntryProto* Arena::CreateMaybeMessage<::onnx::StringStringEntryProto>(Arena*);
template<> ::onnx::TensorAnnotation* Arena::CreateMaybeMessage<::onnx::TensorAnnotation>(Arena*);
template<> ::onnx::TensorProto* Arena::CreateMaybeMessage<::onnx::TensorProto>(Arena*);
template<> ::onnx::TensorProto_Segment* Arena::CreateMaybeMessage<::onnx::TensorProto_Segment>(Arena*);
template<> ::onnx::TensorShapeProto* Arena::CreateMaybeMessage<::onnx::TensorShapeProto>(Arena*);
template<> ::onnx::TensorShapeProto_Dimension* Arena::CreateMaybeMessage<::onnx::TensorShapeProto_Dimension>(Arena*);
template<> ::onnx::TrainingInfoProto* Arena::CreateMaybeMessage<::onnx::TrainingInfoProto>(Arena*);
template<> ::onnx::TypeProto* Arena::CreateMaybeMessage<::onnx::TypeProto>(Arena*);
template<> ::onnx::TypeProto_Map* Arena::CreateMaybeMessage<::onnx::TypeProto_Map>(Arena*);
template<> ::onnx::TypeProto_Opaque* Arena::CreateMaybeMessage<::onnx::TypeProto_Opaque>(Arena*);
template<> ::onnx::TypeProto_Optional* Arena::CreateMaybeMessage<::onnx::TypeProto_Optional>(Arena*);
template<> ::onnx::TypeProto_Sequence* Arena::CreateMaybeMessage<::onnx::TypeProto_Sequence>(Arena*);
template<> ::onnx::TypeProto_SparseTensor* Arena::CreateMaybeMessage<::onnx::TypeProto_SparseTensor>(Arena*);
template<> ::onnx::TypeProto_Tensor* Arena::CreateMaybeMessage<::onnx::TypeProto_Tensor>(Arena*);
template<> ::onnx::ValueInfoProto* Arena::CreateMaybeMessage<::onnx::ValueInfoProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace onnx {

enum AttributeProto_AttributeType : int {
  AttributeProto_AttributeType_UNDEFINED = 0,
  AttributeProto_AttributeType_FLOAT = 1,
  AttributeProto_AttributeType_INT = 2,
  AttributeProto_AttributeType_STRING = 3,
  AttributeProto_AttributeType_TENSOR = 4,
  AttributeProto_AttributeType_GRAPH = 5,
  AttributeProto_AttributeType_SPARSE_TENSOR = 11,
  AttributeProto_AttributeType_TYPE_PROTO = 13,
  AttributeProto_AttributeType_FLOATS = 6,
  AttributeProto_AttributeType_INTS = 7,
  AttributeProto_AttributeType_STRINGS = 8,
  AttributeProto_AttributeType_TENSORS = 9,
  AttributeProto_AttributeType_GRAPHS = 10,
  AttributeProto_AttributeType_SPARSE_TENSORS = 12,
  AttributeProto_AttributeType_TYPE_PROTOS = 14
};
bool AttributeProto_AttributeType_IsValid(int value);
constexpr AttributeProto_AttributeType AttributeProto_AttributeType_AttributeType_MIN = AttributeProto_AttributeType_UNDEFINED;
constexpr AttributeProto_AttributeType AttributeProto_AttributeType_AttributeType_MAX = AttributeProto_AttributeType_TYPE_PROTOS;
constexpr int AttributeProto_AttributeType_AttributeType_ARRAYSIZE = AttributeProto_AttributeType_AttributeType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AttributeProto_AttributeType_descriptor();
template<typename T>
inline const std::string& AttributeProto_AttributeType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AttributeProto_AttributeType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AttributeProto_AttributeType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AttributeProto_AttributeType_descriptor(), enum_t_value);
}
inline bool AttributeProto_AttributeType_Parse(
    const std::string& name, AttributeProto_AttributeType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AttributeProto_AttributeType>(
    AttributeProto_AttributeType_descriptor(), name, value);
}
enum TensorProto_DataType : int {
  TensorProto_DataType_UNDEFINED = 0,
  TensorProto_DataType_FLOAT = 1,
  TensorProto_DataType_UINT8 = 2,
  TensorProto_DataType_INT8 = 3,
  TensorProto_DataType_UINT16 = 4,
  TensorProto_DataType_INT16 = 5,
  TensorProto_DataType_INT32 = 6,
  TensorProto_DataType_INT64 = 7,
  TensorProto_DataType_STRING = 8,
  TensorProto_DataType_BOOL = 9,
  TensorProto_DataType_FLOAT16 = 10,
  TensorProto_DataType_DOUBLE = 11,
  TensorProto_DataType_UINT32 = 12,
  TensorProto_DataType_UINT64 = 13,
  TensorProto_DataType_COMPLEX64 = 14,
  TensorProto_DataType_COMPLEX128 = 15,
  TensorProto_DataType_BFLOAT16 = 16
};
bool TensorProto_DataType_IsValid(int value);
constexpr TensorProto_DataType TensorProto_DataType_DataType_MIN = TensorProto_DataType_UNDEFINED;
constexpr TensorProto_DataType TensorProto_DataType_DataType_MAX = TensorProto_DataType_BFLOAT16;
constexpr int TensorProto_DataType_DataType_ARRAYSIZE = TensorProto_DataType_DataType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TensorProto_DataType_descriptor();
template<typename T>
inline const std::string& TensorProto_DataType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TensorProto_DataType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TensorProto_DataType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TensorProto_DataType_descriptor(), enum_t_value);
}
inline bool TensorProto_DataType_Parse(
    const std::string& name, TensorProto_DataType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TensorProto_DataType>(
    TensorProto_DataType_descriptor(), name, value);
}
enum TensorProto_DataLocation : int {
  TensorProto_DataLocation_DEFAULT = 0,
  TensorProto_DataLocation_EXTERNAL = 1
};
bool TensorProto_DataLocation_IsValid(int value);
constexpr TensorProto_DataLocation TensorProto_DataLocation_DataLocation_MIN = TensorProto_DataLocation_DEFAULT;
constexpr TensorProto_DataLocation TensorProto_DataLocation_DataLocation_MAX = TensorProto_DataLocation_EXTERNAL;
constexpr int TensorProto_DataLocation_DataLocation_ARRAYSIZE = TensorProto_DataLocation_DataLocation_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TensorProto_DataLocation_descriptor();
template<typename T>
inline const std::string& TensorProto_DataLocation_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TensorProto_DataLocation>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TensorProto_DataLocation_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TensorProto_DataLocation_descriptor(), enum_t_value);
}
inline bool TensorProto_DataLocation_Parse(
    const std::string& name, TensorProto_DataLocation* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TensorProto_DataLocation>(
    TensorProto_DataLocation_descriptor(), name, value);
}
enum Version : int {
  _START_VERSION = 0,
  IR_VERSION_2017_10_10 = 1,
  IR_VERSION_2017_10_30 = 2,
  IR_VERSION_2017_11_3 = 3,
  IR_VERSION_2019_1_22 = 4,
  IR_VERSION_2019_3_18 = 5,
  IR_VERSION_2019_9_19 = 6,
  IR_VERSION_2020_5_8 = 7,
  IR_VERSION = 8
};
bool Version_IsValid(int value);
constexpr Version Version_MIN = _START_VERSION;
constexpr Version Version_MAX = IR_VERSION;
constexpr int Version_ARRAYSIZE = Version_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Version_descriptor();
template<typename T>
inline const std::string& Version_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Version>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Version_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Version_descriptor(), enum_t_value);
}
inline bool Version_Parse(
    const std::string& name, Version* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Version>(
    Version_descriptor(), name, value);
}
enum OperatorStatus : int {
  EXPERIMENTAL = 0,
  STABLE = 1
};
bool OperatorStatus_IsValid(int value);
constexpr OperatorStatus OperatorStatus_MIN = EXPERIMENTAL;
constexpr OperatorStatus OperatorStatus_MAX = STABLE;
constexpr int OperatorStatus_ARRAYSIZE = OperatorStatus_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OperatorStatus_descriptor();
template<typename T>
inline const std::string& OperatorStatus_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OperatorStatus>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OperatorStatus_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OperatorStatus_descriptor(), enum_t_value);
}
inline bool OperatorStatus_Parse(
    const std::string& name, OperatorStatus* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OperatorStatus>(
    OperatorStatus_descriptor(), name, value);
}
// ===================================================================

class AttributeProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.AttributeProto) */ {
 public:
  AttributeProto();
  virtual ~AttributeProto();

  AttributeProto(const AttributeProto& from);
  AttributeProto(AttributeProto&& from) noexcept
    : AttributeProto() {
    *this = ::std::move(from);
  }

  inline AttributeProto& operator=(const AttributeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline AttributeProto& operator=(AttributeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AttributeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AttributeProto* internal_default_instance() {
    return reinterpret_cast<const AttributeProto*>(
               &_AttributeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AttributeProto& a, AttributeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(AttributeProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AttributeProto* New() const final {
    return CreateMaybeMessage<AttributeProto>(nullptr);
  }

  AttributeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AttributeProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AttributeProto& from);
  void MergeFrom(const AttributeProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AttributeProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.AttributeProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef AttributeProto_AttributeType AttributeType;
  static constexpr AttributeType UNDEFINED =
    AttributeProto_AttributeType_UNDEFINED;
  static constexpr AttributeType FLOAT =
    AttributeProto_AttributeType_FLOAT;
  static constexpr AttributeType INT =
    AttributeProto_AttributeType_INT;
  static constexpr AttributeType STRING =
    AttributeProto_AttributeType_STRING;
  static constexpr AttributeType TENSOR =
    AttributeProto_AttributeType_TENSOR;
  static constexpr AttributeType GRAPH =
    AttributeProto_AttributeType_GRAPH;
  static constexpr AttributeType SPARSE_TENSOR =
    AttributeProto_AttributeType_SPARSE_TENSOR;
  static constexpr AttributeType TYPE_PROTO =
    AttributeProto_AttributeType_TYPE_PROTO;
  static constexpr AttributeType FLOATS =
    AttributeProto_AttributeType_FLOATS;
  static constexpr AttributeType INTS =
    AttributeProto_AttributeType_INTS;
  static constexpr AttributeType STRINGS =
    AttributeProto_AttributeType_STRINGS;
  static constexpr AttributeType TENSORS =
    AttributeProto_AttributeType_TENSORS;
  static constexpr AttributeType GRAPHS =
    AttributeProto_AttributeType_GRAPHS;
  static constexpr AttributeType SPARSE_TENSORS =
    AttributeProto_AttributeType_SPARSE_TENSORS;
  static constexpr AttributeType TYPE_PROTOS =
    AttributeProto_AttributeType_TYPE_PROTOS;
  static inline bool AttributeType_IsValid(int value) {
    return AttributeProto_AttributeType_IsValid(value);
  }
  static constexpr AttributeType AttributeType_MIN =
    AttributeProto_AttributeType_AttributeType_MIN;
  static constexpr AttributeType AttributeType_MAX =
    AttributeProto_AttributeType_AttributeType_MAX;
  static constexpr int AttributeType_ARRAYSIZE =
    AttributeProto_AttributeType_AttributeType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  AttributeType_descriptor() {
    return AttributeProto_AttributeType_descriptor();
  }
  template<typename T>
  static inline const std::string& AttributeType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, AttributeType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function AttributeType_Name.");
    return AttributeProto_AttributeType_Name(enum_t_value);
  }
  static inline bool AttributeType_Parse(const std::string& name,
      AttributeType* value) {
    return AttributeProto_AttributeType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFloatsFieldNumber = 7,
    kIntsFieldNumber = 8,
    kStringsFieldNumber = 9,
    kTensorsFieldNumber = 10,
    kGraphsFieldNumber = 11,
    kTypeProtosFieldNumber = 15,
    kSparseTensorsFieldNumber = 23,
    kNameFieldNumber = 1,
    kSFieldNumber = 4,
    kDocStringFieldNumber = 13,
    kRefAttrNameFieldNumber = 21,
    kTFieldNumber = 5,
    kGFieldNumber = 6,
    kTpFieldNumber = 14,
    kSparseTensorFieldNumber = 22,
    kIFieldNumber = 3,
    kFFieldNumber = 2,
    kTypeFieldNumber = 20,
  };
  // repeated float floats = 7;
  int floats_size() const;
  private:
  int _internal_floats_size() const;
  public:
  void clear_floats();
  private:
  float _internal_floats(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_floats() const;
  void _internal_add_floats(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_floats();
  public:
  float floats(int index) const;
  void set_floats(int index, float value);
  void add_floats(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      floats() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_floats();

  // repeated int64 ints = 8;
  int ints_size() const;
  private:
  int _internal_ints_size() const;
  public:
  void clear_ints();
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_ints(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      _internal_ints() const;
  void _internal_add_ints(::PROTOBUF_NAMESPACE_ID::int64 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      _internal_mutable_ints();
  public:
  ::PROTOBUF_NAMESPACE_ID::int64 ints(int index) const;
  void set_ints(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_ints(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      ints() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_ints();

  // repeated bytes strings = 9;
  int strings_size() const;
  private:
  int _internal_strings_size() const;
  public:
  void clear_strings();
  const std::string& strings(int index) const;
  std::string* mutable_strings(int index);
  void set_strings(int index, const std::string& value);
  void set_strings(int index, std::string&& value);
  void set_strings(int index, const char* value);
  void set_strings(int index, const void* value, size_t size);
  std::string* add_strings();
  void add_strings(const std::string& value);
  void add_strings(std::string&& value);
  void add_strings(const char* value);
  void add_strings(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& strings() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_strings();
  private:
  const std::string& _internal_strings(int index) const;
  std::string* _internal_add_strings();
  public:

  // repeated .onnx.TensorProto tensors = 10;
  int tensors_size() const;
  private:
  int _internal_tensors_size() const;
  public:
  void clear_tensors();
  ::onnx::TensorProto* mutable_tensors(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto >*
      mutable_tensors();
  private:
  const ::onnx::TensorProto& _internal_tensors(int index) const;
  ::onnx::TensorProto* _internal_add_tensors();
  public:
  const ::onnx::TensorProto& tensors(int index) const;
  ::onnx::TensorProto* add_tensors();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto >&
      tensors() const;

  // repeated .onnx.GraphProto graphs = 11;
  int graphs_size() const;
  private:
  int _internal_graphs_size() const;
  public:
  void clear_graphs();
  ::onnx::GraphProto* mutable_graphs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::GraphProto >*
      mutable_graphs();
  private:
  const ::onnx::GraphProto& _internal_graphs(int index) const;
  ::onnx::GraphProto* _internal_add_graphs();
  public:
  const ::onnx::GraphProto& graphs(int index) const;
  ::onnx::GraphProto* add_graphs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::GraphProto >&
      graphs() const;

  // repeated .onnx.TypeProto type_protos = 15;
  int type_protos_size() const;
  private:
  int _internal_type_protos_size() const;
  public:
  void clear_type_protos();
  ::onnx::TypeProto* mutable_type_protos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TypeProto >*
      mutable_type_protos();
  private:
  const ::onnx::TypeProto& _internal_type_protos(int index) const;
  ::onnx::TypeProto* _internal_add_type_protos();
  public:
  const ::onnx::TypeProto& type_protos(int index) const;
  ::onnx::TypeProto* add_type_protos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TypeProto >&
      type_protos() const;

  // repeated .onnx.SparseTensorProto sparse_tensors = 23;
  int sparse_tensors_size() const;
  private:
  int _internal_sparse_tensors_size() const;
  public:
  void clear_sparse_tensors();
  ::onnx::SparseTensorProto* mutable_sparse_tensors(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto >*
      mutable_sparse_tensors();
  private:
  const ::onnx::SparseTensorProto& _internal_sparse_tensors(int index) const;
  ::onnx::SparseTensorProto* _internal_add_sparse_tensors();
  public:
  const ::onnx::SparseTensorProto& sparse_tensors(int index) const;
  ::onnx::SparseTensorProto* add_sparse_tensors();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto >&
      sparse_tensors() const;

  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional bytes s = 4;
  bool has_s() const;
  private:
  bool _internal_has_s() const;
  public:
  void clear_s();
  const std::string& s() const;
  void set_s(const std::string& value);
  void set_s(std::string&& value);
  void set_s(const char* value);
  void set_s(const void* value, size_t size);
  std::string* mutable_s();
  std::string* release_s();
  void set_allocated_s(std::string* s);
  private:
  const std::string& _internal_s() const;
  void _internal_set_s(const std::string& value);
  std::string* _internal_mutable_s();
  public:

  // optional string doc_string = 13;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  void set_doc_string(const std::string& value);
  void set_doc_string(std::string&& value);
  void set_doc_string(const char* value);
  void set_doc_string(const char* value, size_t size);
  std::string* mutable_doc_string();
  std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional string ref_attr_name = 21;
  bool has_ref_attr_name() const;
  private:
  bool _internal_has_ref_attr_name() const;
  public:
  void clear_ref_attr_name();
  const std::string& ref_attr_name() const;
  void set_ref_attr_name(const std::string& value);
  void set_ref_attr_name(std::string&& value);
  void set_ref_attr_name(const char* value);
  void set_ref_attr_name(const char* value, size_t size);
  std::string* mutable_ref_attr_name();
  std::string* release_ref_attr_name();
  void set_allocated_ref_attr_name(std::string* ref_attr_name);
  private:
  const std::string& _internal_ref_attr_name() const;
  void _internal_set_ref_attr_name(const std::string& value);
  std::string* _internal_mutable_ref_attr_name();
  public:

  // optional .onnx.TensorProto t = 5;
  bool has_t() const;
  private:
  bool _internal_has_t() const;
  public:
  void clear_t();
  const ::onnx::TensorProto& t() const;
  ::onnx::TensorProto* release_t();
  ::onnx::TensorProto* mutable_t();
  void set_allocated_t(::onnx::TensorProto* t);
  private:
  const ::onnx::TensorProto& _internal_t() const;
  ::onnx::TensorProto* _internal_mutable_t();
  public:

  // optional .onnx.GraphProto g = 6;
  bool has_g() const;
  private:
  bool _internal_has_g() const;
  public:
  void clear_g();
  const ::onnx::GraphProto& g() const;
  ::onnx::GraphProto* release_g();
  ::onnx::GraphProto* mutable_g();
  void set_allocated_g(::onnx::GraphProto* g);
  private:
  const ::onnx::GraphProto& _internal_g() const;
  ::onnx::GraphProto* _internal_mutable_g();
  public:

  // optional .onnx.TypeProto tp = 14;
  bool has_tp() const;
  private:
  bool _internal_has_tp() const;
  public:
  void clear_tp();
  const ::onnx::TypeProto& tp() const;
  ::onnx::TypeProto* release_tp();
  ::onnx::TypeProto* mutable_tp();
  void set_allocated_tp(::onnx::TypeProto* tp);
  private:
  const ::onnx::TypeProto& _internal_tp() const;
  ::onnx::TypeProto* _internal_mutable_tp();
  public:

  // optional .onnx.SparseTensorProto sparse_tensor = 22;
  bool has_sparse_tensor() const;
  private:
  bool _internal_has_sparse_tensor() const;
  public:
  void clear_sparse_tensor();
  const ::onnx::SparseTensorProto& sparse_tensor() const;
  ::onnx::SparseTensorProto* release_sparse_tensor();
  ::onnx::SparseTensorProto* mutable_sparse_tensor();
  void set_allocated_sparse_tensor(::onnx::SparseTensorProto* sparse_tensor);
  private:
  const ::onnx::SparseTensorProto& _internal_sparse_tensor() const;
  ::onnx::SparseTensorProto* _internal_mutable_sparse_tensor();
  public:

  // optional int64 i = 3;
  bool has_i() const;
  private:
  bool _internal_has_i() const;
  public:
  void clear_i();
  ::PROTOBUF_NAMESPACE_ID::int64 i() const;
  void set_i(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_i() const;
  void _internal_set_i(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // optional float f = 2;
  bool has_f() const;
  private:
  bool _internal_has_f() const;
  public:
  void clear_f();
  float f() const;
  void set_f(float value);
  private:
  float _internal_f() const;
  void _internal_set_f(float value);
  public:

  // optional .onnx.AttributeProto.AttributeType type = 20;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  ::onnx::AttributeProto_AttributeType type() const;
  void set_type(::onnx::AttributeProto_AttributeType value);
  private:
  ::onnx::AttributeProto_AttributeType _internal_type() const;
  void _internal_set_type(::onnx::AttributeProto_AttributeType value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.AttributeProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > floats_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > ints_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> strings_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto > tensors_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::GraphProto > graphs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TypeProto > type_protos_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto > sparse_tensors_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr s_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ref_attr_name_;
  ::onnx::TensorProto* t_;
  ::onnx::GraphProto* g_;
  ::onnx::TypeProto* tp_;
  ::onnx::SparseTensorProto* sparse_tensor_;
  ::PROTOBUF_NAMESPACE_ID::int64 i_;
  float f_;
  int type_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class ValueInfoProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.ValueInfoProto) */ {
 public:
  ValueInfoProto();
  virtual ~ValueInfoProto();

  ValueInfoProto(const ValueInfoProto& from);
  ValueInfoProto(ValueInfoProto&& from) noexcept
    : ValueInfoProto() {
    *this = ::std::move(from);
  }

  inline ValueInfoProto& operator=(const ValueInfoProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ValueInfoProto& operator=(ValueInfoProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ValueInfoProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ValueInfoProto* internal_default_instance() {
    return reinterpret_cast<const ValueInfoProto*>(
               &_ValueInfoProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ValueInfoProto& a, ValueInfoProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ValueInfoProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ValueInfoProto* New() const final {
    return CreateMaybeMessage<ValueInfoProto>(nullptr);
  }

  ValueInfoProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ValueInfoProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ValueInfoProto& from);
  void MergeFrom(const ValueInfoProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ValueInfoProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.ValueInfoProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kDocStringFieldNumber = 3,
    kTypeFieldNumber = 2,
  };
  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional string doc_string = 3;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  void set_doc_string(const std::string& value);
  void set_doc_string(std::string&& value);
  void set_doc_string(const char* value);
  void set_doc_string(const char* value, size_t size);
  std::string* mutable_doc_string();
  std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional .onnx.TypeProto type = 2;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  const ::onnx::TypeProto& type() const;
  ::onnx::TypeProto* release_type();
  ::onnx::TypeProto* mutable_type();
  void set_allocated_type(::onnx::TypeProto* type);
  private:
  const ::onnx::TypeProto& _internal_type() const;
  ::onnx::TypeProto* _internal_mutable_type();
  public:

  // @@protoc_insertion_point(class_scope:onnx.ValueInfoProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::onnx::TypeProto* type_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class NodeProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.NodeProto) */ {
 public:
  NodeProto();
  virtual ~NodeProto();

  NodeProto(const NodeProto& from);
  NodeProto(NodeProto&& from) noexcept
    : NodeProto() {
    *this = ::std::move(from);
  }

  inline NodeProto& operator=(const NodeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeProto& operator=(NodeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NodeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NodeProto* internal_default_instance() {
    return reinterpret_cast<const NodeProto*>(
               &_NodeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(NodeProto& a, NodeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NodeProto* New() const final {
    return CreateMaybeMessage<NodeProto>(nullptr);
  }

  NodeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NodeProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NodeProto& from);
  void MergeFrom(const NodeProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.NodeProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputFieldNumber = 1,
    kOutputFieldNumber = 2,
    kAttributeFieldNumber = 5,
    kNameFieldNumber = 3,
    kOpTypeFieldNumber = 4,
    kDocStringFieldNumber = 6,
    kDomainFieldNumber = 7,
  };
  // repeated string input = 1;
  int input_size() const;
  private:
  int _internal_input_size() const;
  public:
  void clear_input();
  const std::string& input(int index) const;
  std::string* mutable_input(int index);
  void set_input(int index, const std::string& value);
  void set_input(int index, std::string&& value);
  void set_input(int index, const char* value);
  void set_input(int index, const char* value, size_t size);
  std::string* add_input();
  void add_input(const std::string& value);
  void add_input(std::string&& value);
  void add_input(const char* value);
  void add_input(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& input() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_input();
  private:
  const std::string& _internal_input(int index) const;
  std::string* _internal_add_input();
  public:

  // repeated string output = 2;
  int output_size() const;
  private:
  int _internal_output_size() const;
  public:
  void clear_output();
  const std::string& output(int index) const;
  std::string* mutable_output(int index);
  void set_output(int index, const std::string& value);
  void set_output(int index, std::string&& value);
  void set_output(int index, const char* value);
  void set_output(int index, const char* value, size_t size);
  std::string* add_output();
  void add_output(const std::string& value);
  void add_output(std::string&& value);
  void add_output(const char* value);
  void add_output(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& output() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_output();
  private:
  const std::string& _internal_output(int index) const;
  std::string* _internal_add_output();
  public:

  // repeated .onnx.AttributeProto attribute = 5;
  int attribute_size() const;
  private:
  int _internal_attribute_size() const;
  public:
  void clear_attribute();
  ::onnx::AttributeProto* mutable_attribute(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::AttributeProto >*
      mutable_attribute();
  private:
  const ::onnx::AttributeProto& _internal_attribute(int index) const;
  ::onnx::AttributeProto* _internal_add_attribute();
  public:
  const ::onnx::AttributeProto& attribute(int index) const;
  ::onnx::AttributeProto* add_attribute();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::AttributeProto >&
      attribute() const;

  // optional string name = 3;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional string op_type = 4;
  bool has_op_type() const;
  private:
  bool _internal_has_op_type() const;
  public:
  void clear_op_type();
  const std::string& op_type() const;
  void set_op_type(const std::string& value);
  void set_op_type(std::string&& value);
  void set_op_type(const char* value);
  void set_op_type(const char* value, size_t size);
  std::string* mutable_op_type();
  std::string* release_op_type();
  void set_allocated_op_type(std::string* op_type);
  private:
  const std::string& _internal_op_type() const;
  void _internal_set_op_type(const std::string& value);
  std::string* _internal_mutable_op_type();
  public:

  // optional string doc_string = 6;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  void set_doc_string(const std::string& value);
  void set_doc_string(std::string&& value);
  void set_doc_string(const char* value);
  void set_doc_string(const char* value, size_t size);
  std::string* mutable_doc_string();
  std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional string domain = 7;
  bool has_domain() const;
  private:
  bool _internal_has_domain() const;
  public:
  void clear_domain();
  const std::string& domain() const;
  void set_domain(const std::string& value);
  void set_domain(std::string&& value);
  void set_domain(const char* value);
  void set_domain(const char* value, size_t size);
  std::string* mutable_domain();
  std::string* release_domain();
  void set_allocated_domain(std::string* domain);
  private:
  const std::string& _internal_domain() const;
  void _internal_set_domain(const std::string& value);
  std::string* _internal_mutable_domain();
  public:

  // @@protoc_insertion_point(class_scope:onnx.NodeProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> input_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> output_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::AttributeProto > attribute_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr domain_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TrainingInfoProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TrainingInfoProto) */ {
 public:
  TrainingInfoProto();
  virtual ~TrainingInfoProto();

  TrainingInfoProto(const TrainingInfoProto& from);
  TrainingInfoProto(TrainingInfoProto&& from) noexcept
    : TrainingInfoProto() {
    *this = ::std::move(from);
  }

  inline TrainingInfoProto& operator=(const TrainingInfoProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrainingInfoProto& operator=(TrainingInfoProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TrainingInfoProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrainingInfoProto* internal_default_instance() {
    return reinterpret_cast<const TrainingInfoProto*>(
               &_TrainingInfoProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TrainingInfoProto& a, TrainingInfoProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TrainingInfoProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TrainingInfoProto* New() const final {
    return CreateMaybeMessage<TrainingInfoProto>(nullptr);
  }

  TrainingInfoProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TrainingInfoProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TrainingInfoProto& from);
  void MergeFrom(const TrainingInfoProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrainingInfoProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TrainingInfoProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInitializationBindingFieldNumber = 3,
    kUpdateBindingFieldNumber = 4,
    kInitializationFieldNumber = 1,
    kAlgorithmFieldNumber = 2,
  };
  // repeated .onnx.StringStringEntryProto initialization_binding = 3;
  int initialization_binding_size() const;
  private:
  int _internal_initialization_binding_size() const;
  public:
  void clear_initialization_binding();
  ::onnx::StringStringEntryProto* mutable_initialization_binding(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
      mutable_initialization_binding();
  private:
  const ::onnx::StringStringEntryProto& _internal_initialization_binding(int index) const;
  ::onnx::StringStringEntryProto* _internal_add_initialization_binding();
  public:
  const ::onnx::StringStringEntryProto& initialization_binding(int index) const;
  ::onnx::StringStringEntryProto* add_initialization_binding();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
      initialization_binding() const;

  // repeated .onnx.StringStringEntryProto update_binding = 4;
  int update_binding_size() const;
  private:
  int _internal_update_binding_size() const;
  public:
  void clear_update_binding();
  ::onnx::StringStringEntryProto* mutable_update_binding(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
      mutable_update_binding();
  private:
  const ::onnx::StringStringEntryProto& _internal_update_binding(int index) const;
  ::onnx::StringStringEntryProto* _internal_add_update_binding();
  public:
  const ::onnx::StringStringEntryProto& update_binding(int index) const;
  ::onnx::StringStringEntryProto* add_update_binding();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
      update_binding() const;

  // optional .onnx.GraphProto initialization = 1;
  bool has_initialization() const;
  private:
  bool _internal_has_initialization() const;
  public:
  void clear_initialization();
  const ::onnx::GraphProto& initialization() const;
  ::onnx::GraphProto* release_initialization();
  ::onnx::GraphProto* mutable_initialization();
  void set_allocated_initialization(::onnx::GraphProto* initialization);
  private:
  const ::onnx::GraphProto& _internal_initialization() const;
  ::onnx::GraphProto* _internal_mutable_initialization();
  public:

  // optional .onnx.GraphProto algorithm = 2;
  bool has_algorithm() const;
  private:
  bool _internal_has_algorithm() const;
  public:
  void clear_algorithm();
  const ::onnx::GraphProto& algorithm() const;
  ::onnx::GraphProto* release_algorithm();
  ::onnx::GraphProto* mutable_algorithm();
  void set_allocated_algorithm(::onnx::GraphProto* algorithm);
  private:
  const ::onnx::GraphProto& _internal_algorithm() const;
  ::onnx::GraphProto* _internal_mutable_algorithm();
  public:

  // @@protoc_insertion_point(class_scope:onnx.TrainingInfoProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto > initialization_binding_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto > update_binding_;
  ::onnx::GraphProto* initialization_;
  ::onnx::GraphProto* algorithm_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class ModelProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.ModelProto) */ {
 public:
  ModelProto();
  virtual ~ModelProto();

  ModelProto(const ModelProto& from);
  ModelProto(ModelProto&& from) noexcept
    : ModelProto() {
    *this = ::std::move(from);
  }

  inline ModelProto& operator=(const ModelProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto& operator=(ModelProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ModelProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ModelProto* internal_default_instance() {
    return reinterpret_cast<const ModelProto*>(
               &_ModelProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ModelProto& a, ModelProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ModelProto* New() const final {
    return CreateMaybeMessage<ModelProto>(nullptr);
  }

  ModelProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ModelProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ModelProto& from);
  void MergeFrom(const ModelProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.ModelProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpsetImportFieldNumber = 8,
    kMetadataPropsFieldNumber = 14,
    kTrainingInfoFieldNumber = 20,
    kFunctionsFieldNumber = 25,
    kProducerNameFieldNumber = 2,
    kProducerVersionFieldNumber = 3,
    kDomainFieldNumber = 4,
    kDocStringFieldNumber = 6,
    kGraphFieldNumber = 7,
    kIrVersionFieldNumber = 1,
    kModelVersionFieldNumber = 5,
  };
  // repeated .onnx.OperatorSetIdProto opset_import = 8;
  int opset_import_size() const;
  private:
  int _internal_opset_import_size() const;
  public:
  void clear_opset_import();
  ::onnx::OperatorSetIdProto* mutable_opset_import(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto >*
      mutable_opset_import();
  private:
  const ::onnx::OperatorSetIdProto& _internal_opset_import(int index) const;
  ::onnx::OperatorSetIdProto* _internal_add_opset_import();
  public:
  const ::onnx::OperatorSetIdProto& opset_import(int index) const;
  ::onnx::OperatorSetIdProto* add_opset_import();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto >&
      opset_import() const;

  // repeated .onnx.StringStringEntryProto metadata_props = 14;
  int metadata_props_size() const;
  private:
  int _internal_metadata_props_size() const;
  public:
  void clear_metadata_props();
  ::onnx::StringStringEntryProto* mutable_metadata_props(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
      mutable_metadata_props();
  private:
  const ::onnx::StringStringEntryProto& _internal_metadata_props(int index) const;
  ::onnx::StringStringEntryProto* _internal_add_metadata_props();
  public:
  const ::onnx::StringStringEntryProto& metadata_props(int index) const;
  ::onnx::StringStringEntryProto* add_metadata_props();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
      metadata_props() const;

  // repeated .onnx.TrainingInfoProto training_info = 20;
  int training_info_size() const;
  private:
  int _internal_training_info_size() const;
  public:
  void clear_training_info();
  ::onnx::TrainingInfoProto* mutable_training_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TrainingInfoProto >*
      mutable_training_info();
  private:
  const ::onnx::TrainingInfoProto& _internal_training_info(int index) const;
  ::onnx::TrainingInfoProto* _internal_add_training_info();
  public:
  const ::onnx::TrainingInfoProto& training_info(int index) const;
  ::onnx::TrainingInfoProto* add_training_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TrainingInfoProto >&
      training_info() const;

  // repeated .onnx.FunctionProto functions = 25;
  int functions_size() const;
  private:
  int _internal_functions_size() const;
  public:
  void clear_functions();
  ::onnx::FunctionProto* mutable_functions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto >*
      mutable_functions();
  private:
  const ::onnx::FunctionProto& _internal_functions(int index) const;
  ::onnx::FunctionProto* _internal_add_functions();
  public:
  const ::onnx::FunctionProto& functions(int index) const;
  ::onnx::FunctionProto* add_functions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto >&
      functions() const;

  // optional string producer_name = 2;
  bool has_producer_name() const;
  private:
  bool _internal_has_producer_name() const;
  public:
  void clear_producer_name();
  const std::string& producer_name() const;
  void set_producer_name(const std::string& value);
  void set_producer_name(std::string&& value);
  void set_producer_name(const char* value);
  void set_producer_name(const char* value, size_t size);
  std::string* mutable_producer_name();
  std::string* release_producer_name();
  void set_allocated_producer_name(std::string* producer_name);
  private:
  const std::string& _internal_producer_name() const;
  void _internal_set_producer_name(const std::string& value);
  std::string* _internal_mutable_producer_name();
  public:

  // optional string producer_version = 3;
  bool has_producer_version() const;
  private:
  bool _internal_has_producer_version() const;
  public:
  void clear_producer_version();
  const std::string& producer_version() const;
  void set_producer_version(const std::string& value);
  void set_producer_version(std::string&& value);
  void set_producer_version(const char* value);
  void set_producer_version(const char* value, size_t size);
  std::string* mutable_producer_version();
  std::string* release_producer_version();
  void set_allocated_producer_version(std::string* producer_version);
  private:
  const std::string& _internal_producer_version() const;
  void _internal_set_producer_version(const std::string& value);
  std::string* _internal_mutable_producer_version();
  public:

  // optional string domain = 4;
  bool has_domain() const;
  private:
  bool _internal_has_domain() const;
  public:
  void clear_domain();
  const std::string& domain() const;
  void set_domain(const std::string& value);
  void set_domain(std::string&& value);
  void set_domain(const char* value);
  void set_domain(const char* value, size_t size);
  std::string* mutable_domain();
  std::string* release_domain();
  void set_allocated_domain(std::string* domain);
  private:
  const std::string& _internal_domain() const;
  void _internal_set_domain(const std::string& value);
  std::string* _internal_mutable_domain();
  public:

  // optional string doc_string = 6;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  void set_doc_string(const std::string& value);
  void set_doc_string(std::string&& value);
  void set_doc_string(const char* value);
  void set_doc_string(const char* value, size_t size);
  std::string* mutable_doc_string();
  std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional .onnx.GraphProto graph = 7;
  bool has_graph() const;
  private:
  bool _internal_has_graph() const;
  public:
  void clear_graph();
  const ::onnx::GraphProto& graph() const;
  ::onnx::GraphProto* release_graph();
  ::onnx::GraphProto* mutable_graph();
  void set_allocated_graph(::onnx::GraphProto* graph);
  private:
  const ::onnx::GraphProto& _internal_graph() const;
  ::onnx::GraphProto* _internal_mutable_graph();
  public:

  // optional int64 ir_version = 1;
  bool has_ir_version() const;
  private:
  bool _internal_has_ir_version() const;
  public:
  void clear_ir_version();
  ::PROTOBUF_NAMESPACE_ID::int64 ir_version() const;
  void set_ir_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_ir_version() const;
  void _internal_set_ir_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // optional int64 model_version = 5;
  bool has_model_version() const;
  private:
  bool _internal_has_model_version() const;
  public:
  void clear_model_version();
  ::PROTOBUF_NAMESPACE_ID::int64 model_version() const;
  void set_model_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_model_version() const;
  void _internal_set_model_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.ModelProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto > opset_import_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto > metadata_props_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TrainingInfoProto > training_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto > functions_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr producer_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr producer_version_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr domain_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::onnx::GraphProto* graph_;
  ::PROTOBUF_NAMESPACE_ID::int64 ir_version_;
  ::PROTOBUF_NAMESPACE_ID::int64 model_version_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class StringStringEntryProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.StringStringEntryProto) */ {
 public:
  StringStringEntryProto();
  virtual ~StringStringEntryProto();

  StringStringEntryProto(const StringStringEntryProto& from);
  StringStringEntryProto(StringStringEntryProto&& from) noexcept
    : StringStringEntryProto() {
    *this = ::std::move(from);
  }

  inline StringStringEntryProto& operator=(const StringStringEntryProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline StringStringEntryProto& operator=(StringStringEntryProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StringStringEntryProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StringStringEntryProto* internal_default_instance() {
    return reinterpret_cast<const StringStringEntryProto*>(
               &_StringStringEntryProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(StringStringEntryProto& a, StringStringEntryProto& b) {
    a.Swap(&b);
  }
  inline void Swap(StringStringEntryProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StringStringEntryProto* New() const final {
    return CreateMaybeMessage<StringStringEntryProto>(nullptr);
  }

  StringStringEntryProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StringStringEntryProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StringStringEntryProto& from);
  void MergeFrom(const StringStringEntryProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StringStringEntryProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.StringStringEntryProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // optional string key = 1;
  bool has_key() const;
  private:
  bool _internal_has_key() const;
  public:
  void clear_key();
  const std::string& key() const;
  void set_key(const std::string& value);
  void set_key(std::string&& value);
  void set_key(const char* value);
  void set_key(const char* value, size_t size);
  std::string* mutable_key();
  std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // optional string value = 2;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const std::string& value() const;
  void set_value(const std::string& value);
  void set_value(std::string&& value);
  void set_value(const char* value);
  void set_value(const char* value, size_t size);
  std::string* mutable_value();
  std::string* release_value();
  void set_allocated_value(std::string* value);
  private:
  const std::string& _internal_value() const;
  void _internal_set_value(const std::string& value);
  std::string* _internal_mutable_value();
  public:

  // @@protoc_insertion_point(class_scope:onnx.StringStringEntryProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TensorAnnotation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TensorAnnotation) */ {
 public:
  TensorAnnotation();
  virtual ~TensorAnnotation();

  TensorAnnotation(const TensorAnnotation& from);
  TensorAnnotation(TensorAnnotation&& from) noexcept
    : TensorAnnotation() {
    *this = ::std::move(from);
  }

  inline TensorAnnotation& operator=(const TensorAnnotation& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorAnnotation& operator=(TensorAnnotation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorAnnotation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorAnnotation* internal_default_instance() {
    return reinterpret_cast<const TensorAnnotation*>(
               &_TensorAnnotation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(TensorAnnotation& a, TensorAnnotation& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorAnnotation* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorAnnotation* New() const final {
    return CreateMaybeMessage<TensorAnnotation>(nullptr);
  }

  TensorAnnotation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorAnnotation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorAnnotation& from);
  void MergeFrom(const TensorAnnotation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorAnnotation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TensorAnnotation";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQuantParameterTensorNamesFieldNumber = 2,
    kTensorNameFieldNumber = 1,
  };
  // repeated .onnx.StringStringEntryProto quant_parameter_tensor_names = 2;
  int quant_parameter_tensor_names_size() const;
  private:
  int _internal_quant_parameter_tensor_names_size() const;
  public:
  void clear_quant_parameter_tensor_names();
  ::onnx::StringStringEntryProto* mutable_quant_parameter_tensor_names(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
      mutable_quant_parameter_tensor_names();
  private:
  const ::onnx::StringStringEntryProto& _internal_quant_parameter_tensor_names(int index) const;
  ::onnx::StringStringEntryProto* _internal_add_quant_parameter_tensor_names();
  public:
  const ::onnx::StringStringEntryProto& quant_parameter_tensor_names(int index) const;
  ::onnx::StringStringEntryProto* add_quant_parameter_tensor_names();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
      quant_parameter_tensor_names() const;

  // optional string tensor_name = 1;
  bool has_tensor_name() const;
  private:
  bool _internal_has_tensor_name() const;
  public:
  void clear_tensor_name();
  const std::string& tensor_name() const;
  void set_tensor_name(const std::string& value);
  void set_tensor_name(std::string&& value);
  void set_tensor_name(const char* value);
  void set_tensor_name(const char* value, size_t size);
  std::string* mutable_tensor_name();
  std::string* release_tensor_name();
  void set_allocated_tensor_name(std::string* tensor_name);
  private:
  const std::string& _internal_tensor_name() const;
  void _internal_set_tensor_name(const std::string& value);
  std::string* _internal_mutable_tensor_name();
  public:

  // @@protoc_insertion_point(class_scope:onnx.TensorAnnotation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto > quant_parameter_tensor_names_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tensor_name_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class GraphProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.GraphProto) */ {
 public:
  GraphProto();
  virtual ~GraphProto();

  GraphProto(const GraphProto& from);
  GraphProto(GraphProto&& from) noexcept
    : GraphProto() {
    *this = ::std::move(from);
  }

  inline GraphProto& operator=(const GraphProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphProto& operator=(GraphProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphProto* internal_default_instance() {
    return reinterpret_cast<const GraphProto*>(
               &_GraphProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(GraphProto& a, GraphProto& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphProto* New() const final {
    return CreateMaybeMessage<GraphProto>(nullptr);
  }

  GraphProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphProto& from);
  void MergeFrom(const GraphProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.GraphProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeFieldNumber = 1,
    kInitializerFieldNumber = 5,
    kInputFieldNumber = 11,
    kOutputFieldNumber = 12,
    kValueInfoFieldNumber = 13,
    kQuantizationAnnotationFieldNumber = 14,
    kSparseInitializerFieldNumber = 15,
    kNameFieldNumber = 2,
    kDocStringFieldNumber = 10,
  };
  // repeated .onnx.NodeProto node = 1;
  int node_size() const;
  private:
  int _internal_node_size() const;
  public:
  void clear_node();
  ::onnx::NodeProto* mutable_node(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto >*
      mutable_node();
  private:
  const ::onnx::NodeProto& _internal_node(int index) const;
  ::onnx::NodeProto* _internal_add_node();
  public:
  const ::onnx::NodeProto& node(int index) const;
  ::onnx::NodeProto* add_node();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto >&
      node() const;

  // repeated .onnx.TensorProto initializer = 5;
  int initializer_size() const;
  private:
  int _internal_initializer_size() const;
  public:
  void clear_initializer();
  ::onnx::TensorProto* mutable_initializer(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto >*
      mutable_initializer();
  private:
  const ::onnx::TensorProto& _internal_initializer(int index) const;
  ::onnx::TensorProto* _internal_add_initializer();
  public:
  const ::onnx::TensorProto& initializer(int index) const;
  ::onnx::TensorProto* add_initializer();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto >&
      initializer() const;

  // repeated .onnx.ValueInfoProto input = 11;
  int input_size() const;
  private:
  int _internal_input_size() const;
  public:
  void clear_input();
  ::onnx::ValueInfoProto* mutable_input(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >*
      mutable_input();
  private:
  const ::onnx::ValueInfoProto& _internal_input(int index) const;
  ::onnx::ValueInfoProto* _internal_add_input();
  public:
  const ::onnx::ValueInfoProto& input(int index) const;
  ::onnx::ValueInfoProto* add_input();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >&
      input() const;

  // repeated .onnx.ValueInfoProto output = 12;
  int output_size() const;
  private:
  int _internal_output_size() const;
  public:
  void clear_output();
  ::onnx::ValueInfoProto* mutable_output(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >*
      mutable_output();
  private:
  const ::onnx::ValueInfoProto& _internal_output(int index) const;
  ::onnx::ValueInfoProto* _internal_add_output();
  public:
  const ::onnx::ValueInfoProto& output(int index) const;
  ::onnx::ValueInfoProto* add_output();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >&
      output() const;

  // repeated .onnx.ValueInfoProto value_info = 13;
  int value_info_size() const;
  private:
  int _internal_value_info_size() const;
  public:
  void clear_value_info();
  ::onnx::ValueInfoProto* mutable_value_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >*
      mutable_value_info();
  private:
  const ::onnx::ValueInfoProto& _internal_value_info(int index) const;
  ::onnx::ValueInfoProto* _internal_add_value_info();
  public:
  const ::onnx::ValueInfoProto& value_info(int index) const;
  ::onnx::ValueInfoProto* add_value_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >&
      value_info() const;

  // repeated .onnx.TensorAnnotation quantization_annotation = 14;
  int quantization_annotation_size() const;
  private:
  int _internal_quantization_annotation_size() const;
  public:
  void clear_quantization_annotation();
  ::onnx::TensorAnnotation* mutable_quantization_annotation(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorAnnotation >*
      mutable_quantization_annotation();
  private:
  const ::onnx::TensorAnnotation& _internal_quantization_annotation(int index) const;
  ::onnx::TensorAnnotation* _internal_add_quantization_annotation();
  public:
  const ::onnx::TensorAnnotation& quantization_annotation(int index) const;
  ::onnx::TensorAnnotation* add_quantization_annotation();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorAnnotation >&
      quantization_annotation() const;

  // repeated .onnx.SparseTensorProto sparse_initializer = 15;
  int sparse_initializer_size() const;
  private:
  int _internal_sparse_initializer_size() const;
  public:
  void clear_sparse_initializer();
  ::onnx::SparseTensorProto* mutable_sparse_initializer(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto >*
      mutable_sparse_initializer();
  private:
  const ::onnx::SparseTensorProto& _internal_sparse_initializer(int index) const;
  ::onnx::SparseTensorProto* _internal_add_sparse_initializer();
  public:
  const ::onnx::SparseTensorProto& sparse_initializer(int index) const;
  ::onnx::SparseTensorProto* add_sparse_initializer();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto >&
      sparse_initializer() const;

  // optional string name = 2;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional string doc_string = 10;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  void set_doc_string(const std::string& value);
  void set_doc_string(std::string&& value);
  void set_doc_string(const char* value);
  void set_doc_string(const char* value, size_t size);
  std::string* mutable_doc_string();
  std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // @@protoc_insertion_point(class_scope:onnx.GraphProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto > node_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto > initializer_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto > input_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto > output_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto > value_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorAnnotation > quantization_annotation_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto > sparse_initializer_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TensorProto_Segment :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TensorProto.Segment) */ {
 public:
  TensorProto_Segment();
  virtual ~TensorProto_Segment();

  TensorProto_Segment(const TensorProto_Segment& from);
  TensorProto_Segment(TensorProto_Segment&& from) noexcept
    : TensorProto_Segment() {
    *this = ::std::move(from);
  }

  inline TensorProto_Segment& operator=(const TensorProto_Segment& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorProto_Segment& operator=(TensorProto_Segment&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorProto_Segment& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorProto_Segment* internal_default_instance() {
    return reinterpret_cast<const TensorProto_Segment*>(
               &_TensorProto_Segment_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(TensorProto_Segment& a, TensorProto_Segment& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorProto_Segment* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorProto_Segment* New() const final {
    return CreateMaybeMessage<TensorProto_Segment>(nullptr);
  }

  TensorProto_Segment* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorProto_Segment>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorProto_Segment& from);
  void MergeFrom(const TensorProto_Segment& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorProto_Segment* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TensorProto.Segment";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBeginFieldNumber = 1,
    kEndFieldNumber = 2,
  };
  // optional int64 begin = 1;
  bool has_begin() const;
  private:
  bool _internal_has_begin() const;
  public:
  void clear_begin();
  ::PROTOBUF_NAMESPACE_ID::int64 begin() const;
  void set_begin(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_begin() const;
  void _internal_set_begin(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // optional int64 end = 2;
  bool has_end() const;
  private:
  bool _internal_has_end() const;
  public:
  void clear_end();
  ::PROTOBUF_NAMESPACE_ID::int64 end() const;
  void set_end(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_end() const;
  void _internal_set_end(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.TensorProto.Segment)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 begin_;
  ::PROTOBUF_NAMESPACE_ID::int64 end_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TensorProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TensorProto) */ {
 public:
  TensorProto();
  virtual ~TensorProto();

  TensorProto(const TensorProto& from);
  TensorProto(TensorProto&& from) noexcept
    : TensorProto() {
    *this = ::std::move(from);
  }

  inline TensorProto& operator=(const TensorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorProto& operator=(TensorProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorProto* internal_default_instance() {
    return reinterpret_cast<const TensorProto*>(
               &_TensorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(TensorProto& a, TensorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorProto* New() const final {
    return CreateMaybeMessage<TensorProto>(nullptr);
  }

  TensorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorProto& from);
  void MergeFrom(const TensorProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TensorProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TensorProto_Segment Segment;

  typedef TensorProto_DataType DataType;
  static constexpr DataType UNDEFINED =
    TensorProto_DataType_UNDEFINED;
  static constexpr DataType FLOAT =
    TensorProto_DataType_FLOAT;
  static constexpr DataType UINT8 =
    TensorProto_DataType_UINT8;
  static constexpr DataType INT8 =
    TensorProto_DataType_INT8;
  static constexpr DataType UINT16 =
    TensorProto_DataType_UINT16;
  static constexpr DataType INT16 =
    TensorProto_DataType_INT16;
  static constexpr DataType INT32 =
    TensorProto_DataType_INT32;
  static constexpr DataType INT64 =
    TensorProto_DataType_INT64;
  static constexpr DataType STRING =
    TensorProto_DataType_STRING;
  static constexpr DataType BOOL =
    TensorProto_DataType_BOOL;
  static constexpr DataType FLOAT16 =
    TensorProto_DataType_FLOAT16;
  static constexpr DataType DOUBLE =
    TensorProto_DataType_DOUBLE;
  static constexpr DataType UINT32 =
    TensorProto_DataType_UINT32;
  static constexpr DataType UINT64 =
    TensorProto_DataType_UINT64;
  static constexpr DataType COMPLEX64 =
    TensorProto_DataType_COMPLEX64;
  static constexpr DataType COMPLEX128 =
    TensorProto_DataType_COMPLEX128;
  static constexpr DataType BFLOAT16 =
    TensorProto_DataType_BFLOAT16;
  static inline bool DataType_IsValid(int value) {
    return TensorProto_DataType_IsValid(value);
  }
  static constexpr DataType DataType_MIN =
    TensorProto_DataType_DataType_MIN;
  static constexpr DataType DataType_MAX =
    TensorProto_DataType_DataType_MAX;
  static constexpr int DataType_ARRAYSIZE =
    TensorProto_DataType_DataType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  DataType_descriptor() {
    return TensorProto_DataType_descriptor();
  }
  template<typename T>
  static inline const std::string& DataType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, DataType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function DataType_Name.");
    return TensorProto_DataType_Name(enum_t_value);
  }
  static inline bool DataType_Parse(const std::string& name,
      DataType* value) {
    return TensorProto_DataType_Parse(name, value);
  }

  typedef TensorProto_DataLocation DataLocation;
  static constexpr DataLocation DEFAULT =
    TensorProto_DataLocation_DEFAULT;
  static constexpr DataLocation EXTERNAL =
    TensorProto_DataLocation_EXTERNAL;
  static inline bool DataLocation_IsValid(int value) {
    return TensorProto_DataLocation_IsValid(value);
  }
  static constexpr DataLocation DataLocation_MIN =
    TensorProto_DataLocation_DataLocation_MIN;
  static constexpr DataLocation DataLocation_MAX =
    TensorProto_DataLocation_DataLocation_MAX;
  static constexpr int DataLocation_ARRAYSIZE =
    TensorProto_DataLocation_DataLocation_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  DataLocation_descriptor() {
    return TensorProto_DataLocation_descriptor();
  }
  template<typename T>
  static inline const std::string& DataLocation_Name(T enum_t_value) {
    static_assert(::std::is_same<T, DataLocation>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function DataLocation_Name.");
    return TensorProto_DataLocation_Name(enum_t_value);
  }
  static inline bool DataLocation_Parse(const std::string& name,
      DataLocation* value) {
    return TensorProto_DataLocation_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDimsFieldNumber = 1,
    kFloatDataFieldNumber = 4,
    kInt32DataFieldNumber = 5,
    kStringDataFieldNumber = 6,
    kInt64DataFieldNumber = 7,
    kDoubleDataFieldNumber = 10,
    kUint64DataFieldNumber = 11,
    kExternalDataFieldNumber = 13,
    kNameFieldNumber = 8,
    kRawDataFieldNumber = 9,
    kDocStringFieldNumber = 12,
    kSegmentFieldNumber = 3,
    kDataTypeFieldNumber = 2,
    kDataLocationFieldNumber = 14,
  };
  // repeated int64 dims = 1;
  int dims_size() const;
  private:
  int _internal_dims_size() const;
  public:
  void clear_dims();
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_dims(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      _internal_dims() const;
  void _internal_add_dims(::PROTOBUF_NAMESPACE_ID::int64 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      _internal_mutable_dims();
  public:
  ::PROTOBUF_NAMESPACE_ID::int64 dims(int index) const;
  void set_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_dims(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_dims();

  // repeated float float_data = 4 [packed = true];
  int float_data_size() const;
  private:
  int _internal_float_data_size() const;
  public:
  void clear_float_data();
  private:
  float _internal_float_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_float_data() const;
  void _internal_add_float_data(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_float_data();
  public:
  float float_data(int index) const;
  void set_float_data(int index, float value);
  void add_float_data(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      float_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_float_data();

  // repeated int32 int32_data = 5 [packed = true];
  int int32_data_size() const;
  private:
  int _internal_int32_data_size() const;
  public:
  void clear_int32_data();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_int32_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_int32_data() const;
  void _internal_add_int32_data(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_int32_data();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 int32_data(int index) const;
  void set_int32_data(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_int32_data(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      int32_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_int32_data();

  // repeated bytes string_data = 6;
  int string_data_size() const;
  private:
  int _internal_string_data_size() const;
  public:
  void clear_string_data();
  const std::string& string_data(int index) const;
  std::string* mutable_string_data(int index);
  void set_string_data(int index, const std::string& value);
  void set_string_data(int index, std::string&& value);
  void set_string_data(int index, const char* value);
  void set_string_data(int index, const void* value, size_t size);
  std::string* add_string_data();
  void add_string_data(const std::string& value);
  void add_string_data(std::string&& value);
  void add_string_data(const char* value);
  void add_string_data(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& string_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_string_data();
  private:
  const std::string& _internal_string_data(int index) const;
  std::string* _internal_add_string_data();
  public:

  // repeated int64 int64_data = 7 [packed = true];
  int int64_data_size() const;
  private:
  int _internal_int64_data_size() const;
  public:
  void clear_int64_data();
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_int64_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      _internal_int64_data() const;
  void _internal_add_int64_data(::PROTOBUF_NAMESPACE_ID::int64 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      _internal_mutable_int64_data();
  public:
  ::PROTOBUF_NAMESPACE_ID::int64 int64_data(int index) const;
  void set_int64_data(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_int64_data(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      int64_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_int64_data();

  // repeated double double_data = 10 [packed = true];
  int double_data_size() const;
  private:
  int _internal_double_data_size() const;
  public:
  void clear_double_data();
  private:
  double _internal_double_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_double_data() const;
  void _internal_add_double_data(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_double_data();
  public:
  double double_data(int index) const;
  void set_double_data(int index, double value);
  void add_double_data(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      double_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_double_data();

  // repeated uint64 uint64_data = 11 [packed = true];
  int uint64_data_size() const;
  private:
  int _internal_uint64_data_size() const;
  public:
  void clear_uint64_data();
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_uint64_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
      _internal_uint64_data() const;
  void _internal_add_uint64_data(::PROTOBUF_NAMESPACE_ID::uint64 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
      _internal_mutable_uint64_data();
  public:
  ::PROTOBUF_NAMESPACE_ID::uint64 uint64_data(int index) const;
  void set_uint64_data(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value);
  void add_uint64_data(::PROTOBUF_NAMESPACE_ID::uint64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
      uint64_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
      mutable_uint64_data();

  // repeated .onnx.StringStringEntryProto external_data = 13;
  int external_data_size() const;
  private:
  int _internal_external_data_size() const;
  public:
  void clear_external_data();
  ::onnx::StringStringEntryProto* mutable_external_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
      mutable_external_data();
  private:
  const ::onnx::StringStringEntryProto& _internal_external_data(int index) const;
  ::onnx::StringStringEntryProto* _internal_add_external_data();
  public:
  const ::onnx::StringStringEntryProto& external_data(int index) const;
  ::onnx::StringStringEntryProto* add_external_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
      external_data() const;

  // optional string name = 8;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional bytes raw_data = 9;
  bool has_raw_data() const;
  private:
  bool _internal_has_raw_data() const;
  public:
  void clear_raw_data();
  const std::string& raw_data() const;
  void set_raw_data(const std::string& value);
  void set_raw_data(std::string&& value);
  void set_raw_data(const char* value);
  void set_raw_data(const void* value, size_t size);
  std::string* mutable_raw_data();
  std::string* release_raw_data();
  void set_allocated_raw_data(std::string* raw_data);
  private:
  const std::string& _internal_raw_data() const;
  void _internal_set_raw_data(const std::string& value);
  std::string* _internal_mutable_raw_data();
  public:

  // optional string doc_string = 12;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  void set_doc_string(const std::string& value);
  void set_doc_string(std::string&& value);
  void set_doc_string(const char* value);
  void set_doc_string(const char* value, size_t size);
  std::string* mutable_doc_string();
  std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional .onnx.TensorProto.Segment segment = 3;
  bool has_segment() const;
  private:
  bool _internal_has_segment() const;
  public:
  void clear_segment();
  const ::onnx::TensorProto_Segment& segment() const;
  ::onnx::TensorProto_Segment* release_segment();
  ::onnx::TensorProto_Segment* mutable_segment();
  void set_allocated_segment(::onnx::TensorProto_Segment* segment);
  private:
  const ::onnx::TensorProto_Segment& _internal_segment() const;
  ::onnx::TensorProto_Segment* _internal_mutable_segment();
  public:

  // optional int32 data_type = 2;
  bool has_data_type() const;
  private:
  bool _internal_has_data_type() const;
  public:
  void clear_data_type();
  ::PROTOBUF_NAMESPACE_ID::int32 data_type() const;
  void set_data_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_data_type() const;
  void _internal_set_data_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // optional .onnx.TensorProto.DataLocation data_location = 14;
  bool has_data_location() const;
  private:
  bool _internal_has_data_location() const;
  public:
  void clear_data_location();
  ::onnx::TensorProto_DataLocation data_location() const;
  void set_data_location(::onnx::TensorProto_DataLocation value);
  private:
  ::onnx::TensorProto_DataLocation _internal_data_location() const;
  void _internal_set_data_location(::onnx::TensorProto_DataLocation value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.TensorProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > dims_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > float_data_;
  mutable std::atomic<int> _float_data_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > int32_data_;
  mutable std::atomic<int> _int32_data_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> string_data_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > int64_data_;
  mutable std::atomic<int> _int64_data_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > double_data_;
  mutable std::atomic<int> _double_data_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 > uint64_data_;
  mutable std::atomic<int> _uint64_data_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto > external_data_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr raw_data_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::onnx::TensorProto_Segment* segment_;
  ::PROTOBUF_NAMESPACE_ID::int32 data_type_;
  int data_location_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class SparseTensorProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.SparseTensorProto) */ {
 public:
  SparseTensorProto();
  virtual ~SparseTensorProto();

  SparseTensorProto(const SparseTensorProto& from);
  SparseTensorProto(SparseTensorProto&& from) noexcept
    : SparseTensorProto() {
    *this = ::std::move(from);
  }

  inline SparseTensorProto& operator=(const SparseTensorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline SparseTensorProto& operator=(SparseTensorProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SparseTensorProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SparseTensorProto* internal_default_instance() {
    return reinterpret_cast<const SparseTensorProto*>(
               &_SparseTensorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(SparseTensorProto& a, SparseTensorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(SparseTensorProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SparseTensorProto* New() const final {
    return CreateMaybeMessage<SparseTensorProto>(nullptr);
  }

  SparseTensorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SparseTensorProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SparseTensorProto& from);
  void MergeFrom(const SparseTensorProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SparseTensorProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.SparseTensorProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimsFieldNumber = 3,
    kValuesFieldNumber = 1,
    kIndicesFieldNumber = 2,
  };
  // repeated int64 dims = 3;
  int dims_size() const;
  private:
  int _internal_dims_size() const;
  public:
  void clear_dims();
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_dims(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      _internal_dims() const;
  void _internal_add_dims(::PROTOBUF_NAMESPACE_ID::int64 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      _internal_mutable_dims();
  public:
  ::PROTOBUF_NAMESPACE_ID::int64 dims(int index) const;
  void set_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_dims(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_dims();

  // optional .onnx.TensorProto values = 1;
  bool has_values() const;
  private:
  bool _internal_has_values() const;
  public:
  void clear_values();
  const ::onnx::TensorProto& values() const;
  ::onnx::TensorProto* release_values();
  ::onnx::TensorProto* mutable_values();
  void set_allocated_values(::onnx::TensorProto* values);
  private:
  const ::onnx::TensorProto& _internal_values() const;
  ::onnx::TensorProto* _internal_mutable_values();
  public:

  // optional .onnx.TensorProto indices = 2;
  bool has_indices() const;
  private:
  bool _internal_has_indices() const;
  public:
  void clear_indices();
  const ::onnx::TensorProto& indices() const;
  ::onnx::TensorProto* release_indices();
  ::onnx::TensorProto* mutable_indices();
  void set_allocated_indices(::onnx::TensorProto* indices);
  private:
  const ::onnx::TensorProto& _internal_indices() const;
  ::onnx::TensorProto* _internal_mutable_indices();
  public:

  // @@protoc_insertion_point(class_scope:onnx.SparseTensorProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > dims_;
  ::onnx::TensorProto* values_;
  ::onnx::TensorProto* indices_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TensorShapeProto_Dimension :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TensorShapeProto.Dimension) */ {
 public:
  TensorShapeProto_Dimension();
  virtual ~TensorShapeProto_Dimension();

  TensorShapeProto_Dimension(const TensorShapeProto_Dimension& from);
  TensorShapeProto_Dimension(TensorShapeProto_Dimension&& from) noexcept
    : TensorShapeProto_Dimension() {
    *this = ::std::move(from);
  }

  inline TensorShapeProto_Dimension& operator=(const TensorShapeProto_Dimension& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorShapeProto_Dimension& operator=(TensorShapeProto_Dimension&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorShapeProto_Dimension& default_instance();

  enum ValueCase {
    kDimValue = 1,
    kDimParam = 2,
    VALUE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorShapeProto_Dimension* internal_default_instance() {
    return reinterpret_cast<const TensorShapeProto_Dimension*>(
               &_TensorShapeProto_Dimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(TensorShapeProto_Dimension& a, TensorShapeProto_Dimension& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorShapeProto_Dimension* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorShapeProto_Dimension* New() const final {
    return CreateMaybeMessage<TensorShapeProto_Dimension>(nullptr);
  }

  TensorShapeProto_Dimension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorShapeProto_Dimension>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorShapeProto_Dimension& from);
  void MergeFrom(const TensorShapeProto_Dimension& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorShapeProto_Dimension* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TensorShapeProto.Dimension";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDenotationFieldNumber = 3,
    kDimValueFieldNumber = 1,
    kDimParamFieldNumber = 2,
  };
  // optional string denotation = 3;
  bool has_denotation() const;
  private:
  bool _internal_has_denotation() const;
  public:
  void clear_denotation();
  const std::string& denotation() const;
  void set_denotation(const std::string& value);
  void set_denotation(std::string&& value);
  void set_denotation(const char* value);
  void set_denotation(const char* value, size_t size);
  std::string* mutable_denotation();
  std::string* release_denotation();
  void set_allocated_denotation(std::string* denotation);
  private:
  const std::string& _internal_denotation() const;
  void _internal_set_denotation(const std::string& value);
  std::string* _internal_mutable_denotation();
  public:

  // optional int64 dim_value = 1;
  bool has_dim_value() const;
  private:
  bool _internal_has_dim_value() const;
  public:
  void clear_dim_value();
  ::PROTOBUF_NAMESPACE_ID::int64 dim_value() const;
  void set_dim_value(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_dim_value() const;
  void _internal_set_dim_value(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // optional string dim_param = 2;
  bool has_dim_param() const;
  private:
  bool _internal_has_dim_param() const;
  public:
  void clear_dim_param();
  const std::string& dim_param() const;
  void set_dim_param(const std::string& value);
  void set_dim_param(std::string&& value);
  void set_dim_param(const char* value);
  void set_dim_param(const char* value, size_t size);
  std::string* mutable_dim_param();
  std::string* release_dim_param();
  void set_allocated_dim_param(std::string* dim_param);
  private:
  const std::string& _internal_dim_param() const;
  void _internal_set_dim_param(const std::string& value);
  std::string* _internal_mutable_dim_param();
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:onnx.TensorShapeProto.Dimension)
 private:
  class _Internal;
  void set_has_dim_value();
  void set_has_dim_param();

  inline bool has_value() const;
  inline void clear_has_value();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr denotation_;
  union ValueUnion {
    ValueUnion() {}
    ::PROTOBUF_NAMESPACE_ID::int64 dim_value_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dim_param_;
  } value_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TensorShapeProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TensorShapeProto) */ {
 public:
  TensorShapeProto();
  virtual ~TensorShapeProto();

  TensorShapeProto(const TensorShapeProto& from);
  TensorShapeProto(TensorShapeProto&& from) noexcept
    : TensorShapeProto() {
    *this = ::std::move(from);
  }

  inline TensorShapeProto& operator=(const TensorShapeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorShapeProto& operator=(TensorShapeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorShapeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorShapeProto* internal_default_instance() {
    return reinterpret_cast<const TensorShapeProto*>(
               &_TensorShapeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(TensorShapeProto& a, TensorShapeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorShapeProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorShapeProto* New() const final {
    return CreateMaybeMessage<TensorShapeProto>(nullptr);
  }

  TensorShapeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorShapeProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorShapeProto& from);
  void MergeFrom(const TensorShapeProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorShapeProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TensorShapeProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TensorShapeProto_Dimension Dimension;

  // accessors -------------------------------------------------------

  enum : int {
    kDimFieldNumber = 1,
  };
  // repeated .onnx.TensorShapeProto.Dimension dim = 1;
  int dim_size() const;
  private:
  int _internal_dim_size() const;
  public:
  void clear_dim();
  ::onnx::TensorShapeProto_Dimension* mutable_dim(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorShapeProto_Dimension >*
      mutable_dim();
  private:
  const ::onnx::TensorShapeProto_Dimension& _internal_dim(int index) const;
  ::onnx::TensorShapeProto_Dimension* _internal_add_dim();
  public:
  const ::onnx::TensorShapeProto_Dimension& dim(int index) const;
  ::onnx::TensorShapeProto_Dimension* add_dim();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorShapeProto_Dimension >&
      dim() const;

  // @@protoc_insertion_point(class_scope:onnx.TensorShapeProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorShapeProto_Dimension > dim_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TypeProto_Tensor :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TypeProto.Tensor) */ {
 public:
  TypeProto_Tensor();
  virtual ~TypeProto_Tensor();

  TypeProto_Tensor(const TypeProto_Tensor& from);
  TypeProto_Tensor(TypeProto_Tensor&& from) noexcept
    : TypeProto_Tensor() {
    *this = ::std::move(from);
  }

  inline TypeProto_Tensor& operator=(const TypeProto_Tensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeProto_Tensor& operator=(TypeProto_Tensor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TypeProto_Tensor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TypeProto_Tensor* internal_default_instance() {
    return reinterpret_cast<const TypeProto_Tensor*>(
               &_TypeProto_Tensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(TypeProto_Tensor& a, TypeProto_Tensor& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeProto_Tensor* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TypeProto_Tensor* New() const final {
    return CreateMaybeMessage<TypeProto_Tensor>(nullptr);
  }

  TypeProto_Tensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TypeProto_Tensor>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TypeProto_Tensor& from);
  void MergeFrom(const TypeProto_Tensor& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeProto_Tensor* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TypeProto.Tensor";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kElemTypeFieldNumber = 1,
  };
  // optional .onnx.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::onnx::TensorShapeProto& shape() const;
  ::onnx::TensorShapeProto* release_shape();
  ::onnx::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::onnx::TensorShapeProto* shape);
  private:
  const ::onnx::TensorShapeProto& _internal_shape() const;
  ::onnx::TensorShapeProto* _internal_mutable_shape();
  public:

  // optional int32 elem_type = 1;
  bool has_elem_type() const;
  private:
  bool _internal_has_elem_type() const;
  public:
  void clear_elem_type();
  ::PROTOBUF_NAMESPACE_ID::int32 elem_type() const;
  void set_elem_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_elem_type() const;
  void _internal_set_elem_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.TypeProto.Tensor)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::onnx::TensorShapeProto* shape_;
  ::PROTOBUF_NAMESPACE_ID::int32 elem_type_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TypeProto_Sequence :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TypeProto.Sequence) */ {
 public:
  TypeProto_Sequence();
  virtual ~TypeProto_Sequence();

  TypeProto_Sequence(const TypeProto_Sequence& from);
  TypeProto_Sequence(TypeProto_Sequence&& from) noexcept
    : TypeProto_Sequence() {
    *this = ::std::move(from);
  }

  inline TypeProto_Sequence& operator=(const TypeProto_Sequence& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeProto_Sequence& operator=(TypeProto_Sequence&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TypeProto_Sequence& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TypeProto_Sequence* internal_default_instance() {
    return reinterpret_cast<const TypeProto_Sequence*>(
               &_TypeProto_Sequence_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(TypeProto_Sequence& a, TypeProto_Sequence& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeProto_Sequence* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TypeProto_Sequence* New() const final {
    return CreateMaybeMessage<TypeProto_Sequence>(nullptr);
  }

  TypeProto_Sequence* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TypeProto_Sequence>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TypeProto_Sequence& from);
  void MergeFrom(const TypeProto_Sequence& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeProto_Sequence* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TypeProto.Sequence";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kElemTypeFieldNumber = 1,
  };
  // optional .onnx.TypeProto elem_type = 1;
  bool has_elem_type() const;
  private:
  bool _internal_has_elem_type() const;
  public:
  void clear_elem_type();
  const ::onnx::TypeProto& elem_type() const;
  ::onnx::TypeProto* release_elem_type();
  ::onnx::TypeProto* mutable_elem_type();
  void set_allocated_elem_type(::onnx::TypeProto* elem_type);
  private:
  const ::onnx::TypeProto& _internal_elem_type() const;
  ::onnx::TypeProto* _internal_mutable_elem_type();
  public:

  // @@protoc_insertion_point(class_scope:onnx.TypeProto.Sequence)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::onnx::TypeProto* elem_type_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TypeProto_Map :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TypeProto.Map) */ {
 public:
  TypeProto_Map();
  virtual ~TypeProto_Map();

  TypeProto_Map(const TypeProto_Map& from);
  TypeProto_Map(TypeProto_Map&& from) noexcept
    : TypeProto_Map() {
    *this = ::std::move(from);
  }

  inline TypeProto_Map& operator=(const TypeProto_Map& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeProto_Map& operator=(TypeProto_Map&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TypeProto_Map& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TypeProto_Map* internal_default_instance() {
    return reinterpret_cast<const TypeProto_Map*>(
               &_TypeProto_Map_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(TypeProto_Map& a, TypeProto_Map& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeProto_Map* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TypeProto_Map* New() const final {
    return CreateMaybeMessage<TypeProto_Map>(nullptr);
  }

  TypeProto_Map* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TypeProto_Map>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TypeProto_Map& from);
  void MergeFrom(const TypeProto_Map& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeProto_Map* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TypeProto.Map";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueTypeFieldNumber = 2,
    kKeyTypeFieldNumber = 1,
  };
  // optional .onnx.TypeProto value_type = 2;
  bool has_value_type() const;
  private:
  bool _internal_has_value_type() const;
  public:
  void clear_value_type();
  const ::onnx::TypeProto& value_type() const;
  ::onnx::TypeProto* release_value_type();
  ::onnx::TypeProto* mutable_value_type();
  void set_allocated_value_type(::onnx::TypeProto* value_type);
  private:
  const ::onnx::TypeProto& _internal_value_type() const;
  ::onnx::TypeProto* _internal_mutable_value_type();
  public:

  // optional int32 key_type = 1;
  bool has_key_type() const;
  private:
  bool _internal_has_key_type() const;
  public:
  void clear_key_type();
  ::PROTOBUF_NAMESPACE_ID::int32 key_type() const;
  void set_key_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_key_type() const;
  void _internal_set_key_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.TypeProto.Map)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::onnx::TypeProto* value_type_;
  ::PROTOBUF_NAMESPACE_ID::int32 key_type_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TypeProto_Optional :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TypeProto.Optional) */ {
 public:
  TypeProto_Optional();
  virtual ~TypeProto_Optional();

  TypeProto_Optional(const TypeProto_Optional& from);
  TypeProto_Optional(TypeProto_Optional&& from) noexcept
    : TypeProto_Optional() {
    *this = ::std::move(from);
  }

  inline TypeProto_Optional& operator=(const TypeProto_Optional& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeProto_Optional& operator=(TypeProto_Optional&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TypeProto_Optional& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TypeProto_Optional* internal_default_instance() {
    return reinterpret_cast<const TypeProto_Optional*>(
               &_TypeProto_Optional_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(TypeProto_Optional& a, TypeProto_Optional& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeProto_Optional* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TypeProto_Optional* New() const final {
    return CreateMaybeMessage<TypeProto_Optional>(nullptr);
  }

  TypeProto_Optional* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TypeProto_Optional>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TypeProto_Optional& from);
  void MergeFrom(const TypeProto_Optional& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeProto_Optional* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TypeProto.Optional";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kElemTypeFieldNumber = 1,
  };
  // optional .onnx.TypeProto elem_type = 1;
  bool has_elem_type() const;
  private:
  bool _internal_has_elem_type() const;
  public:
  void clear_elem_type();
  const ::onnx::TypeProto& elem_type() const;
  ::onnx::TypeProto* release_elem_type();
  ::onnx::TypeProto* mutable_elem_type();
  void set_allocated_elem_type(::onnx::TypeProto* elem_type);
  private:
  const ::onnx::TypeProto& _internal_elem_type() const;
  ::onnx::TypeProto* _internal_mutable_elem_type();
  public:

  // @@protoc_insertion_point(class_scope:onnx.TypeProto.Optional)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::onnx::TypeProto* elem_type_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TypeProto_SparseTensor :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TypeProto.SparseTensor) */ {
 public:
  TypeProto_SparseTensor();
  virtual ~TypeProto_SparseTensor();

  TypeProto_SparseTensor(const TypeProto_SparseTensor& from);
  TypeProto_SparseTensor(TypeProto_SparseTensor&& from) noexcept
    : TypeProto_SparseTensor() {
    *this = ::std::move(from);
  }

  inline TypeProto_SparseTensor& operator=(const TypeProto_SparseTensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeProto_SparseTensor& operator=(TypeProto_SparseTensor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TypeProto_SparseTensor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TypeProto_SparseTensor* internal_default_instance() {
    return reinterpret_cast<const TypeProto_SparseTensor*>(
               &_TypeProto_SparseTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(TypeProto_SparseTensor& a, TypeProto_SparseTensor& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeProto_SparseTensor* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TypeProto_SparseTensor* New() const final {
    return CreateMaybeMessage<TypeProto_SparseTensor>(nullptr);
  }

  TypeProto_SparseTensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TypeProto_SparseTensor>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TypeProto_SparseTensor& from);
  void MergeFrom(const TypeProto_SparseTensor& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeProto_SparseTensor* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TypeProto.SparseTensor";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kElemTypeFieldNumber = 1,
  };
  // optional .onnx.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::onnx::TensorShapeProto& shape() const;
  ::onnx::TensorShapeProto* release_shape();
  ::onnx::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::onnx::TensorShapeProto* shape);
  private:
  const ::onnx::TensorShapeProto& _internal_shape() const;
  ::onnx::TensorShapeProto* _internal_mutable_shape();
  public:

  // optional int32 elem_type = 1;
  bool has_elem_type() const;
  private:
  bool _internal_has_elem_type() const;
  public:
  void clear_elem_type();
  ::PROTOBUF_NAMESPACE_ID::int32 elem_type() const;
  void set_elem_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_elem_type() const;
  void _internal_set_elem_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.TypeProto.SparseTensor)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::onnx::TensorShapeProto* shape_;
  ::PROTOBUF_NAMESPACE_ID::int32 elem_type_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TypeProto_Opaque :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TypeProto.Opaque) */ {
 public:
  TypeProto_Opaque();
  virtual ~TypeProto_Opaque();

  TypeProto_Opaque(const TypeProto_Opaque& from);
  TypeProto_Opaque(TypeProto_Opaque&& from) noexcept
    : TypeProto_Opaque() {
    *this = ::std::move(from);
  }

  inline TypeProto_Opaque& operator=(const TypeProto_Opaque& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeProto_Opaque& operator=(TypeProto_Opaque&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TypeProto_Opaque& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TypeProto_Opaque* internal_default_instance() {
    return reinterpret_cast<const TypeProto_Opaque*>(
               &_TypeProto_Opaque_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(TypeProto_Opaque& a, TypeProto_Opaque& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeProto_Opaque* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TypeProto_Opaque* New() const final {
    return CreateMaybeMessage<TypeProto_Opaque>(nullptr);
  }

  TypeProto_Opaque* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TypeProto_Opaque>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TypeProto_Opaque& from);
  void MergeFrom(const TypeProto_Opaque& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeProto_Opaque* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TypeProto.Opaque";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDomainFieldNumber = 1,
    kNameFieldNumber = 2,
  };
  // optional string domain = 1;
  bool has_domain() const;
  private:
  bool _internal_has_domain() const;
  public:
  void clear_domain();
  const std::string& domain() const;
  void set_domain(const std::string& value);
  void set_domain(std::string&& value);
  void set_domain(const char* value);
  void set_domain(const char* value, size_t size);
  std::string* mutable_domain();
  std::string* release_domain();
  void set_allocated_domain(std::string* domain);
  private:
  const std::string& _internal_domain() const;
  void _internal_set_domain(const std::string& value);
  std::string* _internal_mutable_domain();
  public:

  // optional string name = 2;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:onnx.TypeProto.Opaque)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr domain_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class TypeProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.TypeProto) */ {
 public:
  TypeProto();
  virtual ~TypeProto();

  TypeProto(const TypeProto& from);
  TypeProto(TypeProto&& from) noexcept
    : TypeProto() {
    *this = ::std::move(from);
  }

  inline TypeProto& operator=(const TypeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeProto& operator=(TypeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TypeProto& default_instance();

  enum ValueCase {
    kTensorType = 1,
    kSequenceType = 4,
    kMapType = 5,
    kOptionalType = 9,
    kSparseTensorType = 8,
    kOpaqueType = 7,
    VALUE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TypeProto* internal_default_instance() {
    return reinterpret_cast<const TypeProto*>(
               &_TypeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(TypeProto& a, TypeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TypeProto* New() const final {
    return CreateMaybeMessage<TypeProto>(nullptr);
  }

  TypeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TypeProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TypeProto& from);
  void MergeFrom(const TypeProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.TypeProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TypeProto_Tensor Tensor;
  typedef TypeProto_Sequence Sequence;
  typedef TypeProto_Map Map;
  typedef TypeProto_Optional Optional;
  typedef TypeProto_SparseTensor SparseTensor;
  typedef TypeProto_Opaque Opaque;

  // accessors -------------------------------------------------------

  enum : int {
    kDenotationFieldNumber = 6,
    kTensorTypeFieldNumber = 1,
    kSequenceTypeFieldNumber = 4,
    kMapTypeFieldNumber = 5,
    kOptionalTypeFieldNumber = 9,
    kSparseTensorTypeFieldNumber = 8,
    kOpaqueTypeFieldNumber = 7,
  };
  // optional string denotation = 6;
  bool has_denotation() const;
  private:
  bool _internal_has_denotation() const;
  public:
  void clear_denotation();
  const std::string& denotation() const;
  void set_denotation(const std::string& value);
  void set_denotation(std::string&& value);
  void set_denotation(const char* value);
  void set_denotation(const char* value, size_t size);
  std::string* mutable_denotation();
  std::string* release_denotation();
  void set_allocated_denotation(std::string* denotation);
  private:
  const std::string& _internal_denotation() const;
  void _internal_set_denotation(const std::string& value);
  std::string* _internal_mutable_denotation();
  public:

  // optional .onnx.TypeProto.Tensor tensor_type = 1;
  bool has_tensor_type() const;
  private:
  bool _internal_has_tensor_type() const;
  public:
  void clear_tensor_type();
  const ::onnx::TypeProto_Tensor& tensor_type() const;
  ::onnx::TypeProto_Tensor* release_tensor_type();
  ::onnx::TypeProto_Tensor* mutable_tensor_type();
  void set_allocated_tensor_type(::onnx::TypeProto_Tensor* tensor_type);
  private:
  const ::onnx::TypeProto_Tensor& _internal_tensor_type() const;
  ::onnx::TypeProto_Tensor* _internal_mutable_tensor_type();
  public:

  // optional .onnx.TypeProto.Sequence sequence_type = 4;
  bool has_sequence_type() const;
  private:
  bool _internal_has_sequence_type() const;
  public:
  void clear_sequence_type();
  const ::onnx::TypeProto_Sequence& sequence_type() const;
  ::onnx::TypeProto_Sequence* release_sequence_type();
  ::onnx::TypeProto_Sequence* mutable_sequence_type();
  void set_allocated_sequence_type(::onnx::TypeProto_Sequence* sequence_type);
  private:
  const ::onnx::TypeProto_Sequence& _internal_sequence_type() const;
  ::onnx::TypeProto_Sequence* _internal_mutable_sequence_type();
  public:

  // optional .onnx.TypeProto.Map map_type = 5;
  bool has_map_type() const;
  private:
  bool _internal_has_map_type() const;
  public:
  void clear_map_type();
  const ::onnx::TypeProto_Map& map_type() const;
  ::onnx::TypeProto_Map* release_map_type();
  ::onnx::TypeProto_Map* mutable_map_type();
  void set_allocated_map_type(::onnx::TypeProto_Map* map_type);
  private:
  const ::onnx::TypeProto_Map& _internal_map_type() const;
  ::onnx::TypeProto_Map* _internal_mutable_map_type();
  public:

  // optional .onnx.TypeProto.Optional optional_type = 9;
  bool has_optional_type() const;
  private:
  bool _internal_has_optional_type() const;
  public:
  void clear_optional_type();
  const ::onnx::TypeProto_Optional& optional_type() const;
  ::onnx::TypeProto_Optional* release_optional_type();
  ::onnx::TypeProto_Optional* mutable_optional_type();
  void set_allocated_optional_type(::onnx::TypeProto_Optional* optional_type);
  private:
  const ::onnx::TypeProto_Optional& _internal_optional_type() const;
  ::onnx::TypeProto_Optional* _internal_mutable_optional_type();
  public:

  // optional .onnx.TypeProto.SparseTensor sparse_tensor_type = 8;
  bool has_sparse_tensor_type() const;
  private:
  bool _internal_has_sparse_tensor_type() const;
  public:
  void clear_sparse_tensor_type();
  const ::onnx::TypeProto_SparseTensor& sparse_tensor_type() const;
  ::onnx::TypeProto_SparseTensor* release_sparse_tensor_type();
  ::onnx::TypeProto_SparseTensor* mutable_sparse_tensor_type();
  void set_allocated_sparse_tensor_type(::onnx::TypeProto_SparseTensor* sparse_tensor_type);
  private:
  const ::onnx::TypeProto_SparseTensor& _internal_sparse_tensor_type() const;
  ::onnx::TypeProto_SparseTensor* _internal_mutable_sparse_tensor_type();
  public:

  // optional .onnx.TypeProto.Opaque opaque_type = 7;
  bool has_opaque_type() const;
  private:
  bool _internal_has_opaque_type() const;
  public:
  void clear_opaque_type();
  const ::onnx::TypeProto_Opaque& opaque_type() const;
  ::onnx::TypeProto_Opaque* release_opaque_type();
  ::onnx::TypeProto_Opaque* mutable_opaque_type();
  void set_allocated_opaque_type(::onnx::TypeProto_Opaque* opaque_type);
  private:
  const ::onnx::TypeProto_Opaque& _internal_opaque_type() const;
  ::onnx::TypeProto_Opaque* _internal_mutable_opaque_type();
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:onnx.TypeProto)
 private:
  class _Internal;
  void set_has_tensor_type();
  void set_has_sequence_type();
  void set_has_map_type();
  void set_has_optional_type();
  void set_has_sparse_tensor_type();
  void set_has_opaque_type();

  inline bool has_value() const;
  inline void clear_has_value();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr denotation_;
  union ValueUnion {
    ValueUnion() {}
    ::onnx::TypeProto_Tensor* tensor_type_;
    ::onnx::TypeProto_Sequence* sequence_type_;
    ::onnx::TypeProto_Map* map_type_;
    ::onnx::TypeProto_Optional* optional_type_;
    ::onnx::TypeProto_SparseTensor* sparse_tensor_type_;
    ::onnx::TypeProto_Opaque* opaque_type_;
  } value_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class OperatorSetIdProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.OperatorSetIdProto) */ {
 public:
  OperatorSetIdProto();
  virtual ~OperatorSetIdProto();

  OperatorSetIdProto(const OperatorSetIdProto& from);
  OperatorSetIdProto(OperatorSetIdProto&& from) noexcept
    : OperatorSetIdProto() {
    *this = ::std::move(from);
  }

  inline OperatorSetIdProto& operator=(const OperatorSetIdProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OperatorSetIdProto& operator=(OperatorSetIdProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OperatorSetIdProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OperatorSetIdProto* internal_default_instance() {
    return reinterpret_cast<const OperatorSetIdProto*>(
               &_OperatorSetIdProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(OperatorSetIdProto& a, OperatorSetIdProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OperatorSetIdProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OperatorSetIdProto* New() const final {
    return CreateMaybeMessage<OperatorSetIdProto>(nullptr);
  }

  OperatorSetIdProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OperatorSetIdProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OperatorSetIdProto& from);
  void MergeFrom(const OperatorSetIdProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OperatorSetIdProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.OperatorSetIdProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDomainFieldNumber = 1,
    kVersionFieldNumber = 2,
  };
  // optional string domain = 1;
  bool has_domain() const;
  private:
  bool _internal_has_domain() const;
  public:
  void clear_domain();
  const std::string& domain() const;
  void set_domain(const std::string& value);
  void set_domain(std::string&& value);
  void set_domain(const char* value);
  void set_domain(const char* value, size_t size);
  std::string* mutable_domain();
  std::string* release_domain();
  void set_allocated_domain(std::string* domain);
  private:
  const std::string& _internal_domain() const;
  void _internal_set_domain(const std::string& value);
  std::string* _internal_mutable_domain();
  public:

  // optional int64 version = 2;
  bool has_version() const;
  private:
  bool _internal_has_version() const;
  public:
  void clear_version();
  ::PROTOBUF_NAMESPACE_ID::int64 version() const;
  void set_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_version() const;
  void _internal_set_version(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // @@protoc_insertion_point(class_scope:onnx.OperatorSetIdProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr domain_;
  ::PROTOBUF_NAMESPACE_ID::int64 version_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// -------------------------------------------------------------------

class FunctionProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:onnx.FunctionProto) */ {
 public:
  FunctionProto();
  virtual ~FunctionProto();

  FunctionProto(const FunctionProto& from);
  FunctionProto(FunctionProto&& from) noexcept
    : FunctionProto() {
    *this = ::std::move(from);
  }

  inline FunctionProto& operator=(const FunctionProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionProto& operator=(FunctionProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FunctionProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FunctionProto* internal_default_instance() {
    return reinterpret_cast<const FunctionProto*>(
               &_FunctionProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(FunctionProto& a, FunctionProto& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FunctionProto* New() const final {
    return CreateMaybeMessage<FunctionProto>(nullptr);
  }

  FunctionProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FunctionProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FunctionProto& from);
  void MergeFrom(const FunctionProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "onnx.FunctionProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_onnx_2dml_2eproto);
    return ::descriptor_table_onnx_2dml_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputFieldNumber = 4,
    kOutputFieldNumber = 5,
    kAttributeFieldNumber = 6,
    kNodeFieldNumber = 7,
    kOpsetImportFieldNumber = 9,
    kNameFieldNumber = 1,
    kDocStringFieldNumber = 8,
    kDomainFieldNumber = 10,
  };
  // repeated string input = 4;
  int input_size() const;
  private:
  int _internal_input_size() const;
  public:
  void clear_input();
  const std::string& input(int index) const;
  std::string* mutable_input(int index);
  void set_input(int index, const std::string& value);
  void set_input(int index, std::string&& value);
  void set_input(int index, const char* value);
  void set_input(int index, const char* value, size_t size);
  std::string* add_input();
  void add_input(const std::string& value);
  void add_input(std::string&& value);
  void add_input(const char* value);
  void add_input(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& input() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_input();
  private:
  const std::string& _internal_input(int index) const;
  std::string* _internal_add_input();
  public:

  // repeated string output = 5;
  int output_size() const;
  private:
  int _internal_output_size() const;
  public:
  void clear_output();
  const std::string& output(int index) const;
  std::string* mutable_output(int index);
  void set_output(int index, const std::string& value);
  void set_output(int index, std::string&& value);
  void set_output(int index, const char* value);
  void set_output(int index, const char* value, size_t size);
  std::string* add_output();
  void add_output(const std::string& value);
  void add_output(std::string&& value);
  void add_output(const char* value);
  void add_output(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& output() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_output();
  private:
  const std::string& _internal_output(int index) const;
  std::string* _internal_add_output();
  public:

  // repeated string attribute = 6;
  int attribute_size() const;
  private:
  int _internal_attribute_size() const;
  public:
  void clear_attribute();
  const std::string& attribute(int index) const;
  std::string* mutable_attribute(int index);
  void set_attribute(int index, const std::string& value);
  void set_attribute(int index, std::string&& value);
  void set_attribute(int index, const char* value);
  void set_attribute(int index, const char* value, size_t size);
  std::string* add_attribute();
  void add_attribute(const std::string& value);
  void add_attribute(std::string&& value);
  void add_attribute(const char* value);
  void add_attribute(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& attribute() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_attribute();
  private:
  const std::string& _internal_attribute(int index) const;
  std::string* _internal_add_attribute();
  public:

  // repeated .onnx.NodeProto node = 7;
  int node_size() const;
  private:
  int _internal_node_size() const;
  public:
  void clear_node();
  ::onnx::NodeProto* mutable_node(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto >*
      mutable_node();
  private:
  const ::onnx::NodeProto& _internal_node(int index) const;
  ::onnx::NodeProto* _internal_add_node();
  public:
  const ::onnx::NodeProto& node(int index) const;
  ::onnx::NodeProto* add_node();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto >&
      node() const;

  // repeated .onnx.OperatorSetIdProto opset_import = 9;
  int opset_import_size() const;
  private:
  int _internal_opset_import_size() const;
  public:
  void clear_opset_import();
  ::onnx::OperatorSetIdProto* mutable_opset_import(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto >*
      mutable_opset_import();
  private:
  const ::onnx::OperatorSetIdProto& _internal_opset_import(int index) const;
  ::onnx::OperatorSetIdProto* _internal_add_opset_import();
  public:
  const ::onnx::OperatorSetIdProto& opset_import(int index) const;
  ::onnx::OperatorSetIdProto* add_opset_import();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto >&
      opset_import() const;

  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional string doc_string = 8;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  void set_doc_string(const std::string& value);
  void set_doc_string(std::string&& value);
  void set_doc_string(const char* value);
  void set_doc_string(const char* value, size_t size);
  std::string* mutable_doc_string();
  std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional string domain = 10;
  bool has_domain() const;
  private:
  bool _internal_has_domain() const;
  public:
  void clear_domain();
  const std::string& domain() const;
  void set_domain(const std::string& value);
  void set_domain(std::string&& value);
  void set_domain(const char* value);
  void set_domain(const char* value, size_t size);
  std::string* mutable_domain();
  std::string* release_domain();
  void set_allocated_domain(std::string* domain);
  private:
  const std::string& _internal_domain() const;
  void _internal_set_domain(const std::string& value);
  std::string* _internal_mutable_domain();
  public:

  // @@protoc_insertion_point(class_scope:onnx.FunctionProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> input_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> output_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> attribute_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto > node_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto > opset_import_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr domain_;
  friend struct ::TableStruct_onnx_2dml_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AttributeProto

// optional string name = 1;
inline bool AttributeProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool AttributeProto::has_name() const {
  return _internal_has_name();
}
inline void AttributeProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& AttributeProto::name() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.name)
  return _internal_name();
}
inline void AttributeProto::set_name(const std::string& value) {
  _internal_set_name(value);
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.name)
}
inline std::string* AttributeProto::mutable_name() {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.name)
  return _internal_mutable_name();
}
inline const std::string& AttributeProto::_internal_name() const {
  return name_.GetNoArena();
}
inline void AttributeProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void AttributeProto::set_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.AttributeProto.name)
}
inline void AttributeProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.AttributeProto.name)
}
inline void AttributeProto::set_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.AttributeProto.name)
}
inline std::string* AttributeProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AttributeProto::release_name() {
  // @@protoc_insertion_point(field_release:onnx.AttributeProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AttributeProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:onnx.AttributeProto.name)
}

// optional string ref_attr_name = 21;
inline bool AttributeProto::_internal_has_ref_attr_name() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool AttributeProto::has_ref_attr_name() const {
  return _internal_has_ref_attr_name();
}
inline void AttributeProto::clear_ref_attr_name() {
  ref_attr_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000008u;
}
inline const std::string& AttributeProto::ref_attr_name() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.ref_attr_name)
  return _internal_ref_attr_name();
}
inline void AttributeProto::set_ref_attr_name(const std::string& value) {
  _internal_set_ref_attr_name(value);
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.ref_attr_name)
}
inline std::string* AttributeProto::mutable_ref_attr_name() {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.ref_attr_name)
  return _internal_mutable_ref_attr_name();
}
inline const std::string& AttributeProto::_internal_ref_attr_name() const {
  return ref_attr_name_.GetNoArena();
}
inline void AttributeProto::_internal_set_ref_attr_name(const std::string& value) {
  _has_bits_[0] |= 0x00000008u;
  ref_attr_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void AttributeProto::set_ref_attr_name(std::string&& value) {
  _has_bits_[0] |= 0x00000008u;
  ref_attr_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.AttributeProto.ref_attr_name)
}
inline void AttributeProto::set_ref_attr_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000008u;
  ref_attr_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.AttributeProto.ref_attr_name)
}
inline void AttributeProto::set_ref_attr_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000008u;
  ref_attr_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.AttributeProto.ref_attr_name)
}
inline std::string* AttributeProto::_internal_mutable_ref_attr_name() {
  _has_bits_[0] |= 0x00000008u;
  return ref_attr_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AttributeProto::release_ref_attr_name() {
  // @@protoc_insertion_point(field_release:onnx.AttributeProto.ref_attr_name)
  if (!_internal_has_ref_attr_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000008u;
  return ref_attr_name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AttributeProto::set_allocated_ref_attr_name(std::string* ref_attr_name) {
  if (ref_attr_name != nullptr) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  ref_attr_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ref_attr_name);
  // @@protoc_insertion_point(field_set_allocated:onnx.AttributeProto.ref_attr_name)
}

// optional string doc_string = 13;
inline bool AttributeProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool AttributeProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void AttributeProto::clear_doc_string() {
  doc_string_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& AttributeProto::doc_string() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.doc_string)
  return _internal_doc_string();
}
inline void AttributeProto::set_doc_string(const std::string& value) {
  _internal_set_doc_string(value);
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.doc_string)
}
inline std::string* AttributeProto::mutable_doc_string() {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.doc_string)
  return _internal_mutable_doc_string();
}
inline const std::string& AttributeProto::_internal_doc_string() const {
  return doc_string_.GetNoArena();
}
inline void AttributeProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void AttributeProto::set_doc_string(std::string&& value) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.AttributeProto.doc_string)
}
inline void AttributeProto::set_doc_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.AttributeProto.doc_string)
}
inline void AttributeProto::set_doc_string(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.AttributeProto.doc_string)
}
inline std::string* AttributeProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000004u;
  return doc_string_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AttributeProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:onnx.AttributeProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  return doc_string_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AttributeProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  doc_string_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string);
  // @@protoc_insertion_point(field_set_allocated:onnx.AttributeProto.doc_string)
}

// optional .onnx.AttributeProto.AttributeType type = 20;
inline bool AttributeProto::_internal_has_type() const {
  bool value = (_has_bits_[0] & 0x00000400u) != 0;
  return value;
}
inline bool AttributeProto::has_type() const {
  return _internal_has_type();
}
inline void AttributeProto::clear_type() {
  type_ = 0;
  _has_bits_[0] &= ~0x00000400u;
}
inline ::onnx::AttributeProto_AttributeType AttributeProto::_internal_type() const {
  return static_cast< ::onnx::AttributeProto_AttributeType >(type_);
}
inline ::onnx::AttributeProto_AttributeType AttributeProto::type() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.type)
  return _internal_type();
}
inline void AttributeProto::_internal_set_type(::onnx::AttributeProto_AttributeType value) {
  assert(::onnx::AttributeProto_AttributeType_IsValid(value));
  _has_bits_[0] |= 0x00000400u;
  type_ = value;
}
inline void AttributeProto::set_type(::onnx::AttributeProto_AttributeType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.type)
}

// optional float f = 2;
inline bool AttributeProto::_internal_has_f() const {
  bool value = (_has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool AttributeProto::has_f() const {
  return _internal_has_f();
}
inline void AttributeProto::clear_f() {
  f_ = 0;
  _has_bits_[0] &= ~0x00000200u;
}
inline float AttributeProto::_internal_f() const {
  return f_;
}
inline float AttributeProto::f() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.f)
  return _internal_f();
}
inline void AttributeProto::_internal_set_f(float value) {
  _has_bits_[0] |= 0x00000200u;
  f_ = value;
}
inline void AttributeProto::set_f(float value) {
  _internal_set_f(value);
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.f)
}

// optional int64 i = 3;
inline bool AttributeProto::_internal_has_i() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool AttributeProto::has_i() const {
  return _internal_has_i();
}
inline void AttributeProto::clear_i() {
  i_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000100u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AttributeProto::_internal_i() const {
  return i_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AttributeProto::i() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.i)
  return _internal_i();
}
inline void AttributeProto::_internal_set_i(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000100u;
  i_ = value;
}
inline void AttributeProto::set_i(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_i(value);
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.i)
}

// optional bytes s = 4;
inline bool AttributeProto::_internal_has_s() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool AttributeProto::has_s() const {
  return _internal_has_s();
}
inline void AttributeProto::clear_s() {
  s_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& AttributeProto::s() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.s)
  return _internal_s();
}
inline void AttributeProto::set_s(const std::string& value) {
  _internal_set_s(value);
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.s)
}
inline std::string* AttributeProto::mutable_s() {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.s)
  return _internal_mutable_s();
}
inline const std::string& AttributeProto::_internal_s() const {
  return s_.GetNoArena();
}
inline void AttributeProto::_internal_set_s(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  s_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void AttributeProto::set_s(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  s_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.AttributeProto.s)
}
inline void AttributeProto::set_s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  s_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.AttributeProto.s)
}
inline void AttributeProto::set_s(const void* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  s_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.AttributeProto.s)
}
inline std::string* AttributeProto::_internal_mutable_s() {
  _has_bits_[0] |= 0x00000002u;
  return s_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AttributeProto::release_s() {
  // @@protoc_insertion_point(field_release:onnx.AttributeProto.s)
  if (!_internal_has_s()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return s_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AttributeProto::set_allocated_s(std::string* s) {
  if (s != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  s_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), s);
  // @@protoc_insertion_point(field_set_allocated:onnx.AttributeProto.s)
}

// optional .onnx.TensorProto t = 5;
inline bool AttributeProto::_internal_has_t() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  PROTOBUF_ASSUME(!value || t_ != nullptr);
  return value;
}
inline bool AttributeProto::has_t() const {
  return _internal_has_t();
}
inline void AttributeProto::clear_t() {
  if (t_ != nullptr) t_->Clear();
  _has_bits_[0] &= ~0x00000010u;
}
inline const ::onnx::TensorProto& AttributeProto::_internal_t() const {
  const ::onnx::TensorProto* p = t_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TensorProto*>(
      &::onnx::_TensorProto_default_instance_);
}
inline const ::onnx::TensorProto& AttributeProto::t() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.t)
  return _internal_t();
}
inline ::onnx::TensorProto* AttributeProto::release_t() {
  // @@protoc_insertion_point(field_release:onnx.AttributeProto.t)
  _has_bits_[0] &= ~0x00000010u;
  ::onnx::TensorProto* temp = t_;
  t_ = nullptr;
  return temp;
}
inline ::onnx::TensorProto* AttributeProto::_internal_mutable_t() {
  _has_bits_[0] |= 0x00000010u;
  if (t_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TensorProto>(GetArenaNoVirtual());
    t_ = p;
  }
  return t_;
}
inline ::onnx::TensorProto* AttributeProto::mutable_t() {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.t)
  return _internal_mutable_t();
}
inline void AttributeProto::set_allocated_t(::onnx::TensorProto* t) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete t_;
  }
  if (t) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      t = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, t, submessage_arena);
    }
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  t_ = t;
  // @@protoc_insertion_point(field_set_allocated:onnx.AttributeProto.t)
}

// optional .onnx.GraphProto g = 6;
inline bool AttributeProto::_internal_has_g() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  PROTOBUF_ASSUME(!value || g_ != nullptr);
  return value;
}
inline bool AttributeProto::has_g() const {
  return _internal_has_g();
}
inline void AttributeProto::clear_g() {
  if (g_ != nullptr) g_->Clear();
  _has_bits_[0] &= ~0x00000020u;
}
inline const ::onnx::GraphProto& AttributeProto::_internal_g() const {
  const ::onnx::GraphProto* p = g_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::GraphProto*>(
      &::onnx::_GraphProto_default_instance_);
}
inline const ::onnx::GraphProto& AttributeProto::g() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.g)
  return _internal_g();
}
inline ::onnx::GraphProto* AttributeProto::release_g() {
  // @@protoc_insertion_point(field_release:onnx.AttributeProto.g)
  _has_bits_[0] &= ~0x00000020u;
  ::onnx::GraphProto* temp = g_;
  g_ = nullptr;
  return temp;
}
inline ::onnx::GraphProto* AttributeProto::_internal_mutable_g() {
  _has_bits_[0] |= 0x00000020u;
  if (g_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::GraphProto>(GetArenaNoVirtual());
    g_ = p;
  }
  return g_;
}
inline ::onnx::GraphProto* AttributeProto::mutable_g() {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.g)
  return _internal_mutable_g();
}
inline void AttributeProto::set_allocated_g(::onnx::GraphProto* g) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete g_;
  }
  if (g) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      g = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, g, submessage_arena);
    }
    _has_bits_[0] |= 0x00000020u;
  } else {
    _has_bits_[0] &= ~0x00000020u;
  }
  g_ = g;
  // @@protoc_insertion_point(field_set_allocated:onnx.AttributeProto.g)
}

// optional .onnx.SparseTensorProto sparse_tensor = 22;
inline bool AttributeProto::_internal_has_sparse_tensor() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  PROTOBUF_ASSUME(!value || sparse_tensor_ != nullptr);
  return value;
}
inline bool AttributeProto::has_sparse_tensor() const {
  return _internal_has_sparse_tensor();
}
inline void AttributeProto::clear_sparse_tensor() {
  if (sparse_tensor_ != nullptr) sparse_tensor_->Clear();
  _has_bits_[0] &= ~0x00000080u;
}
inline const ::onnx::SparseTensorProto& AttributeProto::_internal_sparse_tensor() const {
  const ::onnx::SparseTensorProto* p = sparse_tensor_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::SparseTensorProto*>(
      &::onnx::_SparseTensorProto_default_instance_);
}
inline const ::onnx::SparseTensorProto& AttributeProto::sparse_tensor() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.sparse_tensor)
  return _internal_sparse_tensor();
}
inline ::onnx::SparseTensorProto* AttributeProto::release_sparse_tensor() {
  // @@protoc_insertion_point(field_release:onnx.AttributeProto.sparse_tensor)
  _has_bits_[0] &= ~0x00000080u;
  ::onnx::SparseTensorProto* temp = sparse_tensor_;
  sparse_tensor_ = nullptr;
  return temp;
}
inline ::onnx::SparseTensorProto* AttributeProto::_internal_mutable_sparse_tensor() {
  _has_bits_[0] |= 0x00000080u;
  if (sparse_tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::SparseTensorProto>(GetArenaNoVirtual());
    sparse_tensor_ = p;
  }
  return sparse_tensor_;
}
inline ::onnx::SparseTensorProto* AttributeProto::mutable_sparse_tensor() {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.sparse_tensor)
  return _internal_mutable_sparse_tensor();
}
inline void AttributeProto::set_allocated_sparse_tensor(::onnx::SparseTensorProto* sparse_tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete sparse_tensor_;
  }
  if (sparse_tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      sparse_tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sparse_tensor, submessage_arena);
    }
    _has_bits_[0] |= 0x00000080u;
  } else {
    _has_bits_[0] &= ~0x00000080u;
  }
  sparse_tensor_ = sparse_tensor;
  // @@protoc_insertion_point(field_set_allocated:onnx.AttributeProto.sparse_tensor)
}

// optional .onnx.TypeProto tp = 14;
inline bool AttributeProto::_internal_has_tp() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  PROTOBUF_ASSUME(!value || tp_ != nullptr);
  return value;
}
inline bool AttributeProto::has_tp() const {
  return _internal_has_tp();
}
inline void AttributeProto::clear_tp() {
  if (tp_ != nullptr) tp_->Clear();
  _has_bits_[0] &= ~0x00000040u;
}
inline const ::onnx::TypeProto& AttributeProto::_internal_tp() const {
  const ::onnx::TypeProto* p = tp_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TypeProto*>(
      &::onnx::_TypeProto_default_instance_);
}
inline const ::onnx::TypeProto& AttributeProto::tp() const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.tp)
  return _internal_tp();
}
inline ::onnx::TypeProto* AttributeProto::release_tp() {
  // @@protoc_insertion_point(field_release:onnx.AttributeProto.tp)
  _has_bits_[0] &= ~0x00000040u;
  ::onnx::TypeProto* temp = tp_;
  tp_ = nullptr;
  return temp;
}
inline ::onnx::TypeProto* AttributeProto::_internal_mutable_tp() {
  _has_bits_[0] |= 0x00000040u;
  if (tp_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TypeProto>(GetArenaNoVirtual());
    tp_ = p;
  }
  return tp_;
}
inline ::onnx::TypeProto* AttributeProto::mutable_tp() {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.tp)
  return _internal_mutable_tp();
}
inline void AttributeProto::set_allocated_tp(::onnx::TypeProto* tp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete tp_;
  }
  if (tp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      tp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tp, submessage_arena);
    }
    _has_bits_[0] |= 0x00000040u;
  } else {
    _has_bits_[0] &= ~0x00000040u;
  }
  tp_ = tp;
  // @@protoc_insertion_point(field_set_allocated:onnx.AttributeProto.tp)
}

// repeated float floats = 7;
inline int AttributeProto::_internal_floats_size() const {
  return floats_.size();
}
inline int AttributeProto::floats_size() const {
  return _internal_floats_size();
}
inline void AttributeProto::clear_floats() {
  floats_.Clear();
}
inline float AttributeProto::_internal_floats(int index) const {
  return floats_.Get(index);
}
inline float AttributeProto::floats(int index) const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.floats)
  return _internal_floats(index);
}
inline void AttributeProto::set_floats(int index, float value) {
  floats_.Set(index, value);
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.floats)
}
inline void AttributeProto::_internal_add_floats(float value) {
  floats_.Add(value);
}
inline void AttributeProto::add_floats(float value) {
  _internal_add_floats(value);
  // @@protoc_insertion_point(field_add:onnx.AttributeProto.floats)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AttributeProto::_internal_floats() const {
  return floats_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AttributeProto::floats() const {
  // @@protoc_insertion_point(field_list:onnx.AttributeProto.floats)
  return _internal_floats();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AttributeProto::_internal_mutable_floats() {
  return &floats_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AttributeProto::mutable_floats() {
  // @@protoc_insertion_point(field_mutable_list:onnx.AttributeProto.floats)
  return _internal_mutable_floats();
}

// repeated int64 ints = 8;
inline int AttributeProto::_internal_ints_size() const {
  return ints_.size();
}
inline int AttributeProto::ints_size() const {
  return _internal_ints_size();
}
inline void AttributeProto::clear_ints() {
  ints_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AttributeProto::_internal_ints(int index) const {
  return ints_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AttributeProto::ints(int index) const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.ints)
  return _internal_ints(index);
}
inline void AttributeProto::set_ints(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  ints_.Set(index, value);
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.ints)
}
inline void AttributeProto::_internal_add_ints(::PROTOBUF_NAMESPACE_ID::int64 value) {
  ints_.Add(value);
}
inline void AttributeProto::add_ints(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_add_ints(value);
  // @@protoc_insertion_point(field_add:onnx.AttributeProto.ints)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
AttributeProto::_internal_ints() const {
  return ints_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
AttributeProto::ints() const {
  // @@protoc_insertion_point(field_list:onnx.AttributeProto.ints)
  return _internal_ints();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
AttributeProto::_internal_mutable_ints() {
  return &ints_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
AttributeProto::mutable_ints() {
  // @@protoc_insertion_point(field_mutable_list:onnx.AttributeProto.ints)
  return _internal_mutable_ints();
}

// repeated bytes strings = 9;
inline int AttributeProto::_internal_strings_size() const {
  return strings_.size();
}
inline int AttributeProto::strings_size() const {
  return _internal_strings_size();
}
inline void AttributeProto::clear_strings() {
  strings_.Clear();
}
inline std::string* AttributeProto::add_strings() {
  // @@protoc_insertion_point(field_add_mutable:onnx.AttributeProto.strings)
  return _internal_add_strings();
}
inline const std::string& AttributeProto::_internal_strings(int index) const {
  return strings_.Get(index);
}
inline const std::string& AttributeProto::strings(int index) const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.strings)
  return _internal_strings(index);
}
inline std::string* AttributeProto::mutable_strings(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.strings)
  return strings_.Mutable(index);
}
inline void AttributeProto::set_strings(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.strings)
  strings_.Mutable(index)->assign(value);
}
inline void AttributeProto::set_strings(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:onnx.AttributeProto.strings)
  strings_.Mutable(index)->assign(std::move(value));
}
inline void AttributeProto::set_strings(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  strings_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:onnx.AttributeProto.strings)
}
inline void AttributeProto::set_strings(int index, const void* value, size_t size) {
  strings_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:onnx.AttributeProto.strings)
}
inline std::string* AttributeProto::_internal_add_strings() {
  return strings_.Add();
}
inline void AttributeProto::add_strings(const std::string& value) {
  strings_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:onnx.AttributeProto.strings)
}
inline void AttributeProto::add_strings(std::string&& value) {
  strings_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:onnx.AttributeProto.strings)
}
inline void AttributeProto::add_strings(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  strings_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:onnx.AttributeProto.strings)
}
inline void AttributeProto::add_strings(const void* value, size_t size) {
  strings_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:onnx.AttributeProto.strings)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
AttributeProto::strings() const {
  // @@protoc_insertion_point(field_list:onnx.AttributeProto.strings)
  return strings_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
AttributeProto::mutable_strings() {
  // @@protoc_insertion_point(field_mutable_list:onnx.AttributeProto.strings)
  return &strings_;
}

// repeated .onnx.TensorProto tensors = 10;
inline int AttributeProto::_internal_tensors_size() const {
  return tensors_.size();
}
inline int AttributeProto::tensors_size() const {
  return _internal_tensors_size();
}
inline void AttributeProto::clear_tensors() {
  tensors_.Clear();
}
inline ::onnx::TensorProto* AttributeProto::mutable_tensors(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.tensors)
  return tensors_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto >*
AttributeProto::mutable_tensors() {
  // @@protoc_insertion_point(field_mutable_list:onnx.AttributeProto.tensors)
  return &tensors_;
}
inline const ::onnx::TensorProto& AttributeProto::_internal_tensors(int index) const {
  return tensors_.Get(index);
}
inline const ::onnx::TensorProto& AttributeProto::tensors(int index) const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.tensors)
  return _internal_tensors(index);
}
inline ::onnx::TensorProto* AttributeProto::_internal_add_tensors() {
  return tensors_.Add();
}
inline ::onnx::TensorProto* AttributeProto::add_tensors() {
  // @@protoc_insertion_point(field_add:onnx.AttributeProto.tensors)
  return _internal_add_tensors();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto >&
AttributeProto::tensors() const {
  // @@protoc_insertion_point(field_list:onnx.AttributeProto.tensors)
  return tensors_;
}

// repeated .onnx.GraphProto graphs = 11;
inline int AttributeProto::_internal_graphs_size() const {
  return graphs_.size();
}
inline int AttributeProto::graphs_size() const {
  return _internal_graphs_size();
}
inline void AttributeProto::clear_graphs() {
  graphs_.Clear();
}
inline ::onnx::GraphProto* AttributeProto::mutable_graphs(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.graphs)
  return graphs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::GraphProto >*
AttributeProto::mutable_graphs() {
  // @@protoc_insertion_point(field_mutable_list:onnx.AttributeProto.graphs)
  return &graphs_;
}
inline const ::onnx::GraphProto& AttributeProto::_internal_graphs(int index) const {
  return graphs_.Get(index);
}
inline const ::onnx::GraphProto& AttributeProto::graphs(int index) const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.graphs)
  return _internal_graphs(index);
}
inline ::onnx::GraphProto* AttributeProto::_internal_add_graphs() {
  return graphs_.Add();
}
inline ::onnx::GraphProto* AttributeProto::add_graphs() {
  // @@protoc_insertion_point(field_add:onnx.AttributeProto.graphs)
  return _internal_add_graphs();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::GraphProto >&
AttributeProto::graphs() const {
  // @@protoc_insertion_point(field_list:onnx.AttributeProto.graphs)
  return graphs_;
}

// repeated .onnx.SparseTensorProto sparse_tensors = 23;
inline int AttributeProto::_internal_sparse_tensors_size() const {
  return sparse_tensors_.size();
}
inline int AttributeProto::sparse_tensors_size() const {
  return _internal_sparse_tensors_size();
}
inline void AttributeProto::clear_sparse_tensors() {
  sparse_tensors_.Clear();
}
inline ::onnx::SparseTensorProto* AttributeProto::mutable_sparse_tensors(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.sparse_tensors)
  return sparse_tensors_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto >*
AttributeProto::mutable_sparse_tensors() {
  // @@protoc_insertion_point(field_mutable_list:onnx.AttributeProto.sparse_tensors)
  return &sparse_tensors_;
}
inline const ::onnx::SparseTensorProto& AttributeProto::_internal_sparse_tensors(int index) const {
  return sparse_tensors_.Get(index);
}
inline const ::onnx::SparseTensorProto& AttributeProto::sparse_tensors(int index) const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.sparse_tensors)
  return _internal_sparse_tensors(index);
}
inline ::onnx::SparseTensorProto* AttributeProto::_internal_add_sparse_tensors() {
  return sparse_tensors_.Add();
}
inline ::onnx::SparseTensorProto* AttributeProto::add_sparse_tensors() {
  // @@protoc_insertion_point(field_add:onnx.AttributeProto.sparse_tensors)
  return _internal_add_sparse_tensors();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto >&
AttributeProto::sparse_tensors() const {
  // @@protoc_insertion_point(field_list:onnx.AttributeProto.sparse_tensors)
  return sparse_tensors_;
}

// repeated .onnx.TypeProto type_protos = 15;
inline int AttributeProto::_internal_type_protos_size() const {
  return type_protos_.size();
}
inline int AttributeProto::type_protos_size() const {
  return _internal_type_protos_size();
}
inline void AttributeProto::clear_type_protos() {
  type_protos_.Clear();
}
inline ::onnx::TypeProto* AttributeProto::mutable_type_protos(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.AttributeProto.type_protos)
  return type_protos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TypeProto >*
AttributeProto::mutable_type_protos() {
  // @@protoc_insertion_point(field_mutable_list:onnx.AttributeProto.type_protos)
  return &type_protos_;
}
inline const ::onnx::TypeProto& AttributeProto::_internal_type_protos(int index) const {
  return type_protos_.Get(index);
}
inline const ::onnx::TypeProto& AttributeProto::type_protos(int index) const {
  // @@protoc_insertion_point(field_get:onnx.AttributeProto.type_protos)
  return _internal_type_protos(index);
}
inline ::onnx::TypeProto* AttributeProto::_internal_add_type_protos() {
  return type_protos_.Add();
}
inline ::onnx::TypeProto* AttributeProto::add_type_protos() {
  // @@protoc_insertion_point(field_add:onnx.AttributeProto.type_protos)
  return _internal_add_type_protos();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TypeProto >&
AttributeProto::type_protos() const {
  // @@protoc_insertion_point(field_list:onnx.AttributeProto.type_protos)
  return type_protos_;
}

// -------------------------------------------------------------------

// ValueInfoProto

// optional string name = 1;
inline bool ValueInfoProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ValueInfoProto::has_name() const {
  return _internal_has_name();
}
inline void ValueInfoProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ValueInfoProto::name() const {
  // @@protoc_insertion_point(field_get:onnx.ValueInfoProto.name)
  return _internal_name();
}
inline void ValueInfoProto::set_name(const std::string& value) {
  _internal_set_name(value);
  // @@protoc_insertion_point(field_set:onnx.ValueInfoProto.name)
}
inline std::string* ValueInfoProto::mutable_name() {
  // @@protoc_insertion_point(field_mutable:onnx.ValueInfoProto.name)
  return _internal_mutable_name();
}
inline const std::string& ValueInfoProto::_internal_name() const {
  return name_.GetNoArena();
}
inline void ValueInfoProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void ValueInfoProto::set_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.ValueInfoProto.name)
}
inline void ValueInfoProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.ValueInfoProto.name)
}
inline void ValueInfoProto::set_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.ValueInfoProto.name)
}
inline std::string* ValueInfoProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ValueInfoProto::release_name() {
  // @@protoc_insertion_point(field_release:onnx.ValueInfoProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ValueInfoProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:onnx.ValueInfoProto.name)
}

// optional .onnx.TypeProto type = 2;
inline bool ValueInfoProto::_internal_has_type() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || type_ != nullptr);
  return value;
}
inline bool ValueInfoProto::has_type() const {
  return _internal_has_type();
}
inline void ValueInfoProto::clear_type() {
  if (type_ != nullptr) type_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
inline const ::onnx::TypeProto& ValueInfoProto::_internal_type() const {
  const ::onnx::TypeProto* p = type_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TypeProto*>(
      &::onnx::_TypeProto_default_instance_);
}
inline const ::onnx::TypeProto& ValueInfoProto::type() const {
  // @@protoc_insertion_point(field_get:onnx.ValueInfoProto.type)
  return _internal_type();
}
inline ::onnx::TypeProto* ValueInfoProto::release_type() {
  // @@protoc_insertion_point(field_release:onnx.ValueInfoProto.type)
  _has_bits_[0] &= ~0x00000004u;
  ::onnx::TypeProto* temp = type_;
  type_ = nullptr;
  return temp;
}
inline ::onnx::TypeProto* ValueInfoProto::_internal_mutable_type() {
  _has_bits_[0] |= 0x00000004u;
  if (type_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TypeProto>(GetArenaNoVirtual());
    type_ = p;
  }
  return type_;
}
inline ::onnx::TypeProto* ValueInfoProto::mutable_type() {
  // @@protoc_insertion_point(field_mutable:onnx.ValueInfoProto.type)
  return _internal_mutable_type();
}
inline void ValueInfoProto::set_allocated_type(::onnx::TypeProto* type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete type_;
  }
  if (type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type, submessage_arena);
    }
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  type_ = type;
  // @@protoc_insertion_point(field_set_allocated:onnx.ValueInfoProto.type)
}

// optional string doc_string = 3;
inline bool ValueInfoProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool ValueInfoProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void ValueInfoProto::clear_doc_string() {
  doc_string_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& ValueInfoProto::doc_string() const {
  // @@protoc_insertion_point(field_get:onnx.ValueInfoProto.doc_string)
  return _internal_doc_string();
}
inline void ValueInfoProto::set_doc_string(const std::string& value) {
  _internal_set_doc_string(value);
  // @@protoc_insertion_point(field_set:onnx.ValueInfoProto.doc_string)
}
inline std::string* ValueInfoProto::mutable_doc_string() {
  // @@protoc_insertion_point(field_mutable:onnx.ValueInfoProto.doc_string)
  return _internal_mutable_doc_string();
}
inline const std::string& ValueInfoProto::_internal_doc_string() const {
  return doc_string_.GetNoArena();
}
inline void ValueInfoProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void ValueInfoProto::set_doc_string(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.ValueInfoProto.doc_string)
}
inline void ValueInfoProto::set_doc_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.ValueInfoProto.doc_string)
}
inline void ValueInfoProto::set_doc_string(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.ValueInfoProto.doc_string)
}
inline std::string* ValueInfoProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000002u;
  return doc_string_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ValueInfoProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:onnx.ValueInfoProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return doc_string_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ValueInfoProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  doc_string_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string);
  // @@protoc_insertion_point(field_set_allocated:onnx.ValueInfoProto.doc_string)
}

// -------------------------------------------------------------------

// NodeProto

// repeated string input = 1;
inline int NodeProto::_internal_input_size() const {
  return input_.size();
}
inline int NodeProto::input_size() const {
  return _internal_input_size();
}
inline void NodeProto::clear_input() {
  input_.Clear();
}
inline std::string* NodeProto::add_input() {
  // @@protoc_insertion_point(field_add_mutable:onnx.NodeProto.input)
  return _internal_add_input();
}
inline const std::string& NodeProto::_internal_input(int index) const {
  return input_.Get(index);
}
inline const std::string& NodeProto::input(int index) const {
  // @@protoc_insertion_point(field_get:onnx.NodeProto.input)
  return _internal_input(index);
}
inline std::string* NodeProto::mutable_input(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.NodeProto.input)
  return input_.Mutable(index);
}
inline void NodeProto::set_input(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:onnx.NodeProto.input)
  input_.Mutable(index)->assign(value);
}
inline void NodeProto::set_input(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:onnx.NodeProto.input)
  input_.Mutable(index)->assign(std::move(value));
}
inline void NodeProto::set_input(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:onnx.NodeProto.input)
}
inline void NodeProto::set_input(int index, const char* value, size_t size) {
  input_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:onnx.NodeProto.input)
}
inline std::string* NodeProto::_internal_add_input() {
  return input_.Add();
}
inline void NodeProto::add_input(const std::string& value) {
  input_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:onnx.NodeProto.input)
}
inline void NodeProto::add_input(std::string&& value) {
  input_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:onnx.NodeProto.input)
}
inline void NodeProto::add_input(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:onnx.NodeProto.input)
}
inline void NodeProto::add_input(const char* value, size_t size) {
  input_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:onnx.NodeProto.input)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
NodeProto::input() const {
  // @@protoc_insertion_point(field_list:onnx.NodeProto.input)
  return input_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
NodeProto::mutable_input() {
  // @@protoc_insertion_point(field_mutable_list:onnx.NodeProto.input)
  return &input_;
}

// repeated string output = 2;
inline int NodeProto::_internal_output_size() const {
  return output_.size();
}
inline int NodeProto::output_size() const {
  return _internal_output_size();
}
inline void NodeProto::clear_output() {
  output_.Clear();
}
inline std::string* NodeProto::add_output() {
  // @@protoc_insertion_point(field_add_mutable:onnx.NodeProto.output)
  return _internal_add_output();
}
inline const std::string& NodeProto::_internal_output(int index) const {
  return output_.Get(index);
}
inline const std::string& NodeProto::output(int index) const {
  // @@protoc_insertion_point(field_get:onnx.NodeProto.output)
  return _internal_output(index);
}
inline std::string* NodeProto::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.NodeProto.output)
  return output_.Mutable(index);
}
inline void NodeProto::set_output(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:onnx.NodeProto.output)
  output_.Mutable(index)->assign(value);
}
inline void NodeProto::set_output(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:onnx.NodeProto.output)
  output_.Mutable(index)->assign(std::move(value));
}
inline void NodeProto::set_output(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:onnx.NodeProto.output)
}
inline void NodeProto::set_output(int index, const char* value, size_t size) {
  output_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:onnx.NodeProto.output)
}
inline std::string* NodeProto::_internal_add_output() {
  return output_.Add();
}
inline void NodeProto::add_output(const std::string& value) {
  output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:onnx.NodeProto.output)
}
inline void NodeProto::add_output(std::string&& value) {
  output_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:onnx.NodeProto.output)
}
inline void NodeProto::add_output(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:onnx.NodeProto.output)
}
inline void NodeProto::add_output(const char* value, size_t size) {
  output_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:onnx.NodeProto.output)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
NodeProto::output() const {
  // @@protoc_insertion_point(field_list:onnx.NodeProto.output)
  return output_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
NodeProto::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:onnx.NodeProto.output)
  return &output_;
}

// optional string name = 3;
inline bool NodeProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool NodeProto::has_name() const {
  return _internal_has_name();
}
inline void NodeProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& NodeProto::name() const {
  // @@protoc_insertion_point(field_get:onnx.NodeProto.name)
  return _internal_name();
}
inline void NodeProto::set_name(const std::string& value) {
  _internal_set_name(value);
  // @@protoc_insertion_point(field_set:onnx.NodeProto.name)
}
inline std::string* NodeProto::mutable_name() {
  // @@protoc_insertion_point(field_mutable:onnx.NodeProto.name)
  return _internal_mutable_name();
}
inline const std::string& NodeProto::_internal_name() const {
  return name_.GetNoArena();
}
inline void NodeProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void NodeProto::set_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.NodeProto.name)
}
inline void NodeProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.NodeProto.name)
}
inline void NodeProto::set_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.NodeProto.name)
}
inline std::string* NodeProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* NodeProto::release_name() {
  // @@protoc_insertion_point(field_release:onnx.NodeProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void NodeProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:onnx.NodeProto.name)
}

// optional string op_type = 4;
inline bool NodeProto::_internal_has_op_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool NodeProto::has_op_type() const {
  return _internal_has_op_type();
}
inline void NodeProto::clear_op_type() {
  op_type_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& NodeProto::op_type() const {
  // @@protoc_insertion_point(field_get:onnx.NodeProto.op_type)
  return _internal_op_type();
}
inline void NodeProto::set_op_type(const std::string& value) {
  _internal_set_op_type(value);
  // @@protoc_insertion_point(field_set:onnx.NodeProto.op_type)
}
inline std::string* NodeProto::mutable_op_type() {
  // @@protoc_insertion_point(field_mutable:onnx.NodeProto.op_type)
  return _internal_mutable_op_type();
}
inline const std::string& NodeProto::_internal_op_type() const {
  return op_type_.GetNoArena();
}
inline void NodeProto::_internal_set_op_type(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  op_type_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void NodeProto::set_op_type(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  op_type_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.NodeProto.op_type)
}
inline void NodeProto::set_op_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  op_type_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.NodeProto.op_type)
}
inline void NodeProto::set_op_type(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  op_type_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.NodeProto.op_type)
}
inline std::string* NodeProto::_internal_mutable_op_type() {
  _has_bits_[0] |= 0x00000002u;
  return op_type_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* NodeProto::release_op_type() {
  // @@protoc_insertion_point(field_release:onnx.NodeProto.op_type)
  if (!_internal_has_op_type()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return op_type_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void NodeProto::set_allocated_op_type(std::string* op_type) {
  if (op_type != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  op_type_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_type);
  // @@protoc_insertion_point(field_set_allocated:onnx.NodeProto.op_type)
}

// optional string domain = 7;
inline bool NodeProto::_internal_has_domain() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool NodeProto::has_domain() const {
  return _internal_has_domain();
}
inline void NodeProto::clear_domain() {
  domain_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000008u;
}
inline const std::string& NodeProto::domain() const {
  // @@protoc_insertion_point(field_get:onnx.NodeProto.domain)
  return _internal_domain();
}
inline void NodeProto::set_domain(const std::string& value) {
  _internal_set_domain(value);
  // @@protoc_insertion_point(field_set:onnx.NodeProto.domain)
}
inline std::string* NodeProto::mutable_domain() {
  // @@protoc_insertion_point(field_mutable:onnx.NodeProto.domain)
  return _internal_mutable_domain();
}
inline const std::string& NodeProto::_internal_domain() const {
  return domain_.GetNoArena();
}
inline void NodeProto::_internal_set_domain(const std::string& value) {
  _has_bits_[0] |= 0x00000008u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void NodeProto::set_domain(std::string&& value) {
  _has_bits_[0] |= 0x00000008u;
  domain_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.NodeProto.domain)
}
inline void NodeProto::set_domain(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000008u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.NodeProto.domain)
}
inline void NodeProto::set_domain(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000008u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.NodeProto.domain)
}
inline std::string* NodeProto::_internal_mutable_domain() {
  _has_bits_[0] |= 0x00000008u;
  return domain_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* NodeProto::release_domain() {
  // @@protoc_insertion_point(field_release:onnx.NodeProto.domain)
  if (!_internal_has_domain()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000008u;
  return domain_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void NodeProto::set_allocated_domain(std::string* domain) {
  if (domain != nullptr) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  domain_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), domain);
  // @@protoc_insertion_point(field_set_allocated:onnx.NodeProto.domain)
}

// repeated .onnx.AttributeProto attribute = 5;
inline int NodeProto::_internal_attribute_size() const {
  return attribute_.size();
}
inline int NodeProto::attribute_size() const {
  return _internal_attribute_size();
}
inline void NodeProto::clear_attribute() {
  attribute_.Clear();
}
inline ::onnx::AttributeProto* NodeProto::mutable_attribute(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.NodeProto.attribute)
  return attribute_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::AttributeProto >*
NodeProto::mutable_attribute() {
  // @@protoc_insertion_point(field_mutable_list:onnx.NodeProto.attribute)
  return &attribute_;
}
inline const ::onnx::AttributeProto& NodeProto::_internal_attribute(int index) const {
  return attribute_.Get(index);
}
inline const ::onnx::AttributeProto& NodeProto::attribute(int index) const {
  // @@protoc_insertion_point(field_get:onnx.NodeProto.attribute)
  return _internal_attribute(index);
}
inline ::onnx::AttributeProto* NodeProto::_internal_add_attribute() {
  return attribute_.Add();
}
inline ::onnx::AttributeProto* NodeProto::add_attribute() {
  // @@protoc_insertion_point(field_add:onnx.NodeProto.attribute)
  return _internal_add_attribute();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::AttributeProto >&
NodeProto::attribute() const {
  // @@protoc_insertion_point(field_list:onnx.NodeProto.attribute)
  return attribute_;
}

// optional string doc_string = 6;
inline bool NodeProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool NodeProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void NodeProto::clear_doc_string() {
  doc_string_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& NodeProto::doc_string() const {
  // @@protoc_insertion_point(field_get:onnx.NodeProto.doc_string)
  return _internal_doc_string();
}
inline void NodeProto::set_doc_string(const std::string& value) {
  _internal_set_doc_string(value);
  // @@protoc_insertion_point(field_set:onnx.NodeProto.doc_string)
}
inline std::string* NodeProto::mutable_doc_string() {
  // @@protoc_insertion_point(field_mutable:onnx.NodeProto.doc_string)
  return _internal_mutable_doc_string();
}
inline const std::string& NodeProto::_internal_doc_string() const {
  return doc_string_.GetNoArena();
}
inline void NodeProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void NodeProto::set_doc_string(std::string&& value) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.NodeProto.doc_string)
}
inline void NodeProto::set_doc_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.NodeProto.doc_string)
}
inline void NodeProto::set_doc_string(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.NodeProto.doc_string)
}
inline std::string* NodeProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000004u;
  return doc_string_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* NodeProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:onnx.NodeProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  return doc_string_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void NodeProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  doc_string_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string);
  // @@protoc_insertion_point(field_set_allocated:onnx.NodeProto.doc_string)
}

// -------------------------------------------------------------------

// TrainingInfoProto

// optional .onnx.GraphProto initialization = 1;
inline bool TrainingInfoProto::_internal_has_initialization() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || initialization_ != nullptr);
  return value;
}
inline bool TrainingInfoProto::has_initialization() const {
  return _internal_has_initialization();
}
inline void TrainingInfoProto::clear_initialization() {
  if (initialization_ != nullptr) initialization_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::onnx::GraphProto& TrainingInfoProto::_internal_initialization() const {
  const ::onnx::GraphProto* p = initialization_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::GraphProto*>(
      &::onnx::_GraphProto_default_instance_);
}
inline const ::onnx::GraphProto& TrainingInfoProto::initialization() const {
  // @@protoc_insertion_point(field_get:onnx.TrainingInfoProto.initialization)
  return _internal_initialization();
}
inline ::onnx::GraphProto* TrainingInfoProto::release_initialization() {
  // @@protoc_insertion_point(field_release:onnx.TrainingInfoProto.initialization)
  _has_bits_[0] &= ~0x00000001u;
  ::onnx::GraphProto* temp = initialization_;
  initialization_ = nullptr;
  return temp;
}
inline ::onnx::GraphProto* TrainingInfoProto::_internal_mutable_initialization() {
  _has_bits_[0] |= 0x00000001u;
  if (initialization_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::GraphProto>(GetArenaNoVirtual());
    initialization_ = p;
  }
  return initialization_;
}
inline ::onnx::GraphProto* TrainingInfoProto::mutable_initialization() {
  // @@protoc_insertion_point(field_mutable:onnx.TrainingInfoProto.initialization)
  return _internal_mutable_initialization();
}
inline void TrainingInfoProto::set_allocated_initialization(::onnx::GraphProto* initialization) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete initialization_;
  }
  if (initialization) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      initialization = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, initialization, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  initialization_ = initialization;
  // @@protoc_insertion_point(field_set_allocated:onnx.TrainingInfoProto.initialization)
}

// optional .onnx.GraphProto algorithm = 2;
inline bool TrainingInfoProto::_internal_has_algorithm() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || algorithm_ != nullptr);
  return value;
}
inline bool TrainingInfoProto::has_algorithm() const {
  return _internal_has_algorithm();
}
inline void TrainingInfoProto::clear_algorithm() {
  if (algorithm_ != nullptr) algorithm_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::onnx::GraphProto& TrainingInfoProto::_internal_algorithm() const {
  const ::onnx::GraphProto* p = algorithm_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::GraphProto*>(
      &::onnx::_GraphProto_default_instance_);
}
inline const ::onnx::GraphProto& TrainingInfoProto::algorithm() const {
  // @@protoc_insertion_point(field_get:onnx.TrainingInfoProto.algorithm)
  return _internal_algorithm();
}
inline ::onnx::GraphProto* TrainingInfoProto::release_algorithm() {
  // @@protoc_insertion_point(field_release:onnx.TrainingInfoProto.algorithm)
  _has_bits_[0] &= ~0x00000002u;
  ::onnx::GraphProto* temp = algorithm_;
  algorithm_ = nullptr;
  return temp;
}
inline ::onnx::GraphProto* TrainingInfoProto::_internal_mutable_algorithm() {
  _has_bits_[0] |= 0x00000002u;
  if (algorithm_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::GraphProto>(GetArenaNoVirtual());
    algorithm_ = p;
  }
  return algorithm_;
}
inline ::onnx::GraphProto* TrainingInfoProto::mutable_algorithm() {
  // @@protoc_insertion_point(field_mutable:onnx.TrainingInfoProto.algorithm)
  return _internal_mutable_algorithm();
}
inline void TrainingInfoProto::set_allocated_algorithm(::onnx::GraphProto* algorithm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete algorithm_;
  }
  if (algorithm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      algorithm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, algorithm, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  algorithm_ = algorithm;
  // @@protoc_insertion_point(field_set_allocated:onnx.TrainingInfoProto.algorithm)
}

// repeated .onnx.StringStringEntryProto initialization_binding = 3;
inline int TrainingInfoProto::_internal_initialization_binding_size() const {
  return initialization_binding_.size();
}
inline int TrainingInfoProto::initialization_binding_size() const {
  return _internal_initialization_binding_size();
}
inline void TrainingInfoProto::clear_initialization_binding() {
  initialization_binding_.Clear();
}
inline ::onnx::StringStringEntryProto* TrainingInfoProto::mutable_initialization_binding(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.TrainingInfoProto.initialization_binding)
  return initialization_binding_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
TrainingInfoProto::mutable_initialization_binding() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TrainingInfoProto.initialization_binding)
  return &initialization_binding_;
}
inline const ::onnx::StringStringEntryProto& TrainingInfoProto::_internal_initialization_binding(int index) const {
  return initialization_binding_.Get(index);
}
inline const ::onnx::StringStringEntryProto& TrainingInfoProto::initialization_binding(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TrainingInfoProto.initialization_binding)
  return _internal_initialization_binding(index);
}
inline ::onnx::StringStringEntryProto* TrainingInfoProto::_internal_add_initialization_binding() {
  return initialization_binding_.Add();
}
inline ::onnx::StringStringEntryProto* TrainingInfoProto::add_initialization_binding() {
  // @@protoc_insertion_point(field_add:onnx.TrainingInfoProto.initialization_binding)
  return _internal_add_initialization_binding();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
TrainingInfoProto::initialization_binding() const {
  // @@protoc_insertion_point(field_list:onnx.TrainingInfoProto.initialization_binding)
  return initialization_binding_;
}

// repeated .onnx.StringStringEntryProto update_binding = 4;
inline int TrainingInfoProto::_internal_update_binding_size() const {
  return update_binding_.size();
}
inline int TrainingInfoProto::update_binding_size() const {
  return _internal_update_binding_size();
}
inline void TrainingInfoProto::clear_update_binding() {
  update_binding_.Clear();
}
inline ::onnx::StringStringEntryProto* TrainingInfoProto::mutable_update_binding(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.TrainingInfoProto.update_binding)
  return update_binding_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
TrainingInfoProto::mutable_update_binding() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TrainingInfoProto.update_binding)
  return &update_binding_;
}
inline const ::onnx::StringStringEntryProto& TrainingInfoProto::_internal_update_binding(int index) const {
  return update_binding_.Get(index);
}
inline const ::onnx::StringStringEntryProto& TrainingInfoProto::update_binding(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TrainingInfoProto.update_binding)
  return _internal_update_binding(index);
}
inline ::onnx::StringStringEntryProto* TrainingInfoProto::_internal_add_update_binding() {
  return update_binding_.Add();
}
inline ::onnx::StringStringEntryProto* TrainingInfoProto::add_update_binding() {
  // @@protoc_insertion_point(field_add:onnx.TrainingInfoProto.update_binding)
  return _internal_add_update_binding();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
TrainingInfoProto::update_binding() const {
  // @@protoc_insertion_point(field_list:onnx.TrainingInfoProto.update_binding)
  return update_binding_;
}

// -------------------------------------------------------------------

// ModelProto

// optional int64 ir_version = 1;
inline bool ModelProto::_internal_has_ir_version() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool ModelProto::has_ir_version() const {
  return _internal_has_ir_version();
}
inline void ModelProto::clear_ir_version() {
  ir_version_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000020u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto::_internal_ir_version() const {
  return ir_version_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto::ir_version() const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.ir_version)
  return _internal_ir_version();
}
inline void ModelProto::_internal_set_ir_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000020u;
  ir_version_ = value;
}
inline void ModelProto::set_ir_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_ir_version(value);
  // @@protoc_insertion_point(field_set:onnx.ModelProto.ir_version)
}

// repeated .onnx.OperatorSetIdProto opset_import = 8;
inline int ModelProto::_internal_opset_import_size() const {
  return opset_import_.size();
}
inline int ModelProto::opset_import_size() const {
  return _internal_opset_import_size();
}
inline void ModelProto::clear_opset_import() {
  opset_import_.Clear();
}
inline ::onnx::OperatorSetIdProto* ModelProto::mutable_opset_import(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.ModelProto.opset_import)
  return opset_import_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto >*
ModelProto::mutable_opset_import() {
  // @@protoc_insertion_point(field_mutable_list:onnx.ModelProto.opset_import)
  return &opset_import_;
}
inline const ::onnx::OperatorSetIdProto& ModelProto::_internal_opset_import(int index) const {
  return opset_import_.Get(index);
}
inline const ::onnx::OperatorSetIdProto& ModelProto::opset_import(int index) const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.opset_import)
  return _internal_opset_import(index);
}
inline ::onnx::OperatorSetIdProto* ModelProto::_internal_add_opset_import() {
  return opset_import_.Add();
}
inline ::onnx::OperatorSetIdProto* ModelProto::add_opset_import() {
  // @@protoc_insertion_point(field_add:onnx.ModelProto.opset_import)
  return _internal_add_opset_import();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto >&
ModelProto::opset_import() const {
  // @@protoc_insertion_point(field_list:onnx.ModelProto.opset_import)
  return opset_import_;
}

// optional string producer_name = 2;
inline bool ModelProto::_internal_has_producer_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ModelProto::has_producer_name() const {
  return _internal_has_producer_name();
}
inline void ModelProto::clear_producer_name() {
  producer_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ModelProto::producer_name() const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.producer_name)
  return _internal_producer_name();
}
inline void ModelProto::set_producer_name(const std::string& value) {
  _internal_set_producer_name(value);
  // @@protoc_insertion_point(field_set:onnx.ModelProto.producer_name)
}
inline std::string* ModelProto::mutable_producer_name() {
  // @@protoc_insertion_point(field_mutable:onnx.ModelProto.producer_name)
  return _internal_mutable_producer_name();
}
inline const std::string& ModelProto::_internal_producer_name() const {
  return producer_name_.GetNoArena();
}
inline void ModelProto::_internal_set_producer_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  producer_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void ModelProto::set_producer_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  producer_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.ModelProto.producer_name)
}
inline void ModelProto::set_producer_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  producer_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.ModelProto.producer_name)
}
inline void ModelProto::set_producer_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  producer_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.ModelProto.producer_name)
}
inline std::string* ModelProto::_internal_mutable_producer_name() {
  _has_bits_[0] |= 0x00000001u;
  return producer_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ModelProto::release_producer_name() {
  // @@protoc_insertion_point(field_release:onnx.ModelProto.producer_name)
  if (!_internal_has_producer_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return producer_name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ModelProto::set_allocated_producer_name(std::string* producer_name) {
  if (producer_name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  producer_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), producer_name);
  // @@protoc_insertion_point(field_set_allocated:onnx.ModelProto.producer_name)
}

// optional string producer_version = 3;
inline bool ModelProto::_internal_has_producer_version() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool ModelProto::has_producer_version() const {
  return _internal_has_producer_version();
}
inline void ModelProto::clear_producer_version() {
  producer_version_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& ModelProto::producer_version() const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.producer_version)
  return _internal_producer_version();
}
inline void ModelProto::set_producer_version(const std::string& value) {
  _internal_set_producer_version(value);
  // @@protoc_insertion_point(field_set:onnx.ModelProto.producer_version)
}
inline std::string* ModelProto::mutable_producer_version() {
  // @@protoc_insertion_point(field_mutable:onnx.ModelProto.producer_version)
  return _internal_mutable_producer_version();
}
inline const std::string& ModelProto::_internal_producer_version() const {
  return producer_version_.GetNoArena();
}
inline void ModelProto::_internal_set_producer_version(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  producer_version_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void ModelProto::set_producer_version(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  producer_version_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.ModelProto.producer_version)
}
inline void ModelProto::set_producer_version(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  producer_version_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.ModelProto.producer_version)
}
inline void ModelProto::set_producer_version(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  producer_version_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.ModelProto.producer_version)
}
inline std::string* ModelProto::_internal_mutable_producer_version() {
  _has_bits_[0] |= 0x00000002u;
  return producer_version_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ModelProto::release_producer_version() {
  // @@protoc_insertion_point(field_release:onnx.ModelProto.producer_version)
  if (!_internal_has_producer_version()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return producer_version_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ModelProto::set_allocated_producer_version(std::string* producer_version) {
  if (producer_version != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  producer_version_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), producer_version);
  // @@protoc_insertion_point(field_set_allocated:onnx.ModelProto.producer_version)
}

// optional string domain = 4;
inline bool ModelProto::_internal_has_domain() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool ModelProto::has_domain() const {
  return _internal_has_domain();
}
inline void ModelProto::clear_domain() {
  domain_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& ModelProto::domain() const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.domain)
  return _internal_domain();
}
inline void ModelProto::set_domain(const std::string& value) {
  _internal_set_domain(value);
  // @@protoc_insertion_point(field_set:onnx.ModelProto.domain)
}
inline std::string* ModelProto::mutable_domain() {
  // @@protoc_insertion_point(field_mutable:onnx.ModelProto.domain)
  return _internal_mutable_domain();
}
inline const std::string& ModelProto::_internal_domain() const {
  return domain_.GetNoArena();
}
inline void ModelProto::_internal_set_domain(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void ModelProto::set_domain(std::string&& value) {
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.ModelProto.domain)
}
inline void ModelProto::set_domain(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.ModelProto.domain)
}
inline void ModelProto::set_domain(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.ModelProto.domain)
}
inline std::string* ModelProto::_internal_mutable_domain() {
  _has_bits_[0] |= 0x00000004u;
  return domain_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ModelProto::release_domain() {
  // @@protoc_insertion_point(field_release:onnx.ModelProto.domain)
  if (!_internal_has_domain()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  return domain_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ModelProto::set_allocated_domain(std::string* domain) {
  if (domain != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  domain_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), domain);
  // @@protoc_insertion_point(field_set_allocated:onnx.ModelProto.domain)
}

// optional int64 model_version = 5;
inline bool ModelProto::_internal_has_model_version() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool ModelProto::has_model_version() const {
  return _internal_has_model_version();
}
inline void ModelProto::clear_model_version() {
  model_version_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000040u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto::_internal_model_version() const {
  return model_version_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto::model_version() const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.model_version)
  return _internal_model_version();
}
inline void ModelProto::_internal_set_model_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000040u;
  model_version_ = value;
}
inline void ModelProto::set_model_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_model_version(value);
  // @@protoc_insertion_point(field_set:onnx.ModelProto.model_version)
}

// optional string doc_string = 6;
inline bool ModelProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool ModelProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void ModelProto::clear_doc_string() {
  doc_string_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000008u;
}
inline const std::string& ModelProto::doc_string() const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.doc_string)
  return _internal_doc_string();
}
inline void ModelProto::set_doc_string(const std::string& value) {
  _internal_set_doc_string(value);
  // @@protoc_insertion_point(field_set:onnx.ModelProto.doc_string)
}
inline std::string* ModelProto::mutable_doc_string() {
  // @@protoc_insertion_point(field_mutable:onnx.ModelProto.doc_string)
  return _internal_mutable_doc_string();
}
inline const std::string& ModelProto::_internal_doc_string() const {
  return doc_string_.GetNoArena();
}
inline void ModelProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000008u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void ModelProto::set_doc_string(std::string&& value) {
  _has_bits_[0] |= 0x00000008u;
  doc_string_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.ModelProto.doc_string)
}
inline void ModelProto::set_doc_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000008u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.ModelProto.doc_string)
}
inline void ModelProto::set_doc_string(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000008u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.ModelProto.doc_string)
}
inline std::string* ModelProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000008u;
  return doc_string_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ModelProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:onnx.ModelProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000008u;
  return doc_string_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ModelProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  doc_string_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string);
  // @@protoc_insertion_point(field_set_allocated:onnx.ModelProto.doc_string)
}

// optional .onnx.GraphProto graph = 7;
inline bool ModelProto::_internal_has_graph() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  PROTOBUF_ASSUME(!value || graph_ != nullptr);
  return value;
}
inline bool ModelProto::has_graph() const {
  return _internal_has_graph();
}
inline void ModelProto::clear_graph() {
  if (graph_ != nullptr) graph_->Clear();
  _has_bits_[0] &= ~0x00000010u;
}
inline const ::onnx::GraphProto& ModelProto::_internal_graph() const {
  const ::onnx::GraphProto* p = graph_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::GraphProto*>(
      &::onnx::_GraphProto_default_instance_);
}
inline const ::onnx::GraphProto& ModelProto::graph() const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.graph)
  return _internal_graph();
}
inline ::onnx::GraphProto* ModelProto::release_graph() {
  // @@protoc_insertion_point(field_release:onnx.ModelProto.graph)
  _has_bits_[0] &= ~0x00000010u;
  ::onnx::GraphProto* temp = graph_;
  graph_ = nullptr;
  return temp;
}
inline ::onnx::GraphProto* ModelProto::_internal_mutable_graph() {
  _has_bits_[0] |= 0x00000010u;
  if (graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::GraphProto>(GetArenaNoVirtual());
    graph_ = p;
  }
  return graph_;
}
inline ::onnx::GraphProto* ModelProto::mutable_graph() {
  // @@protoc_insertion_point(field_mutable:onnx.ModelProto.graph)
  return _internal_mutable_graph();
}
inline void ModelProto::set_allocated_graph(::onnx::GraphProto* graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete graph_;
  }
  if (graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph, submessage_arena);
    }
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  graph_ = graph;
  // @@protoc_insertion_point(field_set_allocated:onnx.ModelProto.graph)
}

// repeated .onnx.StringStringEntryProto metadata_props = 14;
inline int ModelProto::_internal_metadata_props_size() const {
  return metadata_props_.size();
}
inline int ModelProto::metadata_props_size() const {
  return _internal_metadata_props_size();
}
inline void ModelProto::clear_metadata_props() {
  metadata_props_.Clear();
}
inline ::onnx::StringStringEntryProto* ModelProto::mutable_metadata_props(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.ModelProto.metadata_props)
  return metadata_props_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
ModelProto::mutable_metadata_props() {
  // @@protoc_insertion_point(field_mutable_list:onnx.ModelProto.metadata_props)
  return &metadata_props_;
}
inline const ::onnx::StringStringEntryProto& ModelProto::_internal_metadata_props(int index) const {
  return metadata_props_.Get(index);
}
inline const ::onnx::StringStringEntryProto& ModelProto::metadata_props(int index) const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.metadata_props)
  return _internal_metadata_props(index);
}
inline ::onnx::StringStringEntryProto* ModelProto::_internal_add_metadata_props() {
  return metadata_props_.Add();
}
inline ::onnx::StringStringEntryProto* ModelProto::add_metadata_props() {
  // @@protoc_insertion_point(field_add:onnx.ModelProto.metadata_props)
  return _internal_add_metadata_props();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
ModelProto::metadata_props() const {
  // @@protoc_insertion_point(field_list:onnx.ModelProto.metadata_props)
  return metadata_props_;
}

// repeated .onnx.TrainingInfoProto training_info = 20;
inline int ModelProto::_internal_training_info_size() const {
  return training_info_.size();
}
inline int ModelProto::training_info_size() const {
  return _internal_training_info_size();
}
inline void ModelProto::clear_training_info() {
  training_info_.Clear();
}
inline ::onnx::TrainingInfoProto* ModelProto::mutable_training_info(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.ModelProto.training_info)
  return training_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TrainingInfoProto >*
ModelProto::mutable_training_info() {
  // @@protoc_insertion_point(field_mutable_list:onnx.ModelProto.training_info)
  return &training_info_;
}
inline const ::onnx::TrainingInfoProto& ModelProto::_internal_training_info(int index) const {
  return training_info_.Get(index);
}
inline const ::onnx::TrainingInfoProto& ModelProto::training_info(int index) const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.training_info)
  return _internal_training_info(index);
}
inline ::onnx::TrainingInfoProto* ModelProto::_internal_add_training_info() {
  return training_info_.Add();
}
inline ::onnx::TrainingInfoProto* ModelProto::add_training_info() {
  // @@protoc_insertion_point(field_add:onnx.ModelProto.training_info)
  return _internal_add_training_info();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TrainingInfoProto >&
ModelProto::training_info() const {
  // @@protoc_insertion_point(field_list:onnx.ModelProto.training_info)
  return training_info_;
}

// repeated .onnx.FunctionProto functions = 25;
inline int ModelProto::_internal_functions_size() const {
  return functions_.size();
}
inline int ModelProto::functions_size() const {
  return _internal_functions_size();
}
inline void ModelProto::clear_functions() {
  functions_.Clear();
}
inline ::onnx::FunctionProto* ModelProto::mutable_functions(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.ModelProto.functions)
  return functions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto >*
ModelProto::mutable_functions() {
  // @@protoc_insertion_point(field_mutable_list:onnx.ModelProto.functions)
  return &functions_;
}
inline const ::onnx::FunctionProto& ModelProto::_internal_functions(int index) const {
  return functions_.Get(index);
}
inline const ::onnx::FunctionProto& ModelProto::functions(int index) const {
  // @@protoc_insertion_point(field_get:onnx.ModelProto.functions)
  return _internal_functions(index);
}
inline ::onnx::FunctionProto* ModelProto::_internal_add_functions() {
  return functions_.Add();
}
inline ::onnx::FunctionProto* ModelProto::add_functions() {
  // @@protoc_insertion_point(field_add:onnx.ModelProto.functions)
  return _internal_add_functions();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::FunctionProto >&
ModelProto::functions() const {
  // @@protoc_insertion_point(field_list:onnx.ModelProto.functions)
  return functions_;
}

// -------------------------------------------------------------------

// StringStringEntryProto

// optional string key = 1;
inline bool StringStringEntryProto::_internal_has_key() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool StringStringEntryProto::has_key() const {
  return _internal_has_key();
}
inline void StringStringEntryProto::clear_key() {
  key_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& StringStringEntryProto::key() const {
  // @@protoc_insertion_point(field_get:onnx.StringStringEntryProto.key)
  return _internal_key();
}
inline void StringStringEntryProto::set_key(const std::string& value) {
  _internal_set_key(value);
  // @@protoc_insertion_point(field_set:onnx.StringStringEntryProto.key)
}
inline std::string* StringStringEntryProto::mutable_key() {
  // @@protoc_insertion_point(field_mutable:onnx.StringStringEntryProto.key)
  return _internal_mutable_key();
}
inline const std::string& StringStringEntryProto::_internal_key() const {
  return key_.GetNoArena();
}
inline void StringStringEntryProto::_internal_set_key(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  key_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void StringStringEntryProto::set_key(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  key_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.StringStringEntryProto.key)
}
inline void StringStringEntryProto::set_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  key_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.StringStringEntryProto.key)
}
inline void StringStringEntryProto::set_key(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  key_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.StringStringEntryProto.key)
}
inline std::string* StringStringEntryProto::_internal_mutable_key() {
  _has_bits_[0] |= 0x00000001u;
  return key_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* StringStringEntryProto::release_key() {
  // @@protoc_insertion_point(field_release:onnx.StringStringEntryProto.key)
  if (!_internal_has_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return key_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void StringStringEntryProto::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  key_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:onnx.StringStringEntryProto.key)
}

// optional string value = 2;
inline bool StringStringEntryProto::_internal_has_value() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool StringStringEntryProto::has_value() const {
  return _internal_has_value();
}
inline void StringStringEntryProto::clear_value() {
  value_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& StringStringEntryProto::value() const {
  // @@protoc_insertion_point(field_get:onnx.StringStringEntryProto.value)
  return _internal_value();
}
inline void StringStringEntryProto::set_value(const std::string& value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:onnx.StringStringEntryProto.value)
}
inline std::string* StringStringEntryProto::mutable_value() {
  // @@protoc_insertion_point(field_mutable:onnx.StringStringEntryProto.value)
  return _internal_mutable_value();
}
inline const std::string& StringStringEntryProto::_internal_value() const {
  return value_.GetNoArena();
}
inline void StringStringEntryProto::_internal_set_value(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  value_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void StringStringEntryProto::set_value(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  value_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.StringStringEntryProto.value)
}
inline void StringStringEntryProto::set_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  value_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.StringStringEntryProto.value)
}
inline void StringStringEntryProto::set_value(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  value_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.StringStringEntryProto.value)
}
inline std::string* StringStringEntryProto::_internal_mutable_value() {
  _has_bits_[0] |= 0x00000002u;
  return value_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* StringStringEntryProto::release_value() {
  // @@protoc_insertion_point(field_release:onnx.StringStringEntryProto.value)
  if (!_internal_has_value()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return value_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void StringStringEntryProto::set_allocated_value(std::string* value) {
  if (value != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  value_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:onnx.StringStringEntryProto.value)
}

// -------------------------------------------------------------------

// TensorAnnotation

// optional string tensor_name = 1;
inline bool TensorAnnotation::_internal_has_tensor_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TensorAnnotation::has_tensor_name() const {
  return _internal_has_tensor_name();
}
inline void TensorAnnotation::clear_tensor_name() {
  tensor_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TensorAnnotation::tensor_name() const {
  // @@protoc_insertion_point(field_get:onnx.TensorAnnotation.tensor_name)
  return _internal_tensor_name();
}
inline void TensorAnnotation::set_tensor_name(const std::string& value) {
  _internal_set_tensor_name(value);
  // @@protoc_insertion_point(field_set:onnx.TensorAnnotation.tensor_name)
}
inline std::string* TensorAnnotation::mutable_tensor_name() {
  // @@protoc_insertion_point(field_mutable:onnx.TensorAnnotation.tensor_name)
  return _internal_mutable_tensor_name();
}
inline const std::string& TensorAnnotation::_internal_tensor_name() const {
  return tensor_name_.GetNoArena();
}
inline void TensorAnnotation::_internal_set_tensor_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  tensor_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void TensorAnnotation::set_tensor_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  tensor_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.TensorAnnotation.tensor_name)
}
inline void TensorAnnotation::set_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  tensor_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.TensorAnnotation.tensor_name)
}
inline void TensorAnnotation::set_tensor_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  tensor_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.TensorAnnotation.tensor_name)
}
inline std::string* TensorAnnotation::_internal_mutable_tensor_name() {
  _has_bits_[0] |= 0x00000001u;
  return tensor_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TensorAnnotation::release_tensor_name() {
  // @@protoc_insertion_point(field_release:onnx.TensorAnnotation.tensor_name)
  if (!_internal_has_tensor_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return tensor_name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TensorAnnotation::set_allocated_tensor_name(std::string* tensor_name) {
  if (tensor_name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  tensor_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tensor_name);
  // @@protoc_insertion_point(field_set_allocated:onnx.TensorAnnotation.tensor_name)
}

// repeated .onnx.StringStringEntryProto quant_parameter_tensor_names = 2;
inline int TensorAnnotation::_internal_quant_parameter_tensor_names_size() const {
  return quant_parameter_tensor_names_.size();
}
inline int TensorAnnotation::quant_parameter_tensor_names_size() const {
  return _internal_quant_parameter_tensor_names_size();
}
inline void TensorAnnotation::clear_quant_parameter_tensor_names() {
  quant_parameter_tensor_names_.Clear();
}
inline ::onnx::StringStringEntryProto* TensorAnnotation::mutable_quant_parameter_tensor_names(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.TensorAnnotation.quant_parameter_tensor_names)
  return quant_parameter_tensor_names_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
TensorAnnotation::mutable_quant_parameter_tensor_names() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorAnnotation.quant_parameter_tensor_names)
  return &quant_parameter_tensor_names_;
}
inline const ::onnx::StringStringEntryProto& TensorAnnotation::_internal_quant_parameter_tensor_names(int index) const {
  return quant_parameter_tensor_names_.Get(index);
}
inline const ::onnx::StringStringEntryProto& TensorAnnotation::quant_parameter_tensor_names(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorAnnotation.quant_parameter_tensor_names)
  return _internal_quant_parameter_tensor_names(index);
}
inline ::onnx::StringStringEntryProto* TensorAnnotation::_internal_add_quant_parameter_tensor_names() {
  return quant_parameter_tensor_names_.Add();
}
inline ::onnx::StringStringEntryProto* TensorAnnotation::add_quant_parameter_tensor_names() {
  // @@protoc_insertion_point(field_add:onnx.TensorAnnotation.quant_parameter_tensor_names)
  return _internal_add_quant_parameter_tensor_names();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
TensorAnnotation::quant_parameter_tensor_names() const {
  // @@protoc_insertion_point(field_list:onnx.TensorAnnotation.quant_parameter_tensor_names)
  return quant_parameter_tensor_names_;
}

// -------------------------------------------------------------------

// GraphProto

// repeated .onnx.NodeProto node = 1;
inline int GraphProto::_internal_node_size() const {
  return node_.size();
}
inline int GraphProto::node_size() const {
  return _internal_node_size();
}
inline void GraphProto::clear_node() {
  node_.Clear();
}
inline ::onnx::NodeProto* GraphProto::mutable_node(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.GraphProto.node)
  return node_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto >*
GraphProto::mutable_node() {
  // @@protoc_insertion_point(field_mutable_list:onnx.GraphProto.node)
  return &node_;
}
inline const ::onnx::NodeProto& GraphProto::_internal_node(int index) const {
  return node_.Get(index);
}
inline const ::onnx::NodeProto& GraphProto::node(int index) const {
  // @@protoc_insertion_point(field_get:onnx.GraphProto.node)
  return _internal_node(index);
}
inline ::onnx::NodeProto* GraphProto::_internal_add_node() {
  return node_.Add();
}
inline ::onnx::NodeProto* GraphProto::add_node() {
  // @@protoc_insertion_point(field_add:onnx.GraphProto.node)
  return _internal_add_node();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto >&
GraphProto::node() const {
  // @@protoc_insertion_point(field_list:onnx.GraphProto.node)
  return node_;
}

// optional string name = 2;
inline bool GraphProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool GraphProto::has_name() const {
  return _internal_has_name();
}
inline void GraphProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& GraphProto::name() const {
  // @@protoc_insertion_point(field_get:onnx.GraphProto.name)
  return _internal_name();
}
inline void GraphProto::set_name(const std::string& value) {
  _internal_set_name(value);
  // @@protoc_insertion_point(field_set:onnx.GraphProto.name)
}
inline std::string* GraphProto::mutable_name() {
  // @@protoc_insertion_point(field_mutable:onnx.GraphProto.name)
  return _internal_mutable_name();
}
inline const std::string& GraphProto::_internal_name() const {
  return name_.GetNoArena();
}
inline void GraphProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void GraphProto::set_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.GraphProto.name)
}
inline void GraphProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.GraphProto.name)
}
inline void GraphProto::set_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.GraphProto.name)
}
inline std::string* GraphProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* GraphProto::release_name() {
  // @@protoc_insertion_point(field_release:onnx.GraphProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void GraphProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:onnx.GraphProto.name)
}

// repeated .onnx.TensorProto initializer = 5;
inline int GraphProto::_internal_initializer_size() const {
  return initializer_.size();
}
inline int GraphProto::initializer_size() const {
  return _internal_initializer_size();
}
inline void GraphProto::clear_initializer() {
  initializer_.Clear();
}
inline ::onnx::TensorProto* GraphProto::mutable_initializer(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.GraphProto.initializer)
  return initializer_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto >*
GraphProto::mutable_initializer() {
  // @@protoc_insertion_point(field_mutable_list:onnx.GraphProto.initializer)
  return &initializer_;
}
inline const ::onnx::TensorProto& GraphProto::_internal_initializer(int index) const {
  return initializer_.Get(index);
}
inline const ::onnx::TensorProto& GraphProto::initializer(int index) const {
  // @@protoc_insertion_point(field_get:onnx.GraphProto.initializer)
  return _internal_initializer(index);
}
inline ::onnx::TensorProto* GraphProto::_internal_add_initializer() {
  return initializer_.Add();
}
inline ::onnx::TensorProto* GraphProto::add_initializer() {
  // @@protoc_insertion_point(field_add:onnx.GraphProto.initializer)
  return _internal_add_initializer();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorProto >&
GraphProto::initializer() const {
  // @@protoc_insertion_point(field_list:onnx.GraphProto.initializer)
  return initializer_;
}

// repeated .onnx.SparseTensorProto sparse_initializer = 15;
inline int GraphProto::_internal_sparse_initializer_size() const {
  return sparse_initializer_.size();
}
inline int GraphProto::sparse_initializer_size() const {
  return _internal_sparse_initializer_size();
}
inline void GraphProto::clear_sparse_initializer() {
  sparse_initializer_.Clear();
}
inline ::onnx::SparseTensorProto* GraphProto::mutable_sparse_initializer(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.GraphProto.sparse_initializer)
  return sparse_initializer_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto >*
GraphProto::mutable_sparse_initializer() {
  // @@protoc_insertion_point(field_mutable_list:onnx.GraphProto.sparse_initializer)
  return &sparse_initializer_;
}
inline const ::onnx::SparseTensorProto& GraphProto::_internal_sparse_initializer(int index) const {
  return sparse_initializer_.Get(index);
}
inline const ::onnx::SparseTensorProto& GraphProto::sparse_initializer(int index) const {
  // @@protoc_insertion_point(field_get:onnx.GraphProto.sparse_initializer)
  return _internal_sparse_initializer(index);
}
inline ::onnx::SparseTensorProto* GraphProto::_internal_add_sparse_initializer() {
  return sparse_initializer_.Add();
}
inline ::onnx::SparseTensorProto* GraphProto::add_sparse_initializer() {
  // @@protoc_insertion_point(field_add:onnx.GraphProto.sparse_initializer)
  return _internal_add_sparse_initializer();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::SparseTensorProto >&
GraphProto::sparse_initializer() const {
  // @@protoc_insertion_point(field_list:onnx.GraphProto.sparse_initializer)
  return sparse_initializer_;
}

// optional string doc_string = 10;
inline bool GraphProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool GraphProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void GraphProto::clear_doc_string() {
  doc_string_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& GraphProto::doc_string() const {
  // @@protoc_insertion_point(field_get:onnx.GraphProto.doc_string)
  return _internal_doc_string();
}
inline void GraphProto::set_doc_string(const std::string& value) {
  _internal_set_doc_string(value);
  // @@protoc_insertion_point(field_set:onnx.GraphProto.doc_string)
}
inline std::string* GraphProto::mutable_doc_string() {
  // @@protoc_insertion_point(field_mutable:onnx.GraphProto.doc_string)
  return _internal_mutable_doc_string();
}
inline const std::string& GraphProto::_internal_doc_string() const {
  return doc_string_.GetNoArena();
}
inline void GraphProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void GraphProto::set_doc_string(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.GraphProto.doc_string)
}
inline void GraphProto::set_doc_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.GraphProto.doc_string)
}
inline void GraphProto::set_doc_string(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.GraphProto.doc_string)
}
inline std::string* GraphProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000002u;
  return doc_string_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* GraphProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:onnx.GraphProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return doc_string_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void GraphProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  doc_string_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string);
  // @@protoc_insertion_point(field_set_allocated:onnx.GraphProto.doc_string)
}

// repeated .onnx.ValueInfoProto input = 11;
inline int GraphProto::_internal_input_size() const {
  return input_.size();
}
inline int GraphProto::input_size() const {
  return _internal_input_size();
}
inline void GraphProto::clear_input() {
  input_.Clear();
}
inline ::onnx::ValueInfoProto* GraphProto::mutable_input(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.GraphProto.input)
  return input_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >*
GraphProto::mutable_input() {
  // @@protoc_insertion_point(field_mutable_list:onnx.GraphProto.input)
  return &input_;
}
inline const ::onnx::ValueInfoProto& GraphProto::_internal_input(int index) const {
  return input_.Get(index);
}
inline const ::onnx::ValueInfoProto& GraphProto::input(int index) const {
  // @@protoc_insertion_point(field_get:onnx.GraphProto.input)
  return _internal_input(index);
}
inline ::onnx::ValueInfoProto* GraphProto::_internal_add_input() {
  return input_.Add();
}
inline ::onnx::ValueInfoProto* GraphProto::add_input() {
  // @@protoc_insertion_point(field_add:onnx.GraphProto.input)
  return _internal_add_input();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >&
GraphProto::input() const {
  // @@protoc_insertion_point(field_list:onnx.GraphProto.input)
  return input_;
}

// repeated .onnx.ValueInfoProto output = 12;
inline int GraphProto::_internal_output_size() const {
  return output_.size();
}
inline int GraphProto::output_size() const {
  return _internal_output_size();
}
inline void GraphProto::clear_output() {
  output_.Clear();
}
inline ::onnx::ValueInfoProto* GraphProto::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.GraphProto.output)
  return output_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >*
GraphProto::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:onnx.GraphProto.output)
  return &output_;
}
inline const ::onnx::ValueInfoProto& GraphProto::_internal_output(int index) const {
  return output_.Get(index);
}
inline const ::onnx::ValueInfoProto& GraphProto::output(int index) const {
  // @@protoc_insertion_point(field_get:onnx.GraphProto.output)
  return _internal_output(index);
}
inline ::onnx::ValueInfoProto* GraphProto::_internal_add_output() {
  return output_.Add();
}
inline ::onnx::ValueInfoProto* GraphProto::add_output() {
  // @@protoc_insertion_point(field_add:onnx.GraphProto.output)
  return _internal_add_output();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >&
GraphProto::output() const {
  // @@protoc_insertion_point(field_list:onnx.GraphProto.output)
  return output_;
}

// repeated .onnx.ValueInfoProto value_info = 13;
inline int GraphProto::_internal_value_info_size() const {
  return value_info_.size();
}
inline int GraphProto::value_info_size() const {
  return _internal_value_info_size();
}
inline void GraphProto::clear_value_info() {
  value_info_.Clear();
}
inline ::onnx::ValueInfoProto* GraphProto::mutable_value_info(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.GraphProto.value_info)
  return value_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >*
GraphProto::mutable_value_info() {
  // @@protoc_insertion_point(field_mutable_list:onnx.GraphProto.value_info)
  return &value_info_;
}
inline const ::onnx::ValueInfoProto& GraphProto::_internal_value_info(int index) const {
  return value_info_.Get(index);
}
inline const ::onnx::ValueInfoProto& GraphProto::value_info(int index) const {
  // @@protoc_insertion_point(field_get:onnx.GraphProto.value_info)
  return _internal_value_info(index);
}
inline ::onnx::ValueInfoProto* GraphProto::_internal_add_value_info() {
  return value_info_.Add();
}
inline ::onnx::ValueInfoProto* GraphProto::add_value_info() {
  // @@protoc_insertion_point(field_add:onnx.GraphProto.value_info)
  return _internal_add_value_info();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::ValueInfoProto >&
GraphProto::value_info() const {
  // @@protoc_insertion_point(field_list:onnx.GraphProto.value_info)
  return value_info_;
}

// repeated .onnx.TensorAnnotation quantization_annotation = 14;
inline int GraphProto::_internal_quantization_annotation_size() const {
  return quantization_annotation_.size();
}
inline int GraphProto::quantization_annotation_size() const {
  return _internal_quantization_annotation_size();
}
inline void GraphProto::clear_quantization_annotation() {
  quantization_annotation_.Clear();
}
inline ::onnx::TensorAnnotation* GraphProto::mutable_quantization_annotation(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.GraphProto.quantization_annotation)
  return quantization_annotation_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorAnnotation >*
GraphProto::mutable_quantization_annotation() {
  // @@protoc_insertion_point(field_mutable_list:onnx.GraphProto.quantization_annotation)
  return &quantization_annotation_;
}
inline const ::onnx::TensorAnnotation& GraphProto::_internal_quantization_annotation(int index) const {
  return quantization_annotation_.Get(index);
}
inline const ::onnx::TensorAnnotation& GraphProto::quantization_annotation(int index) const {
  // @@protoc_insertion_point(field_get:onnx.GraphProto.quantization_annotation)
  return _internal_quantization_annotation(index);
}
inline ::onnx::TensorAnnotation* GraphProto::_internal_add_quantization_annotation() {
  return quantization_annotation_.Add();
}
inline ::onnx::TensorAnnotation* GraphProto::add_quantization_annotation() {
  // @@protoc_insertion_point(field_add:onnx.GraphProto.quantization_annotation)
  return _internal_add_quantization_annotation();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorAnnotation >&
GraphProto::quantization_annotation() const {
  // @@protoc_insertion_point(field_list:onnx.GraphProto.quantization_annotation)
  return quantization_annotation_;
}

// -------------------------------------------------------------------

// TensorProto_Segment

// optional int64 begin = 1;
inline bool TensorProto_Segment::_internal_has_begin() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TensorProto_Segment::has_begin() const {
  return _internal_has_begin();
}
inline void TensorProto_Segment::clear_begin() {
  begin_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000001u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorProto_Segment::_internal_begin() const {
  return begin_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorProto_Segment::begin() const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.Segment.begin)
  return _internal_begin();
}
inline void TensorProto_Segment::_internal_set_begin(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000001u;
  begin_ = value;
}
inline void TensorProto_Segment::set_begin(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_begin(value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.Segment.begin)
}

// optional int64 end = 2;
inline bool TensorProto_Segment::_internal_has_end() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TensorProto_Segment::has_end() const {
  return _internal_has_end();
}
inline void TensorProto_Segment::clear_end() {
  end_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorProto_Segment::_internal_end() const {
  return end_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorProto_Segment::end() const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.Segment.end)
  return _internal_end();
}
inline void TensorProto_Segment::_internal_set_end(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000002u;
  end_ = value;
}
inline void TensorProto_Segment::set_end(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_end(value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.Segment.end)
}

// -------------------------------------------------------------------

// TensorProto

// repeated int64 dims = 1;
inline int TensorProto::_internal_dims_size() const {
  return dims_.size();
}
inline int TensorProto::dims_size() const {
  return _internal_dims_size();
}
inline void TensorProto::clear_dims() {
  dims_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorProto::_internal_dims(int index) const {
  return dims_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorProto::dims(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.dims)
  return _internal_dims(index);
}
inline void TensorProto::set_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.dims)
}
inline void TensorProto::_internal_add_dims(::PROTOBUF_NAMESPACE_ID::int64 value) {
  dims_.Add(value);
}
inline void TensorProto::add_dims(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_add_dims(value);
  // @@protoc_insertion_point(field_add:onnx.TensorProto.dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
TensorProto::_internal_dims() const {
  return dims_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
TensorProto::dims() const {
  // @@protoc_insertion_point(field_list:onnx.TensorProto.dims)
  return _internal_dims();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
TensorProto::_internal_mutable_dims() {
  return &dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
TensorProto::mutable_dims() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorProto.dims)
  return _internal_mutable_dims();
}

// optional int32 data_type = 2;
inline bool TensorProto::_internal_has_data_type() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool TensorProto::has_data_type() const {
  return _internal_has_data_type();
}
inline void TensorProto::clear_data_type() {
  data_type_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TensorProto::_internal_data_type() const {
  return data_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TensorProto::data_type() const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.data_type)
  return _internal_data_type();
}
inline void TensorProto::_internal_set_data_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _has_bits_[0] |= 0x00000010u;
  data_type_ = value;
}
inline void TensorProto::set_data_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_data_type(value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.data_type)
}

// optional .onnx.TensorProto.Segment segment = 3;
inline bool TensorProto::_internal_has_segment() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  PROTOBUF_ASSUME(!value || segment_ != nullptr);
  return value;
}
inline bool TensorProto::has_segment() const {
  return _internal_has_segment();
}
inline void TensorProto::clear_segment() {
  if (segment_ != nullptr) segment_->Clear();
  _has_bits_[0] &= ~0x00000008u;
}
inline const ::onnx::TensorProto_Segment& TensorProto::_internal_segment() const {
  const ::onnx::TensorProto_Segment* p = segment_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TensorProto_Segment*>(
      &::onnx::_TensorProto_Segment_default_instance_);
}
inline const ::onnx::TensorProto_Segment& TensorProto::segment() const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.segment)
  return _internal_segment();
}
inline ::onnx::TensorProto_Segment* TensorProto::release_segment() {
  // @@protoc_insertion_point(field_release:onnx.TensorProto.segment)
  _has_bits_[0] &= ~0x00000008u;
  ::onnx::TensorProto_Segment* temp = segment_;
  segment_ = nullptr;
  return temp;
}
inline ::onnx::TensorProto_Segment* TensorProto::_internal_mutable_segment() {
  _has_bits_[0] |= 0x00000008u;
  if (segment_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TensorProto_Segment>(GetArenaNoVirtual());
    segment_ = p;
  }
  return segment_;
}
inline ::onnx::TensorProto_Segment* TensorProto::mutable_segment() {
  // @@protoc_insertion_point(field_mutable:onnx.TensorProto.segment)
  return _internal_mutable_segment();
}
inline void TensorProto::set_allocated_segment(::onnx::TensorProto_Segment* segment) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete segment_;
  }
  if (segment) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      segment = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, segment, submessage_arena);
    }
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  segment_ = segment;
  // @@protoc_insertion_point(field_set_allocated:onnx.TensorProto.segment)
}

// repeated float float_data = 4 [packed = true];
inline int TensorProto::_internal_float_data_size() const {
  return float_data_.size();
}
inline int TensorProto::float_data_size() const {
  return _internal_float_data_size();
}
inline void TensorProto::clear_float_data() {
  float_data_.Clear();
}
inline float TensorProto::_internal_float_data(int index) const {
  return float_data_.Get(index);
}
inline float TensorProto::float_data(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.float_data)
  return _internal_float_data(index);
}
inline void TensorProto::set_float_data(int index, float value) {
  float_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.float_data)
}
inline void TensorProto::_internal_add_float_data(float value) {
  float_data_.Add(value);
}
inline void TensorProto::add_float_data(float value) {
  _internal_add_float_data(value);
  // @@protoc_insertion_point(field_add:onnx.TensorProto.float_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TensorProto::_internal_float_data() const {
  return float_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TensorProto::float_data() const {
  // @@protoc_insertion_point(field_list:onnx.TensorProto.float_data)
  return _internal_float_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TensorProto::_internal_mutable_float_data() {
  return &float_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TensorProto::mutable_float_data() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorProto.float_data)
  return _internal_mutable_float_data();
}

// repeated int32 int32_data = 5 [packed = true];
inline int TensorProto::_internal_int32_data_size() const {
  return int32_data_.size();
}
inline int TensorProto::int32_data_size() const {
  return _internal_int32_data_size();
}
inline void TensorProto::clear_int32_data() {
  int32_data_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TensorProto::_internal_int32_data(int index) const {
  return int32_data_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TensorProto::int32_data(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.int32_data)
  return _internal_int32_data(index);
}
inline void TensorProto::set_int32_data(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  int32_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.int32_data)
}
inline void TensorProto::_internal_add_int32_data(::PROTOBUF_NAMESPACE_ID::int32 value) {
  int32_data_.Add(value);
}
inline void TensorProto::add_int32_data(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_int32_data(value);
  // @@protoc_insertion_point(field_add:onnx.TensorProto.int32_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
TensorProto::_internal_int32_data() const {
  return int32_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
TensorProto::int32_data() const {
  // @@protoc_insertion_point(field_list:onnx.TensorProto.int32_data)
  return _internal_int32_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
TensorProto::_internal_mutable_int32_data() {
  return &int32_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
TensorProto::mutable_int32_data() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorProto.int32_data)
  return _internal_mutable_int32_data();
}

// repeated bytes string_data = 6;
inline int TensorProto::_internal_string_data_size() const {
  return string_data_.size();
}
inline int TensorProto::string_data_size() const {
  return _internal_string_data_size();
}
inline void TensorProto::clear_string_data() {
  string_data_.Clear();
}
inline std::string* TensorProto::add_string_data() {
  // @@protoc_insertion_point(field_add_mutable:onnx.TensorProto.string_data)
  return _internal_add_string_data();
}
inline const std::string& TensorProto::_internal_string_data(int index) const {
  return string_data_.Get(index);
}
inline const std::string& TensorProto::string_data(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.string_data)
  return _internal_string_data(index);
}
inline std::string* TensorProto::mutable_string_data(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.TensorProto.string_data)
  return string_data_.Mutable(index);
}
inline void TensorProto::set_string_data(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:onnx.TensorProto.string_data)
  string_data_.Mutable(index)->assign(value);
}
inline void TensorProto::set_string_data(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:onnx.TensorProto.string_data)
  string_data_.Mutable(index)->assign(std::move(value));
}
inline void TensorProto::set_string_data(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  string_data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:onnx.TensorProto.string_data)
}
inline void TensorProto::set_string_data(int index, const void* value, size_t size) {
  string_data_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:onnx.TensorProto.string_data)
}
inline std::string* TensorProto::_internal_add_string_data() {
  return string_data_.Add();
}
inline void TensorProto::add_string_data(const std::string& value) {
  string_data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:onnx.TensorProto.string_data)
}
inline void TensorProto::add_string_data(std::string&& value) {
  string_data_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:onnx.TensorProto.string_data)
}
inline void TensorProto::add_string_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  string_data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:onnx.TensorProto.string_data)
}
inline void TensorProto::add_string_data(const void* value, size_t size) {
  string_data_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:onnx.TensorProto.string_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TensorProto::string_data() const {
  // @@protoc_insertion_point(field_list:onnx.TensorProto.string_data)
  return string_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TensorProto::mutable_string_data() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorProto.string_data)
  return &string_data_;
}

// repeated int64 int64_data = 7 [packed = true];
inline int TensorProto::_internal_int64_data_size() const {
  return int64_data_.size();
}
inline int TensorProto::int64_data_size() const {
  return _internal_int64_data_size();
}
inline void TensorProto::clear_int64_data() {
  int64_data_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorProto::_internal_int64_data(int index) const {
  return int64_data_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorProto::int64_data(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.int64_data)
  return _internal_int64_data(index);
}
inline void TensorProto::set_int64_data(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  int64_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.int64_data)
}
inline void TensorProto::_internal_add_int64_data(::PROTOBUF_NAMESPACE_ID::int64 value) {
  int64_data_.Add(value);
}
inline void TensorProto::add_int64_data(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_add_int64_data(value);
  // @@protoc_insertion_point(field_add:onnx.TensorProto.int64_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
TensorProto::_internal_int64_data() const {
  return int64_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
TensorProto::int64_data() const {
  // @@protoc_insertion_point(field_list:onnx.TensorProto.int64_data)
  return _internal_int64_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
TensorProto::_internal_mutable_int64_data() {
  return &int64_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
TensorProto::mutable_int64_data() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorProto.int64_data)
  return _internal_mutable_int64_data();
}

// optional string name = 8;
inline bool TensorProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TensorProto::has_name() const {
  return _internal_has_name();
}
inline void TensorProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TensorProto::name() const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.name)
  return _internal_name();
}
inline void TensorProto::set_name(const std::string& value) {
  _internal_set_name(value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.name)
}
inline std::string* TensorProto::mutable_name() {
  // @@protoc_insertion_point(field_mutable:onnx.TensorProto.name)
  return _internal_mutable_name();
}
inline const std::string& TensorProto::_internal_name() const {
  return name_.GetNoArena();
}
inline void TensorProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void TensorProto::set_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.TensorProto.name)
}
inline void TensorProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.TensorProto.name)
}
inline void TensorProto::set_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.TensorProto.name)
}
inline std::string* TensorProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TensorProto::release_name() {
  // @@protoc_insertion_point(field_release:onnx.TensorProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TensorProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:onnx.TensorProto.name)
}

// optional string doc_string = 12;
inline bool TensorProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool TensorProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void TensorProto::clear_doc_string() {
  doc_string_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& TensorProto::doc_string() const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.doc_string)
  return _internal_doc_string();
}
inline void TensorProto::set_doc_string(const std::string& value) {
  _internal_set_doc_string(value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.doc_string)
}
inline std::string* TensorProto::mutable_doc_string() {
  // @@protoc_insertion_point(field_mutable:onnx.TensorProto.doc_string)
  return _internal_mutable_doc_string();
}
inline const std::string& TensorProto::_internal_doc_string() const {
  return doc_string_.GetNoArena();
}
inline void TensorProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void TensorProto::set_doc_string(std::string&& value) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.TensorProto.doc_string)
}
inline void TensorProto::set_doc_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.TensorProto.doc_string)
}
inline void TensorProto::set_doc_string(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.TensorProto.doc_string)
}
inline std::string* TensorProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000004u;
  return doc_string_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TensorProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:onnx.TensorProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  return doc_string_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TensorProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  doc_string_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string);
  // @@protoc_insertion_point(field_set_allocated:onnx.TensorProto.doc_string)
}

// optional bytes raw_data = 9;
inline bool TensorProto::_internal_has_raw_data() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TensorProto::has_raw_data() const {
  return _internal_has_raw_data();
}
inline void TensorProto::clear_raw_data() {
  raw_data_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& TensorProto::raw_data() const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.raw_data)
  return _internal_raw_data();
}
inline void TensorProto::set_raw_data(const std::string& value) {
  _internal_set_raw_data(value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.raw_data)
}
inline std::string* TensorProto::mutable_raw_data() {
  // @@protoc_insertion_point(field_mutable:onnx.TensorProto.raw_data)
  return _internal_mutable_raw_data();
}
inline const std::string& TensorProto::_internal_raw_data() const {
  return raw_data_.GetNoArena();
}
inline void TensorProto::_internal_set_raw_data(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  raw_data_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void TensorProto::set_raw_data(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  raw_data_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.TensorProto.raw_data)
}
inline void TensorProto::set_raw_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  raw_data_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.TensorProto.raw_data)
}
inline void TensorProto::set_raw_data(const void* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  raw_data_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.TensorProto.raw_data)
}
inline std::string* TensorProto::_internal_mutable_raw_data() {
  _has_bits_[0] |= 0x00000002u;
  return raw_data_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TensorProto::release_raw_data() {
  // @@protoc_insertion_point(field_release:onnx.TensorProto.raw_data)
  if (!_internal_has_raw_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return raw_data_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TensorProto::set_allocated_raw_data(std::string* raw_data) {
  if (raw_data != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  raw_data_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), raw_data);
  // @@protoc_insertion_point(field_set_allocated:onnx.TensorProto.raw_data)
}

// repeated .onnx.StringStringEntryProto external_data = 13;
inline int TensorProto::_internal_external_data_size() const {
  return external_data_.size();
}
inline int TensorProto::external_data_size() const {
  return _internal_external_data_size();
}
inline void TensorProto::clear_external_data() {
  external_data_.Clear();
}
inline ::onnx::StringStringEntryProto* TensorProto::mutable_external_data(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.TensorProto.external_data)
  return external_data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >*
TensorProto::mutable_external_data() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorProto.external_data)
  return &external_data_;
}
inline const ::onnx::StringStringEntryProto& TensorProto::_internal_external_data(int index) const {
  return external_data_.Get(index);
}
inline const ::onnx::StringStringEntryProto& TensorProto::external_data(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.external_data)
  return _internal_external_data(index);
}
inline ::onnx::StringStringEntryProto* TensorProto::_internal_add_external_data() {
  return external_data_.Add();
}
inline ::onnx::StringStringEntryProto* TensorProto::add_external_data() {
  // @@protoc_insertion_point(field_add:onnx.TensorProto.external_data)
  return _internal_add_external_data();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::StringStringEntryProto >&
TensorProto::external_data() const {
  // @@protoc_insertion_point(field_list:onnx.TensorProto.external_data)
  return external_data_;
}

// optional .onnx.TensorProto.DataLocation data_location = 14;
inline bool TensorProto::_internal_has_data_location() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool TensorProto::has_data_location() const {
  return _internal_has_data_location();
}
inline void TensorProto::clear_data_location() {
  data_location_ = 0;
  _has_bits_[0] &= ~0x00000020u;
}
inline ::onnx::TensorProto_DataLocation TensorProto::_internal_data_location() const {
  return static_cast< ::onnx::TensorProto_DataLocation >(data_location_);
}
inline ::onnx::TensorProto_DataLocation TensorProto::data_location() const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.data_location)
  return _internal_data_location();
}
inline void TensorProto::_internal_set_data_location(::onnx::TensorProto_DataLocation value) {
  assert(::onnx::TensorProto_DataLocation_IsValid(value));
  _has_bits_[0] |= 0x00000020u;
  data_location_ = value;
}
inline void TensorProto::set_data_location(::onnx::TensorProto_DataLocation value) {
  _internal_set_data_location(value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.data_location)
}

// repeated double double_data = 10 [packed = true];
inline int TensorProto::_internal_double_data_size() const {
  return double_data_.size();
}
inline int TensorProto::double_data_size() const {
  return _internal_double_data_size();
}
inline void TensorProto::clear_double_data() {
  double_data_.Clear();
}
inline double TensorProto::_internal_double_data(int index) const {
  return double_data_.Get(index);
}
inline double TensorProto::double_data(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.double_data)
  return _internal_double_data(index);
}
inline void TensorProto::set_double_data(int index, double value) {
  double_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.double_data)
}
inline void TensorProto::_internal_add_double_data(double value) {
  double_data_.Add(value);
}
inline void TensorProto::add_double_data(double value) {
  _internal_add_double_data(value);
  // @@protoc_insertion_point(field_add:onnx.TensorProto.double_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TensorProto::_internal_double_data() const {
  return double_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TensorProto::double_data() const {
  // @@protoc_insertion_point(field_list:onnx.TensorProto.double_data)
  return _internal_double_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TensorProto::_internal_mutable_double_data() {
  return &double_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TensorProto::mutable_double_data() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorProto.double_data)
  return _internal_mutable_double_data();
}

// repeated uint64 uint64_data = 11 [packed = true];
inline int TensorProto::_internal_uint64_data_size() const {
  return uint64_data_.size();
}
inline int TensorProto::uint64_data_size() const {
  return _internal_uint64_data_size();
}
inline void TensorProto::clear_uint64_data() {
  uint64_data_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 TensorProto::_internal_uint64_data(int index) const {
  return uint64_data_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 TensorProto::uint64_data(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorProto.uint64_data)
  return _internal_uint64_data(index);
}
inline void TensorProto::set_uint64_data(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value) {
  uint64_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:onnx.TensorProto.uint64_data)
}
inline void TensorProto::_internal_add_uint64_data(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  uint64_data_.Add(value);
}
inline void TensorProto::add_uint64_data(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_add_uint64_data(value);
  // @@protoc_insertion_point(field_add:onnx.TensorProto.uint64_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
TensorProto::_internal_uint64_data() const {
  return uint64_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
TensorProto::uint64_data() const {
  // @@protoc_insertion_point(field_list:onnx.TensorProto.uint64_data)
  return _internal_uint64_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
TensorProto::_internal_mutable_uint64_data() {
  return &uint64_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
TensorProto::mutable_uint64_data() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorProto.uint64_data)
  return _internal_mutable_uint64_data();
}

// -------------------------------------------------------------------

// SparseTensorProto

// optional .onnx.TensorProto values = 1;
inline bool SparseTensorProto::_internal_has_values() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || values_ != nullptr);
  return value;
}
inline bool SparseTensorProto::has_values() const {
  return _internal_has_values();
}
inline void SparseTensorProto::clear_values() {
  if (values_ != nullptr) values_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::onnx::TensorProto& SparseTensorProto::_internal_values() const {
  const ::onnx::TensorProto* p = values_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TensorProto*>(
      &::onnx::_TensorProto_default_instance_);
}
inline const ::onnx::TensorProto& SparseTensorProto::values() const {
  // @@protoc_insertion_point(field_get:onnx.SparseTensorProto.values)
  return _internal_values();
}
inline ::onnx::TensorProto* SparseTensorProto::release_values() {
  // @@protoc_insertion_point(field_release:onnx.SparseTensorProto.values)
  _has_bits_[0] &= ~0x00000001u;
  ::onnx::TensorProto* temp = values_;
  values_ = nullptr;
  return temp;
}
inline ::onnx::TensorProto* SparseTensorProto::_internal_mutable_values() {
  _has_bits_[0] |= 0x00000001u;
  if (values_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TensorProto>(GetArenaNoVirtual());
    values_ = p;
  }
  return values_;
}
inline ::onnx::TensorProto* SparseTensorProto::mutable_values() {
  // @@protoc_insertion_point(field_mutable:onnx.SparseTensorProto.values)
  return _internal_mutable_values();
}
inline void SparseTensorProto::set_allocated_values(::onnx::TensorProto* values) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete values_;
  }
  if (values) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      values = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, values, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  values_ = values;
  // @@protoc_insertion_point(field_set_allocated:onnx.SparseTensorProto.values)
}

// optional .onnx.TensorProto indices = 2;
inline bool SparseTensorProto::_internal_has_indices() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || indices_ != nullptr);
  return value;
}
inline bool SparseTensorProto::has_indices() const {
  return _internal_has_indices();
}
inline void SparseTensorProto::clear_indices() {
  if (indices_ != nullptr) indices_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::onnx::TensorProto& SparseTensorProto::_internal_indices() const {
  const ::onnx::TensorProto* p = indices_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TensorProto*>(
      &::onnx::_TensorProto_default_instance_);
}
inline const ::onnx::TensorProto& SparseTensorProto::indices() const {
  // @@protoc_insertion_point(field_get:onnx.SparseTensorProto.indices)
  return _internal_indices();
}
inline ::onnx::TensorProto* SparseTensorProto::release_indices() {
  // @@protoc_insertion_point(field_release:onnx.SparseTensorProto.indices)
  _has_bits_[0] &= ~0x00000002u;
  ::onnx::TensorProto* temp = indices_;
  indices_ = nullptr;
  return temp;
}
inline ::onnx::TensorProto* SparseTensorProto::_internal_mutable_indices() {
  _has_bits_[0] |= 0x00000002u;
  if (indices_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TensorProto>(GetArenaNoVirtual());
    indices_ = p;
  }
  return indices_;
}
inline ::onnx::TensorProto* SparseTensorProto::mutable_indices() {
  // @@protoc_insertion_point(field_mutable:onnx.SparseTensorProto.indices)
  return _internal_mutable_indices();
}
inline void SparseTensorProto::set_allocated_indices(::onnx::TensorProto* indices) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete indices_;
  }
  if (indices) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      indices = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, indices, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  indices_ = indices;
  // @@protoc_insertion_point(field_set_allocated:onnx.SparseTensorProto.indices)
}

// repeated int64 dims = 3;
inline int SparseTensorProto::_internal_dims_size() const {
  return dims_.size();
}
inline int SparseTensorProto::dims_size() const {
  return _internal_dims_size();
}
inline void SparseTensorProto::clear_dims() {
  dims_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SparseTensorProto::_internal_dims(int index) const {
  return dims_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SparseTensorProto::dims(int index) const {
  // @@protoc_insertion_point(field_get:onnx.SparseTensorProto.dims)
  return _internal_dims(index);
}
inline void SparseTensorProto::set_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:onnx.SparseTensorProto.dims)
}
inline void SparseTensorProto::_internal_add_dims(::PROTOBUF_NAMESPACE_ID::int64 value) {
  dims_.Add(value);
}
inline void SparseTensorProto::add_dims(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_add_dims(value);
  // @@protoc_insertion_point(field_add:onnx.SparseTensorProto.dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
SparseTensorProto::_internal_dims() const {
  return dims_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
SparseTensorProto::dims() const {
  // @@protoc_insertion_point(field_list:onnx.SparseTensorProto.dims)
  return _internal_dims();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
SparseTensorProto::_internal_mutable_dims() {
  return &dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
SparseTensorProto::mutable_dims() {
  // @@protoc_insertion_point(field_mutable_list:onnx.SparseTensorProto.dims)
  return _internal_mutable_dims();
}

// -------------------------------------------------------------------

// TensorShapeProto_Dimension

// optional int64 dim_value = 1;
inline bool TensorShapeProto_Dimension::_internal_has_dim_value() const {
  return value_case() == kDimValue;
}
inline bool TensorShapeProto_Dimension::has_dim_value() const {
  return _internal_has_dim_value();
}
inline void TensorShapeProto_Dimension::set_has_dim_value() {
  _oneof_case_[0] = kDimValue;
}
inline void TensorShapeProto_Dimension::clear_dim_value() {
  if (_internal_has_dim_value()) {
    value_.dim_value_ = PROTOBUF_LONGLONG(0);
    clear_has_value();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorShapeProto_Dimension::_internal_dim_value() const {
  if (_internal_has_dim_value()) {
    return value_.dim_value_;
  }
  return PROTOBUF_LONGLONG(0);
}
inline void TensorShapeProto_Dimension::_internal_set_dim_value(::PROTOBUF_NAMESPACE_ID::int64 value) {
  if (!_internal_has_dim_value()) {
    clear_value();
    set_has_dim_value();
  }
  value_.dim_value_ = value;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorShapeProto_Dimension::dim_value() const {
  // @@protoc_insertion_point(field_get:onnx.TensorShapeProto.Dimension.dim_value)
  return _internal_dim_value();
}
inline void TensorShapeProto_Dimension::set_dim_value(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_dim_value(value);
  // @@protoc_insertion_point(field_set:onnx.TensorShapeProto.Dimension.dim_value)
}

// optional string dim_param = 2;
inline bool TensorShapeProto_Dimension::_internal_has_dim_param() const {
  return value_case() == kDimParam;
}
inline bool TensorShapeProto_Dimension::has_dim_param() const {
  return _internal_has_dim_param();
}
inline void TensorShapeProto_Dimension::set_has_dim_param() {
  _oneof_case_[0] = kDimParam;
}
inline void TensorShapeProto_Dimension::clear_dim_param() {
  if (_internal_has_dim_param()) {
    value_.dim_param_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
    clear_has_value();
  }
}
inline const std::string& TensorShapeProto_Dimension::dim_param() const {
  // @@protoc_insertion_point(field_get:onnx.TensorShapeProto.Dimension.dim_param)
  return _internal_dim_param();
}
inline void TensorShapeProto_Dimension::set_dim_param(const std::string& value) {
  _internal_set_dim_param(value);
  // @@protoc_insertion_point(field_set:onnx.TensorShapeProto.Dimension.dim_param)
}
inline std::string* TensorShapeProto_Dimension::mutable_dim_param() {
  // @@protoc_insertion_point(field_mutable:onnx.TensorShapeProto.Dimension.dim_param)
  return _internal_mutable_dim_param();
}
inline const std::string& TensorShapeProto_Dimension::_internal_dim_param() const {
  if (_internal_has_dim_param()) {
    return value_.dim_param_.GetNoArena();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void TensorShapeProto_Dimension::_internal_set_dim_param(const std::string& value) {
  if (!_internal_has_dim_param()) {
    clear_value();
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.dim_param_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void TensorShapeProto_Dimension::set_dim_param(std::string&& value) {
  // @@protoc_insertion_point(field_set:onnx.TensorShapeProto.Dimension.dim_param)
  if (!_internal_has_dim_param()) {
    clear_value();
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.dim_param_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.TensorShapeProto.Dimension.dim_param)
}
inline void TensorShapeProto_Dimension::set_dim_param(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!_internal_has_dim_param()) {
    clear_value();
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.dim_param_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.TensorShapeProto.Dimension.dim_param)
}
inline void TensorShapeProto_Dimension::set_dim_param(const char* value, size_t size) {
  if (!_internal_has_dim_param()) {
    clear_value();
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.dim_param_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.TensorShapeProto.Dimension.dim_param)
}
inline std::string* TensorShapeProto_Dimension::_internal_mutable_dim_param() {
  if (!_internal_has_dim_param()) {
    clear_value();
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.dim_param_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TensorShapeProto_Dimension::release_dim_param() {
  // @@protoc_insertion_point(field_release:onnx.TensorShapeProto.Dimension.dim_param)
  if (_internal_has_dim_param()) {
    clear_has_value();
    return value_.dim_param_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  } else {
    return nullptr;
  }
}
inline void TensorShapeProto_Dimension::set_allocated_dim_param(std::string* dim_param) {
  if (has_value()) {
    clear_value();
  }
  if (dim_param != nullptr) {
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(dim_param);
  }
  // @@protoc_insertion_point(field_set_allocated:onnx.TensorShapeProto.Dimension.dim_param)
}

// optional string denotation = 3;
inline bool TensorShapeProto_Dimension::_internal_has_denotation() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TensorShapeProto_Dimension::has_denotation() const {
  return _internal_has_denotation();
}
inline void TensorShapeProto_Dimension::clear_denotation() {
  denotation_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TensorShapeProto_Dimension::denotation() const {
  // @@protoc_insertion_point(field_get:onnx.TensorShapeProto.Dimension.denotation)
  return _internal_denotation();
}
inline void TensorShapeProto_Dimension::set_denotation(const std::string& value) {
  _internal_set_denotation(value);
  // @@protoc_insertion_point(field_set:onnx.TensorShapeProto.Dimension.denotation)
}
inline std::string* TensorShapeProto_Dimension::mutable_denotation() {
  // @@protoc_insertion_point(field_mutable:onnx.TensorShapeProto.Dimension.denotation)
  return _internal_mutable_denotation();
}
inline const std::string& TensorShapeProto_Dimension::_internal_denotation() const {
  return denotation_.GetNoArena();
}
inline void TensorShapeProto_Dimension::_internal_set_denotation(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  denotation_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void TensorShapeProto_Dimension::set_denotation(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  denotation_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.TensorShapeProto.Dimension.denotation)
}
inline void TensorShapeProto_Dimension::set_denotation(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  denotation_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.TensorShapeProto.Dimension.denotation)
}
inline void TensorShapeProto_Dimension::set_denotation(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  denotation_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.TensorShapeProto.Dimension.denotation)
}
inline std::string* TensorShapeProto_Dimension::_internal_mutable_denotation() {
  _has_bits_[0] |= 0x00000001u;
  return denotation_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TensorShapeProto_Dimension::release_denotation() {
  // @@protoc_insertion_point(field_release:onnx.TensorShapeProto.Dimension.denotation)
  if (!_internal_has_denotation()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return denotation_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TensorShapeProto_Dimension::set_allocated_denotation(std::string* denotation) {
  if (denotation != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  denotation_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), denotation);
  // @@protoc_insertion_point(field_set_allocated:onnx.TensorShapeProto.Dimension.denotation)
}

inline bool TensorShapeProto_Dimension::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void TensorShapeProto_Dimension::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline TensorShapeProto_Dimension::ValueCase TensorShapeProto_Dimension::value_case() const {
  return TensorShapeProto_Dimension::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// TensorShapeProto

// repeated .onnx.TensorShapeProto.Dimension dim = 1;
inline int TensorShapeProto::_internal_dim_size() const {
  return dim_.size();
}
inline int TensorShapeProto::dim_size() const {
  return _internal_dim_size();
}
inline void TensorShapeProto::clear_dim() {
  dim_.Clear();
}
inline ::onnx::TensorShapeProto_Dimension* TensorShapeProto::mutable_dim(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.TensorShapeProto.dim)
  return dim_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorShapeProto_Dimension >*
TensorShapeProto::mutable_dim() {
  // @@protoc_insertion_point(field_mutable_list:onnx.TensorShapeProto.dim)
  return &dim_;
}
inline const ::onnx::TensorShapeProto_Dimension& TensorShapeProto::_internal_dim(int index) const {
  return dim_.Get(index);
}
inline const ::onnx::TensorShapeProto_Dimension& TensorShapeProto::dim(int index) const {
  // @@protoc_insertion_point(field_get:onnx.TensorShapeProto.dim)
  return _internal_dim(index);
}
inline ::onnx::TensorShapeProto_Dimension* TensorShapeProto::_internal_add_dim() {
  return dim_.Add();
}
inline ::onnx::TensorShapeProto_Dimension* TensorShapeProto::add_dim() {
  // @@protoc_insertion_point(field_add:onnx.TensorShapeProto.dim)
  return _internal_add_dim();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::TensorShapeProto_Dimension >&
TensorShapeProto::dim() const {
  // @@protoc_insertion_point(field_list:onnx.TensorShapeProto.dim)
  return dim_;
}

// -------------------------------------------------------------------

// TypeProto_Tensor

// optional int32 elem_type = 1;
inline bool TypeProto_Tensor::_internal_has_elem_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TypeProto_Tensor::has_elem_type() const {
  return _internal_has_elem_type();
}
inline void TypeProto_Tensor::clear_elem_type() {
  elem_type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TypeProto_Tensor::_internal_elem_type() const {
  return elem_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TypeProto_Tensor::elem_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.Tensor.elem_type)
  return _internal_elem_type();
}
inline void TypeProto_Tensor::_internal_set_elem_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _has_bits_[0] |= 0x00000002u;
  elem_type_ = value;
}
inline void TypeProto_Tensor::set_elem_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_elem_type(value);
  // @@protoc_insertion_point(field_set:onnx.TypeProto.Tensor.elem_type)
}

// optional .onnx.TensorShapeProto shape = 2;
inline bool TypeProto_Tensor::_internal_has_shape() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || shape_ != nullptr);
  return value;
}
inline bool TypeProto_Tensor::has_shape() const {
  return _internal_has_shape();
}
inline void TypeProto_Tensor::clear_shape() {
  if (shape_ != nullptr) shape_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::onnx::TensorShapeProto& TypeProto_Tensor::_internal_shape() const {
  const ::onnx::TensorShapeProto* p = shape_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TensorShapeProto*>(
      &::onnx::_TensorShapeProto_default_instance_);
}
inline const ::onnx::TensorShapeProto& TypeProto_Tensor::shape() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.Tensor.shape)
  return _internal_shape();
}
inline ::onnx::TensorShapeProto* TypeProto_Tensor::release_shape() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.Tensor.shape)
  _has_bits_[0] &= ~0x00000001u;
  ::onnx::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::onnx::TensorShapeProto* TypeProto_Tensor::_internal_mutable_shape() {
  _has_bits_[0] |= 0x00000001u;
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  return shape_;
}
inline ::onnx::TensorShapeProto* TypeProto_Tensor::mutable_shape() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.Tensor.shape)
  return _internal_mutable_shape();
}
inline void TypeProto_Tensor::set_allocated_shape(::onnx::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete shape_;
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.Tensor.shape)
}

// -------------------------------------------------------------------

// TypeProto_Sequence

// optional .onnx.TypeProto elem_type = 1;
inline bool TypeProto_Sequence::_internal_has_elem_type() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || elem_type_ != nullptr);
  return value;
}
inline bool TypeProto_Sequence::has_elem_type() const {
  return _internal_has_elem_type();
}
inline void TypeProto_Sequence::clear_elem_type() {
  if (elem_type_ != nullptr) elem_type_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::onnx::TypeProto& TypeProto_Sequence::_internal_elem_type() const {
  const ::onnx::TypeProto* p = elem_type_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TypeProto*>(
      &::onnx::_TypeProto_default_instance_);
}
inline const ::onnx::TypeProto& TypeProto_Sequence::elem_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.Sequence.elem_type)
  return _internal_elem_type();
}
inline ::onnx::TypeProto* TypeProto_Sequence::release_elem_type() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.Sequence.elem_type)
  _has_bits_[0] &= ~0x00000001u;
  ::onnx::TypeProto* temp = elem_type_;
  elem_type_ = nullptr;
  return temp;
}
inline ::onnx::TypeProto* TypeProto_Sequence::_internal_mutable_elem_type() {
  _has_bits_[0] |= 0x00000001u;
  if (elem_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TypeProto>(GetArenaNoVirtual());
    elem_type_ = p;
  }
  return elem_type_;
}
inline ::onnx::TypeProto* TypeProto_Sequence::mutable_elem_type() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.Sequence.elem_type)
  return _internal_mutable_elem_type();
}
inline void TypeProto_Sequence::set_allocated_elem_type(::onnx::TypeProto* elem_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete elem_type_;
  }
  if (elem_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      elem_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, elem_type, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  elem_type_ = elem_type;
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.Sequence.elem_type)
}

// -------------------------------------------------------------------

// TypeProto_Map

// optional int32 key_type = 1;
inline bool TypeProto_Map::_internal_has_key_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TypeProto_Map::has_key_type() const {
  return _internal_has_key_type();
}
inline void TypeProto_Map::clear_key_type() {
  key_type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TypeProto_Map::_internal_key_type() const {
  return key_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TypeProto_Map::key_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.Map.key_type)
  return _internal_key_type();
}
inline void TypeProto_Map::_internal_set_key_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _has_bits_[0] |= 0x00000002u;
  key_type_ = value;
}
inline void TypeProto_Map::set_key_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_key_type(value);
  // @@protoc_insertion_point(field_set:onnx.TypeProto.Map.key_type)
}

// optional .onnx.TypeProto value_type = 2;
inline bool TypeProto_Map::_internal_has_value_type() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || value_type_ != nullptr);
  return value;
}
inline bool TypeProto_Map::has_value_type() const {
  return _internal_has_value_type();
}
inline void TypeProto_Map::clear_value_type() {
  if (value_type_ != nullptr) value_type_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::onnx::TypeProto& TypeProto_Map::_internal_value_type() const {
  const ::onnx::TypeProto* p = value_type_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TypeProto*>(
      &::onnx::_TypeProto_default_instance_);
}
inline const ::onnx::TypeProto& TypeProto_Map::value_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.Map.value_type)
  return _internal_value_type();
}
inline ::onnx::TypeProto* TypeProto_Map::release_value_type() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.Map.value_type)
  _has_bits_[0] &= ~0x00000001u;
  ::onnx::TypeProto* temp = value_type_;
  value_type_ = nullptr;
  return temp;
}
inline ::onnx::TypeProto* TypeProto_Map::_internal_mutable_value_type() {
  _has_bits_[0] |= 0x00000001u;
  if (value_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TypeProto>(GetArenaNoVirtual());
    value_type_ = p;
  }
  return value_type_;
}
inline ::onnx::TypeProto* TypeProto_Map::mutable_value_type() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.Map.value_type)
  return _internal_mutable_value_type();
}
inline void TypeProto_Map::set_allocated_value_type(::onnx::TypeProto* value_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete value_type_;
  }
  if (value_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      value_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value_type, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  value_type_ = value_type;
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.Map.value_type)
}

// -------------------------------------------------------------------

// TypeProto_Optional

// optional .onnx.TypeProto elem_type = 1;
inline bool TypeProto_Optional::_internal_has_elem_type() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || elem_type_ != nullptr);
  return value;
}
inline bool TypeProto_Optional::has_elem_type() const {
  return _internal_has_elem_type();
}
inline void TypeProto_Optional::clear_elem_type() {
  if (elem_type_ != nullptr) elem_type_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::onnx::TypeProto& TypeProto_Optional::_internal_elem_type() const {
  const ::onnx::TypeProto* p = elem_type_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TypeProto*>(
      &::onnx::_TypeProto_default_instance_);
}
inline const ::onnx::TypeProto& TypeProto_Optional::elem_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.Optional.elem_type)
  return _internal_elem_type();
}
inline ::onnx::TypeProto* TypeProto_Optional::release_elem_type() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.Optional.elem_type)
  _has_bits_[0] &= ~0x00000001u;
  ::onnx::TypeProto* temp = elem_type_;
  elem_type_ = nullptr;
  return temp;
}
inline ::onnx::TypeProto* TypeProto_Optional::_internal_mutable_elem_type() {
  _has_bits_[0] |= 0x00000001u;
  if (elem_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TypeProto>(GetArenaNoVirtual());
    elem_type_ = p;
  }
  return elem_type_;
}
inline ::onnx::TypeProto* TypeProto_Optional::mutable_elem_type() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.Optional.elem_type)
  return _internal_mutable_elem_type();
}
inline void TypeProto_Optional::set_allocated_elem_type(::onnx::TypeProto* elem_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete elem_type_;
  }
  if (elem_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      elem_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, elem_type, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  elem_type_ = elem_type;
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.Optional.elem_type)
}

// -------------------------------------------------------------------

// TypeProto_SparseTensor

// optional int32 elem_type = 1;
inline bool TypeProto_SparseTensor::_internal_has_elem_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TypeProto_SparseTensor::has_elem_type() const {
  return _internal_has_elem_type();
}
inline void TypeProto_SparseTensor::clear_elem_type() {
  elem_type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TypeProto_SparseTensor::_internal_elem_type() const {
  return elem_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TypeProto_SparseTensor::elem_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.SparseTensor.elem_type)
  return _internal_elem_type();
}
inline void TypeProto_SparseTensor::_internal_set_elem_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _has_bits_[0] |= 0x00000002u;
  elem_type_ = value;
}
inline void TypeProto_SparseTensor::set_elem_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_elem_type(value);
  // @@protoc_insertion_point(field_set:onnx.TypeProto.SparseTensor.elem_type)
}

// optional .onnx.TensorShapeProto shape = 2;
inline bool TypeProto_SparseTensor::_internal_has_shape() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || shape_ != nullptr);
  return value;
}
inline bool TypeProto_SparseTensor::has_shape() const {
  return _internal_has_shape();
}
inline void TypeProto_SparseTensor::clear_shape() {
  if (shape_ != nullptr) shape_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::onnx::TensorShapeProto& TypeProto_SparseTensor::_internal_shape() const {
  const ::onnx::TensorShapeProto* p = shape_;
  return p != nullptr ? *p : *reinterpret_cast<const ::onnx::TensorShapeProto*>(
      &::onnx::_TensorShapeProto_default_instance_);
}
inline const ::onnx::TensorShapeProto& TypeProto_SparseTensor::shape() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.SparseTensor.shape)
  return _internal_shape();
}
inline ::onnx::TensorShapeProto* TypeProto_SparseTensor::release_shape() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.SparseTensor.shape)
  _has_bits_[0] &= ~0x00000001u;
  ::onnx::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::onnx::TensorShapeProto* TypeProto_SparseTensor::_internal_mutable_shape() {
  _has_bits_[0] |= 0x00000001u;
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::onnx::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  return shape_;
}
inline ::onnx::TensorShapeProto* TypeProto_SparseTensor::mutable_shape() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.SparseTensor.shape)
  return _internal_mutable_shape();
}
inline void TypeProto_SparseTensor::set_allocated_shape(::onnx::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete shape_;
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.SparseTensor.shape)
}

// -------------------------------------------------------------------

// TypeProto_Opaque

// optional string domain = 1;
inline bool TypeProto_Opaque::_internal_has_domain() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TypeProto_Opaque::has_domain() const {
  return _internal_has_domain();
}
inline void TypeProto_Opaque::clear_domain() {
  domain_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TypeProto_Opaque::domain() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.Opaque.domain)
  return _internal_domain();
}
inline void TypeProto_Opaque::set_domain(const std::string& value) {
  _internal_set_domain(value);
  // @@protoc_insertion_point(field_set:onnx.TypeProto.Opaque.domain)
}
inline std::string* TypeProto_Opaque::mutable_domain() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.Opaque.domain)
  return _internal_mutable_domain();
}
inline const std::string& TypeProto_Opaque::_internal_domain() const {
  return domain_.GetNoArena();
}
inline void TypeProto_Opaque::_internal_set_domain(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void TypeProto_Opaque::set_domain(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  domain_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.TypeProto.Opaque.domain)
}
inline void TypeProto_Opaque::set_domain(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.TypeProto.Opaque.domain)
}
inline void TypeProto_Opaque::set_domain(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.TypeProto.Opaque.domain)
}
inline std::string* TypeProto_Opaque::_internal_mutable_domain() {
  _has_bits_[0] |= 0x00000001u;
  return domain_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TypeProto_Opaque::release_domain() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.Opaque.domain)
  if (!_internal_has_domain()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return domain_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TypeProto_Opaque::set_allocated_domain(std::string* domain) {
  if (domain != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  domain_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), domain);
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.Opaque.domain)
}

// optional string name = 2;
inline bool TypeProto_Opaque::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TypeProto_Opaque::has_name() const {
  return _internal_has_name();
}
inline void TypeProto_Opaque::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& TypeProto_Opaque::name() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.Opaque.name)
  return _internal_name();
}
inline void TypeProto_Opaque::set_name(const std::string& value) {
  _internal_set_name(value);
  // @@protoc_insertion_point(field_set:onnx.TypeProto.Opaque.name)
}
inline std::string* TypeProto_Opaque::mutable_name() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.Opaque.name)
  return _internal_mutable_name();
}
inline const std::string& TypeProto_Opaque::_internal_name() const {
  return name_.GetNoArena();
}
inline void TypeProto_Opaque::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void TypeProto_Opaque::set_name(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.TypeProto.Opaque.name)
}
inline void TypeProto_Opaque::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.TypeProto.Opaque.name)
}
inline void TypeProto_Opaque::set_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.TypeProto.Opaque.name)
}
inline std::string* TypeProto_Opaque::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000002u;
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TypeProto_Opaque::release_name() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.Opaque.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TypeProto_Opaque::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.Opaque.name)
}

// -------------------------------------------------------------------

// TypeProto

// optional .onnx.TypeProto.Tensor tensor_type = 1;
inline bool TypeProto::_internal_has_tensor_type() const {
  return value_case() == kTensorType;
}
inline bool TypeProto::has_tensor_type() const {
  return _internal_has_tensor_type();
}
inline void TypeProto::set_has_tensor_type() {
  _oneof_case_[0] = kTensorType;
}
inline void TypeProto::clear_tensor_type() {
  if (_internal_has_tensor_type()) {
    delete value_.tensor_type_;
    clear_has_value();
  }
}
inline ::onnx::TypeProto_Tensor* TypeProto::release_tensor_type() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.tensor_type)
  if (_internal_has_tensor_type()) {
    clear_has_value();
      ::onnx::TypeProto_Tensor* temp = value_.tensor_type_;
    value_.tensor_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::onnx::TypeProto_Tensor& TypeProto::_internal_tensor_type() const {
  return _internal_has_tensor_type()
      ? *value_.tensor_type_
      : *reinterpret_cast< ::onnx::TypeProto_Tensor*>(&::onnx::_TypeProto_Tensor_default_instance_);
}
inline const ::onnx::TypeProto_Tensor& TypeProto::tensor_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.tensor_type)
  return _internal_tensor_type();
}
inline ::onnx::TypeProto_Tensor* TypeProto::_internal_mutable_tensor_type() {
  if (!_internal_has_tensor_type()) {
    clear_value();
    set_has_tensor_type();
    value_.tensor_type_ = CreateMaybeMessage< ::onnx::TypeProto_Tensor >(
        GetArenaNoVirtual());
  }
  return value_.tensor_type_;
}
inline ::onnx::TypeProto_Tensor* TypeProto::mutable_tensor_type() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.tensor_type)
  return _internal_mutable_tensor_type();
}

// optional .onnx.TypeProto.Sequence sequence_type = 4;
inline bool TypeProto::_internal_has_sequence_type() const {
  return value_case() == kSequenceType;
}
inline bool TypeProto::has_sequence_type() const {
  return _internal_has_sequence_type();
}
inline void TypeProto::set_has_sequence_type() {
  _oneof_case_[0] = kSequenceType;
}
inline void TypeProto::clear_sequence_type() {
  if (_internal_has_sequence_type()) {
    delete value_.sequence_type_;
    clear_has_value();
  }
}
inline ::onnx::TypeProto_Sequence* TypeProto::release_sequence_type() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.sequence_type)
  if (_internal_has_sequence_type()) {
    clear_has_value();
      ::onnx::TypeProto_Sequence* temp = value_.sequence_type_;
    value_.sequence_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::onnx::TypeProto_Sequence& TypeProto::_internal_sequence_type() const {
  return _internal_has_sequence_type()
      ? *value_.sequence_type_
      : *reinterpret_cast< ::onnx::TypeProto_Sequence*>(&::onnx::_TypeProto_Sequence_default_instance_);
}
inline const ::onnx::TypeProto_Sequence& TypeProto::sequence_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.sequence_type)
  return _internal_sequence_type();
}
inline ::onnx::TypeProto_Sequence* TypeProto::_internal_mutable_sequence_type() {
  if (!_internal_has_sequence_type()) {
    clear_value();
    set_has_sequence_type();
    value_.sequence_type_ = CreateMaybeMessage< ::onnx::TypeProto_Sequence >(
        GetArenaNoVirtual());
  }
  return value_.sequence_type_;
}
inline ::onnx::TypeProto_Sequence* TypeProto::mutable_sequence_type() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.sequence_type)
  return _internal_mutable_sequence_type();
}

// optional .onnx.TypeProto.Map map_type = 5;
inline bool TypeProto::_internal_has_map_type() const {
  return value_case() == kMapType;
}
inline bool TypeProto::has_map_type() const {
  return _internal_has_map_type();
}
inline void TypeProto::set_has_map_type() {
  _oneof_case_[0] = kMapType;
}
inline void TypeProto::clear_map_type() {
  if (_internal_has_map_type()) {
    delete value_.map_type_;
    clear_has_value();
  }
}
inline ::onnx::TypeProto_Map* TypeProto::release_map_type() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.map_type)
  if (_internal_has_map_type()) {
    clear_has_value();
      ::onnx::TypeProto_Map* temp = value_.map_type_;
    value_.map_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::onnx::TypeProto_Map& TypeProto::_internal_map_type() const {
  return _internal_has_map_type()
      ? *value_.map_type_
      : *reinterpret_cast< ::onnx::TypeProto_Map*>(&::onnx::_TypeProto_Map_default_instance_);
}
inline const ::onnx::TypeProto_Map& TypeProto::map_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.map_type)
  return _internal_map_type();
}
inline ::onnx::TypeProto_Map* TypeProto::_internal_mutable_map_type() {
  if (!_internal_has_map_type()) {
    clear_value();
    set_has_map_type();
    value_.map_type_ = CreateMaybeMessage< ::onnx::TypeProto_Map >(
        GetArenaNoVirtual());
  }
  return value_.map_type_;
}
inline ::onnx::TypeProto_Map* TypeProto::mutable_map_type() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.map_type)
  return _internal_mutable_map_type();
}

// optional .onnx.TypeProto.Optional optional_type = 9;
inline bool TypeProto::_internal_has_optional_type() const {
  return value_case() == kOptionalType;
}
inline bool TypeProto::has_optional_type() const {
  return _internal_has_optional_type();
}
inline void TypeProto::set_has_optional_type() {
  _oneof_case_[0] = kOptionalType;
}
inline void TypeProto::clear_optional_type() {
  if (_internal_has_optional_type()) {
    delete value_.optional_type_;
    clear_has_value();
  }
}
inline ::onnx::TypeProto_Optional* TypeProto::release_optional_type() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.optional_type)
  if (_internal_has_optional_type()) {
    clear_has_value();
      ::onnx::TypeProto_Optional* temp = value_.optional_type_;
    value_.optional_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::onnx::TypeProto_Optional& TypeProto::_internal_optional_type() const {
  return _internal_has_optional_type()
      ? *value_.optional_type_
      : *reinterpret_cast< ::onnx::TypeProto_Optional*>(&::onnx::_TypeProto_Optional_default_instance_);
}
inline const ::onnx::TypeProto_Optional& TypeProto::optional_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.optional_type)
  return _internal_optional_type();
}
inline ::onnx::TypeProto_Optional* TypeProto::_internal_mutable_optional_type() {
  if (!_internal_has_optional_type()) {
    clear_value();
    set_has_optional_type();
    value_.optional_type_ = CreateMaybeMessage< ::onnx::TypeProto_Optional >(
        GetArenaNoVirtual());
  }
  return value_.optional_type_;
}
inline ::onnx::TypeProto_Optional* TypeProto::mutable_optional_type() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.optional_type)
  return _internal_mutable_optional_type();
}

// optional .onnx.TypeProto.SparseTensor sparse_tensor_type = 8;
inline bool TypeProto::_internal_has_sparse_tensor_type() const {
  return value_case() == kSparseTensorType;
}
inline bool TypeProto::has_sparse_tensor_type() const {
  return _internal_has_sparse_tensor_type();
}
inline void TypeProto::set_has_sparse_tensor_type() {
  _oneof_case_[0] = kSparseTensorType;
}
inline void TypeProto::clear_sparse_tensor_type() {
  if (_internal_has_sparse_tensor_type()) {
    delete value_.sparse_tensor_type_;
    clear_has_value();
  }
}
inline ::onnx::TypeProto_SparseTensor* TypeProto::release_sparse_tensor_type() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.sparse_tensor_type)
  if (_internal_has_sparse_tensor_type()) {
    clear_has_value();
      ::onnx::TypeProto_SparseTensor* temp = value_.sparse_tensor_type_;
    value_.sparse_tensor_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::onnx::TypeProto_SparseTensor& TypeProto::_internal_sparse_tensor_type() const {
  return _internal_has_sparse_tensor_type()
      ? *value_.sparse_tensor_type_
      : *reinterpret_cast< ::onnx::TypeProto_SparseTensor*>(&::onnx::_TypeProto_SparseTensor_default_instance_);
}
inline const ::onnx::TypeProto_SparseTensor& TypeProto::sparse_tensor_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.sparse_tensor_type)
  return _internal_sparse_tensor_type();
}
inline ::onnx::TypeProto_SparseTensor* TypeProto::_internal_mutable_sparse_tensor_type() {
  if (!_internal_has_sparse_tensor_type()) {
    clear_value();
    set_has_sparse_tensor_type();
    value_.sparse_tensor_type_ = CreateMaybeMessage< ::onnx::TypeProto_SparseTensor >(
        GetArenaNoVirtual());
  }
  return value_.sparse_tensor_type_;
}
inline ::onnx::TypeProto_SparseTensor* TypeProto::mutable_sparse_tensor_type() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.sparse_tensor_type)
  return _internal_mutable_sparse_tensor_type();
}

// optional .onnx.TypeProto.Opaque opaque_type = 7;
inline bool TypeProto::_internal_has_opaque_type() const {
  return value_case() == kOpaqueType;
}
inline bool TypeProto::has_opaque_type() const {
  return _internal_has_opaque_type();
}
inline void TypeProto::set_has_opaque_type() {
  _oneof_case_[0] = kOpaqueType;
}
inline void TypeProto::clear_opaque_type() {
  if (_internal_has_opaque_type()) {
    delete value_.opaque_type_;
    clear_has_value();
  }
}
inline ::onnx::TypeProto_Opaque* TypeProto::release_opaque_type() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.opaque_type)
  if (_internal_has_opaque_type()) {
    clear_has_value();
      ::onnx::TypeProto_Opaque* temp = value_.opaque_type_;
    value_.opaque_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::onnx::TypeProto_Opaque& TypeProto::_internal_opaque_type() const {
  return _internal_has_opaque_type()
      ? *value_.opaque_type_
      : *reinterpret_cast< ::onnx::TypeProto_Opaque*>(&::onnx::_TypeProto_Opaque_default_instance_);
}
inline const ::onnx::TypeProto_Opaque& TypeProto::opaque_type() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.opaque_type)
  return _internal_opaque_type();
}
inline ::onnx::TypeProto_Opaque* TypeProto::_internal_mutable_opaque_type() {
  if (!_internal_has_opaque_type()) {
    clear_value();
    set_has_opaque_type();
    value_.opaque_type_ = CreateMaybeMessage< ::onnx::TypeProto_Opaque >(
        GetArenaNoVirtual());
  }
  return value_.opaque_type_;
}
inline ::onnx::TypeProto_Opaque* TypeProto::mutable_opaque_type() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.opaque_type)
  return _internal_mutable_opaque_type();
}

// optional string denotation = 6;
inline bool TypeProto::_internal_has_denotation() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TypeProto::has_denotation() const {
  return _internal_has_denotation();
}
inline void TypeProto::clear_denotation() {
  denotation_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TypeProto::denotation() const {
  // @@protoc_insertion_point(field_get:onnx.TypeProto.denotation)
  return _internal_denotation();
}
inline void TypeProto::set_denotation(const std::string& value) {
  _internal_set_denotation(value);
  // @@protoc_insertion_point(field_set:onnx.TypeProto.denotation)
}
inline std::string* TypeProto::mutable_denotation() {
  // @@protoc_insertion_point(field_mutable:onnx.TypeProto.denotation)
  return _internal_mutable_denotation();
}
inline const std::string& TypeProto::_internal_denotation() const {
  return denotation_.GetNoArena();
}
inline void TypeProto::_internal_set_denotation(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  denotation_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void TypeProto::set_denotation(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  denotation_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.TypeProto.denotation)
}
inline void TypeProto::set_denotation(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  denotation_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.TypeProto.denotation)
}
inline void TypeProto::set_denotation(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  denotation_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.TypeProto.denotation)
}
inline std::string* TypeProto::_internal_mutable_denotation() {
  _has_bits_[0] |= 0x00000001u;
  return denotation_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TypeProto::release_denotation() {
  // @@protoc_insertion_point(field_release:onnx.TypeProto.denotation)
  if (!_internal_has_denotation()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return denotation_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TypeProto::set_allocated_denotation(std::string* denotation) {
  if (denotation != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  denotation_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), denotation);
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.denotation)
}

inline bool TypeProto::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void TypeProto::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline TypeProto::ValueCase TypeProto::value_case() const {
  return TypeProto::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// OperatorSetIdProto

// optional string domain = 1;
inline bool OperatorSetIdProto::_internal_has_domain() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool OperatorSetIdProto::has_domain() const {
  return _internal_has_domain();
}
inline void OperatorSetIdProto::clear_domain() {
  domain_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& OperatorSetIdProto::domain() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetIdProto.domain)
  return _internal_domain();
}
inline void OperatorSetIdProto::set_domain(const std::string& value) {
  _internal_set_domain(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorSetIdProto.domain)
}
inline std::string* OperatorSetIdProto::mutable_domain() {
  // @@protoc_insertion_point(field_mutable:onnx.OperatorSetIdProto.domain)
  return _internal_mutable_domain();
}
inline const std::string& OperatorSetIdProto::_internal_domain() const {
  return domain_.GetNoArena();
}
inline void OperatorSetIdProto::_internal_set_domain(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void OperatorSetIdProto::set_domain(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  domain_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.OperatorSetIdProto.domain)
}
inline void OperatorSetIdProto::set_domain(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.OperatorSetIdProto.domain)
}
inline void OperatorSetIdProto::set_domain(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.OperatorSetIdProto.domain)
}
inline std::string* OperatorSetIdProto::_internal_mutable_domain() {
  _has_bits_[0] |= 0x00000001u;
  return domain_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OperatorSetIdProto::release_domain() {
  // @@protoc_insertion_point(field_release:onnx.OperatorSetIdProto.domain)
  if (!_internal_has_domain()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return domain_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OperatorSetIdProto::set_allocated_domain(std::string* domain) {
  if (domain != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  domain_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), domain);
  // @@protoc_insertion_point(field_set_allocated:onnx.OperatorSetIdProto.domain)
}

// optional int64 version = 2;
inline bool OperatorSetIdProto::_internal_has_version() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool OperatorSetIdProto::has_version() const {
  return _internal_has_version();
}
inline void OperatorSetIdProto::clear_version() {
  version_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OperatorSetIdProto::_internal_version() const {
  return version_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OperatorSetIdProto::version() const {
  // @@protoc_insertion_point(field_get:onnx.OperatorSetIdProto.version)
  return _internal_version();
}
inline void OperatorSetIdProto::_internal_set_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000002u;
  version_ = value;
}
inline void OperatorSetIdProto::set_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:onnx.OperatorSetIdProto.version)
}

// -------------------------------------------------------------------

// FunctionProto

// optional string name = 1;
inline bool FunctionProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool FunctionProto::has_name() const {
  return _internal_has_name();
}
inline void FunctionProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& FunctionProto::name() const {
  // @@protoc_insertion_point(field_get:onnx.FunctionProto.name)
  return _internal_name();
}
inline void FunctionProto::set_name(const std::string& value) {
  _internal_set_name(value);
  // @@protoc_insertion_point(field_set:onnx.FunctionProto.name)
}
inline std::string* FunctionProto::mutable_name() {
  // @@protoc_insertion_point(field_mutable:onnx.FunctionProto.name)
  return _internal_mutable_name();
}
inline const std::string& FunctionProto::_internal_name() const {
  return name_.GetNoArena();
}
inline void FunctionProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void FunctionProto::set_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.FunctionProto.name)
}
inline void FunctionProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.FunctionProto.name)
}
inline void FunctionProto::set_name(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000001u;
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.FunctionProto.name)
}
inline std::string* FunctionProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* FunctionProto::release_name() {
  // @@protoc_insertion_point(field_release:onnx.FunctionProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return name_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void FunctionProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:onnx.FunctionProto.name)
}

// repeated string input = 4;
inline int FunctionProto::_internal_input_size() const {
  return input_.size();
}
inline int FunctionProto::input_size() const {
  return _internal_input_size();
}
inline void FunctionProto::clear_input() {
  input_.Clear();
}
inline std::string* FunctionProto::add_input() {
  // @@protoc_insertion_point(field_add_mutable:onnx.FunctionProto.input)
  return _internal_add_input();
}
inline const std::string& FunctionProto::_internal_input(int index) const {
  return input_.Get(index);
}
inline const std::string& FunctionProto::input(int index) const {
  // @@protoc_insertion_point(field_get:onnx.FunctionProto.input)
  return _internal_input(index);
}
inline std::string* FunctionProto::mutable_input(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.FunctionProto.input)
  return input_.Mutable(index);
}
inline void FunctionProto::set_input(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:onnx.FunctionProto.input)
  input_.Mutable(index)->assign(value);
}
inline void FunctionProto::set_input(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:onnx.FunctionProto.input)
  input_.Mutable(index)->assign(std::move(value));
}
inline void FunctionProto::set_input(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:onnx.FunctionProto.input)
}
inline void FunctionProto::set_input(int index, const char* value, size_t size) {
  input_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:onnx.FunctionProto.input)
}
inline std::string* FunctionProto::_internal_add_input() {
  return input_.Add();
}
inline void FunctionProto::add_input(const std::string& value) {
  input_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:onnx.FunctionProto.input)
}
inline void FunctionProto::add_input(std::string&& value) {
  input_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:onnx.FunctionProto.input)
}
inline void FunctionProto::add_input(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:onnx.FunctionProto.input)
}
inline void FunctionProto::add_input(const char* value, size_t size) {
  input_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:onnx.FunctionProto.input)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
FunctionProto::input() const {
  // @@protoc_insertion_point(field_list:onnx.FunctionProto.input)
  return input_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
FunctionProto::mutable_input() {
  // @@protoc_insertion_point(field_mutable_list:onnx.FunctionProto.input)
  return &input_;
}

// repeated string output = 5;
inline int FunctionProto::_internal_output_size() const {
  return output_.size();
}
inline int FunctionProto::output_size() const {
  return _internal_output_size();
}
inline void FunctionProto::clear_output() {
  output_.Clear();
}
inline std::string* FunctionProto::add_output() {
  // @@protoc_insertion_point(field_add_mutable:onnx.FunctionProto.output)
  return _internal_add_output();
}
inline const std::string& FunctionProto::_internal_output(int index) const {
  return output_.Get(index);
}
inline const std::string& FunctionProto::output(int index) const {
  // @@protoc_insertion_point(field_get:onnx.FunctionProto.output)
  return _internal_output(index);
}
inline std::string* FunctionProto::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.FunctionProto.output)
  return output_.Mutable(index);
}
inline void FunctionProto::set_output(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:onnx.FunctionProto.output)
  output_.Mutable(index)->assign(value);
}
inline void FunctionProto::set_output(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:onnx.FunctionProto.output)
  output_.Mutable(index)->assign(std::move(value));
}
inline void FunctionProto::set_output(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:onnx.FunctionProto.output)
}
inline void FunctionProto::set_output(int index, const char* value, size_t size) {
  output_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:onnx.FunctionProto.output)
}
inline std::string* FunctionProto::_internal_add_output() {
  return output_.Add();
}
inline void FunctionProto::add_output(const std::string& value) {
  output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:onnx.FunctionProto.output)
}
inline void FunctionProto::add_output(std::string&& value) {
  output_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:onnx.FunctionProto.output)
}
inline void FunctionProto::add_output(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:onnx.FunctionProto.output)
}
inline void FunctionProto::add_output(const char* value, size_t size) {
  output_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:onnx.FunctionProto.output)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
FunctionProto::output() const {
  // @@protoc_insertion_point(field_list:onnx.FunctionProto.output)
  return output_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
FunctionProto::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:onnx.FunctionProto.output)
  return &output_;
}

// repeated string attribute = 6;
inline int FunctionProto::_internal_attribute_size() const {
  return attribute_.size();
}
inline int FunctionProto::attribute_size() const {
  return _internal_attribute_size();
}
inline void FunctionProto::clear_attribute() {
  attribute_.Clear();
}
inline std::string* FunctionProto::add_attribute() {
  // @@protoc_insertion_point(field_add_mutable:onnx.FunctionProto.attribute)
  return _internal_add_attribute();
}
inline const std::string& FunctionProto::_internal_attribute(int index) const {
  return attribute_.Get(index);
}
inline const std::string& FunctionProto::attribute(int index) const {
  // @@protoc_insertion_point(field_get:onnx.FunctionProto.attribute)
  return _internal_attribute(index);
}
inline std::string* FunctionProto::mutable_attribute(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.FunctionProto.attribute)
  return attribute_.Mutable(index);
}
inline void FunctionProto::set_attribute(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:onnx.FunctionProto.attribute)
  attribute_.Mutable(index)->assign(value);
}
inline void FunctionProto::set_attribute(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:onnx.FunctionProto.attribute)
  attribute_.Mutable(index)->assign(std::move(value));
}
inline void FunctionProto::set_attribute(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  attribute_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:onnx.FunctionProto.attribute)
}
inline void FunctionProto::set_attribute(int index, const char* value, size_t size) {
  attribute_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:onnx.FunctionProto.attribute)
}
inline std::string* FunctionProto::_internal_add_attribute() {
  return attribute_.Add();
}
inline void FunctionProto::add_attribute(const std::string& value) {
  attribute_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:onnx.FunctionProto.attribute)
}
inline void FunctionProto::add_attribute(std::string&& value) {
  attribute_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:onnx.FunctionProto.attribute)
}
inline void FunctionProto::add_attribute(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  attribute_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:onnx.FunctionProto.attribute)
}
inline void FunctionProto::add_attribute(const char* value, size_t size) {
  attribute_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:onnx.FunctionProto.attribute)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
FunctionProto::attribute() const {
  // @@protoc_insertion_point(field_list:onnx.FunctionProto.attribute)
  return attribute_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
FunctionProto::mutable_attribute() {
  // @@protoc_insertion_point(field_mutable_list:onnx.FunctionProto.attribute)
  return &attribute_;
}

// repeated .onnx.NodeProto node = 7;
inline int FunctionProto::_internal_node_size() const {
  return node_.size();
}
inline int FunctionProto::node_size() const {
  return _internal_node_size();
}
inline void FunctionProto::clear_node() {
  node_.Clear();
}
inline ::onnx::NodeProto* FunctionProto::mutable_node(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.FunctionProto.node)
  return node_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto >*
FunctionProto::mutable_node() {
  // @@protoc_insertion_point(field_mutable_list:onnx.FunctionProto.node)
  return &node_;
}
inline const ::onnx::NodeProto& FunctionProto::_internal_node(int index) const {
  return node_.Get(index);
}
inline const ::onnx::NodeProto& FunctionProto::node(int index) const {
  // @@protoc_insertion_point(field_get:onnx.FunctionProto.node)
  return _internal_node(index);
}
inline ::onnx::NodeProto* FunctionProto::_internal_add_node() {
  return node_.Add();
}
inline ::onnx::NodeProto* FunctionProto::add_node() {
  // @@protoc_insertion_point(field_add:onnx.FunctionProto.node)
  return _internal_add_node();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::NodeProto >&
FunctionProto::node() const {
  // @@protoc_insertion_point(field_list:onnx.FunctionProto.node)
  return node_;
}

// optional string doc_string = 8;
inline bool FunctionProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool FunctionProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void FunctionProto::clear_doc_string() {
  doc_string_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& FunctionProto::doc_string() const {
  // @@protoc_insertion_point(field_get:onnx.FunctionProto.doc_string)
  return _internal_doc_string();
}
inline void FunctionProto::set_doc_string(const std::string& value) {
  _internal_set_doc_string(value);
  // @@protoc_insertion_point(field_set:onnx.FunctionProto.doc_string)
}
inline std::string* FunctionProto::mutable_doc_string() {
  // @@protoc_insertion_point(field_mutable:onnx.FunctionProto.doc_string)
  return _internal_mutable_doc_string();
}
inline const std::string& FunctionProto::_internal_doc_string() const {
  return doc_string_.GetNoArena();
}
inline void FunctionProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void FunctionProto::set_doc_string(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.FunctionProto.doc_string)
}
inline void FunctionProto::set_doc_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.FunctionProto.doc_string)
}
inline void FunctionProto::set_doc_string(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.FunctionProto.doc_string)
}
inline std::string* FunctionProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000002u;
  return doc_string_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* FunctionProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:onnx.FunctionProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return doc_string_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void FunctionProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  doc_string_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string);
  // @@protoc_insertion_point(field_set_allocated:onnx.FunctionProto.doc_string)
}

// repeated .onnx.OperatorSetIdProto opset_import = 9;
inline int FunctionProto::_internal_opset_import_size() const {
  return opset_import_.size();
}
inline int FunctionProto::opset_import_size() const {
  return _internal_opset_import_size();
}
inline void FunctionProto::clear_opset_import() {
  opset_import_.Clear();
}
inline ::onnx::OperatorSetIdProto* FunctionProto::mutable_opset_import(int index) {
  // @@protoc_insertion_point(field_mutable:onnx.FunctionProto.opset_import)
  return opset_import_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto >*
FunctionProto::mutable_opset_import() {
  // @@protoc_insertion_point(field_mutable_list:onnx.FunctionProto.opset_import)
  return &opset_import_;
}
inline const ::onnx::OperatorSetIdProto& FunctionProto::_internal_opset_import(int index) const {
  return opset_import_.Get(index);
}
inline const ::onnx::OperatorSetIdProto& FunctionProto::opset_import(int index) const {
  // @@protoc_insertion_point(field_get:onnx.FunctionProto.opset_import)
  return _internal_opset_import(index);
}
inline ::onnx::OperatorSetIdProto* FunctionProto::_internal_add_opset_import() {
  return opset_import_.Add();
}
inline ::onnx::OperatorSetIdProto* FunctionProto::add_opset_import() {
  // @@protoc_insertion_point(field_add:onnx.FunctionProto.opset_import)
  return _internal_add_opset_import();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::onnx::OperatorSetIdProto >&
FunctionProto::opset_import() const {
  // @@protoc_insertion_point(field_list:onnx.FunctionProto.opset_import)
  return opset_import_;
}

// optional string domain = 10;
inline bool FunctionProto::_internal_has_domain() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool FunctionProto::has_domain() const {
  return _internal_has_domain();
}
inline void FunctionProto::clear_domain() {
  domain_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& FunctionProto::domain() const {
  // @@protoc_insertion_point(field_get:onnx.FunctionProto.domain)
  return _internal_domain();
}
inline void FunctionProto::set_domain(const std::string& value) {
  _internal_set_domain(value);
  // @@protoc_insertion_point(field_set:onnx.FunctionProto.domain)
}
inline std::string* FunctionProto::mutable_domain() {
  // @@protoc_insertion_point(field_mutable:onnx.FunctionProto.domain)
  return _internal_mutable_domain();
}
inline const std::string& FunctionProto::_internal_domain() const {
  return domain_.GetNoArena();
}
inline void FunctionProto::_internal_set_domain(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
}
inline void FunctionProto::set_domain(std::string&& value) {
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:onnx.FunctionProto.domain)
}
inline void FunctionProto::set_domain(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:onnx.FunctionProto.domain)
}
inline void FunctionProto::set_domain(const char* value, size_t size) {
  _has_bits_[0] |= 0x00000004u;
  domain_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:onnx.FunctionProto.domain)
}
inline std::string* FunctionProto::_internal_mutable_domain() {
  _has_bits_[0] |= 0x00000004u;
  return domain_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* FunctionProto::release_domain() {
  // @@protoc_insertion_point(field_release:onnx.FunctionProto.domain)
  if (!_internal_has_domain()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  return domain_.ReleaseNonDefaultNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void FunctionProto::set_allocated_domain(std::string* domain) {
  if (domain != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  domain_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), domain);
  // @@protoc_insertion_point(field_set_allocated:onnx.FunctionProto.domain)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace onnx

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::onnx::AttributeProto_AttributeType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::onnx::AttributeProto_AttributeType>() {
  return ::onnx::AttributeProto_AttributeType_descriptor();
}
template <> struct is_proto_enum< ::onnx::TensorProto_DataType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::onnx::TensorProto_DataType>() {
  return ::onnx::TensorProto_DataType_descriptor();
}
template <> struct is_proto_enum< ::onnx::TensorProto_DataLocation> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::onnx::TensorProto_DataLocation>() {
  return ::onnx::TensorProto_DataLocation_descriptor();
}
template <> struct is_proto_enum< ::onnx::Version> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::onnx::Version>() {
  return ::onnx::Version_descriptor();
}
template <> struct is_proto_enum< ::onnx::OperatorStatus> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::onnx::OperatorStatus>() {
  return ::onnx::OperatorStatus_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_onnx_2dml_2eproto

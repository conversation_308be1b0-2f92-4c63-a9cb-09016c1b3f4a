// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: onnx-ml.proto

#include "onnx-ml.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<5> scc_info_AttributeProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_FunctionProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_OperatorSetIdProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_SparseTensorProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_StringStringEntryProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TensorAnnotation_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_TensorProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TensorProto_Segment_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TensorShapeProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TensorShapeProto_Dimension_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_TrainingInfoProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_TypeProto_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TypeProto_Opaque_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TypeProto_SparseTensor_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TypeProto_Tensor_onnx_2dml_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_onnx_2dml_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ValueInfoProto_onnx_2dml_2eproto;
namespace onnx {
class AttributeProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<AttributeProto> _instance;
} _AttributeProto_default_instance_;
class ValueInfoProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ValueInfoProto> _instance;
} _ValueInfoProto_default_instance_;
class NodeProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<NodeProto> _instance;
} _NodeProto_default_instance_;
class TrainingInfoProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TrainingInfoProto> _instance;
} _TrainingInfoProto_default_instance_;
class ModelProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ModelProto> _instance;
} _ModelProto_default_instance_;
class StringStringEntryProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<StringStringEntryProto> _instance;
} _StringStringEntryProto_default_instance_;
class TensorAnnotationDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TensorAnnotation> _instance;
} _TensorAnnotation_default_instance_;
class GraphProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<GraphProto> _instance;
} _GraphProto_default_instance_;
class TensorProto_SegmentDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TensorProto_Segment> _instance;
} _TensorProto_Segment_default_instance_;
class TensorProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TensorProto> _instance;
} _TensorProto_default_instance_;
class SparseTensorProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<SparseTensorProto> _instance;
} _SparseTensorProto_default_instance_;
class TensorShapeProto_DimensionDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TensorShapeProto_Dimension> _instance;
  ::PROTOBUF_NAMESPACE_ID::int64 dim_value_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dim_param_;
} _TensorShapeProto_Dimension_default_instance_;
class TensorShapeProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TensorShapeProto> _instance;
} _TensorShapeProto_default_instance_;
class TypeProto_TensorDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TypeProto_Tensor> _instance;
} _TypeProto_Tensor_default_instance_;
class TypeProto_SequenceDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TypeProto_Sequence> _instance;
} _TypeProto_Sequence_default_instance_;
class TypeProto_MapDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TypeProto_Map> _instance;
} _TypeProto_Map_default_instance_;
class TypeProto_OptionalDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TypeProto_Optional> _instance;
} _TypeProto_Optional_default_instance_;
class TypeProto_SparseTensorDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TypeProto_SparseTensor> _instance;
} _TypeProto_SparseTensor_default_instance_;
class TypeProto_OpaqueDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TypeProto_Opaque> _instance;
} _TypeProto_Opaque_default_instance_;
class TypeProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TypeProto> _instance;
  const ::onnx::TypeProto_Tensor* tensor_type_;
  const ::onnx::TypeProto_Sequence* sequence_type_;
  const ::onnx::TypeProto_Map* map_type_;
  const ::onnx::TypeProto_Optional* optional_type_;
  const ::onnx::TypeProto_SparseTensor* sparse_tensor_type_;
  const ::onnx::TypeProto_Opaque* opaque_type_;
} _TypeProto_default_instance_;
class OperatorSetIdProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<OperatorSetIdProto> _instance;
} _OperatorSetIdProto_default_instance_;
class FunctionProtoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<FunctionProto> _instance;
} _FunctionProto_default_instance_;
}  // namespace onnx
static void InitDefaultsscc_info_AttributeProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_AttributeProto_default_instance_;
    new (ptr) ::onnx::AttributeProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::onnx::_NodeProto_default_instance_;
    new (ptr) ::onnx::NodeProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::onnx::_GraphProto_default_instance_;
    new (ptr) ::onnx::GraphProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::AttributeProto::InitAsDefaultInstance();
  ::onnx::NodeProto::InitAsDefaultInstance();
  ::onnx::GraphProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<5> scc_info_AttributeProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 5, 0, InitDefaultsscc_info_AttributeProto_onnx_2dml_2eproto}, {
      &scc_info_TensorProto_onnx_2dml_2eproto.base,
      &scc_info_SparseTensorProto_onnx_2dml_2eproto.base,
      &scc_info_TypeProto_onnx_2dml_2eproto.base,
      &scc_info_ValueInfoProto_onnx_2dml_2eproto.base,
      &scc_info_TensorAnnotation_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_FunctionProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_FunctionProto_default_instance_;
    new (ptr) ::onnx::FunctionProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::FunctionProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_FunctionProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_FunctionProto_onnx_2dml_2eproto}, {
      &scc_info_AttributeProto_onnx_2dml_2eproto.base,
      &scc_info_OperatorSetIdProto_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_ModelProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_ModelProto_default_instance_;
    new (ptr) ::onnx::ModelProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::ModelProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<5> scc_info_ModelProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 5, 0, InitDefaultsscc_info_ModelProto_onnx_2dml_2eproto}, {
      &scc_info_OperatorSetIdProto_onnx_2dml_2eproto.base,
      &scc_info_AttributeProto_onnx_2dml_2eproto.base,
      &scc_info_StringStringEntryProto_onnx_2dml_2eproto.base,
      &scc_info_TrainingInfoProto_onnx_2dml_2eproto.base,
      &scc_info_FunctionProto_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_OperatorSetIdProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_OperatorSetIdProto_default_instance_;
    new (ptr) ::onnx::OperatorSetIdProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::OperatorSetIdProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_OperatorSetIdProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_OperatorSetIdProto_onnx_2dml_2eproto}, {}};

static void InitDefaultsscc_info_SparseTensorProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_SparseTensorProto_default_instance_;
    new (ptr) ::onnx::SparseTensorProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::SparseTensorProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_SparseTensorProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_SparseTensorProto_onnx_2dml_2eproto}, {
      &scc_info_TensorProto_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_StringStringEntryProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_StringStringEntryProto_default_instance_;
    new (ptr) ::onnx::StringStringEntryProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::StringStringEntryProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_StringStringEntryProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_StringStringEntryProto_onnx_2dml_2eproto}, {}};

static void InitDefaultsscc_info_TensorAnnotation_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TensorAnnotation_default_instance_;
    new (ptr) ::onnx::TensorAnnotation();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TensorAnnotation::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TensorAnnotation_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_TensorAnnotation_onnx_2dml_2eproto}, {
      &scc_info_StringStringEntryProto_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_TensorProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TensorProto_default_instance_;
    new (ptr) ::onnx::TensorProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TensorProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_TensorProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_TensorProto_onnx_2dml_2eproto}, {
      &scc_info_TensorProto_Segment_onnx_2dml_2eproto.base,
      &scc_info_StringStringEntryProto_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_TensorProto_Segment_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TensorProto_Segment_default_instance_;
    new (ptr) ::onnx::TensorProto_Segment();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TensorProto_Segment::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TensorProto_Segment_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_TensorProto_Segment_onnx_2dml_2eproto}, {}};

static void InitDefaultsscc_info_TensorShapeProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TensorShapeProto_default_instance_;
    new (ptr) ::onnx::TensorShapeProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TensorShapeProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TensorShapeProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_TensorShapeProto_onnx_2dml_2eproto}, {
      &scc_info_TensorShapeProto_Dimension_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_TensorShapeProto_Dimension_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TensorShapeProto_Dimension_default_instance_;
    new (ptr) ::onnx::TensorShapeProto_Dimension();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TensorShapeProto_Dimension::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TensorShapeProto_Dimension_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_TensorShapeProto_Dimension_onnx_2dml_2eproto}, {}};

static void InitDefaultsscc_info_TrainingInfoProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TrainingInfoProto_default_instance_;
    new (ptr) ::onnx::TrainingInfoProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TrainingInfoProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_TrainingInfoProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_TrainingInfoProto_onnx_2dml_2eproto}, {
      &scc_info_AttributeProto_onnx_2dml_2eproto.base,
      &scc_info_StringStringEntryProto_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_TypeProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TypeProto_Sequence_default_instance_;
    new (ptr) ::onnx::TypeProto_Sequence();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::onnx::_TypeProto_Map_default_instance_;
    new (ptr) ::onnx::TypeProto_Map();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::onnx::_TypeProto_Optional_default_instance_;
    new (ptr) ::onnx::TypeProto_Optional();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::onnx::_TypeProto_default_instance_;
    new (ptr) ::onnx::TypeProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TypeProto_Sequence::InitAsDefaultInstance();
  ::onnx::TypeProto_Map::InitAsDefaultInstance();
  ::onnx::TypeProto_Optional::InitAsDefaultInstance();
  ::onnx::TypeProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_TypeProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 3, 0, InitDefaultsscc_info_TypeProto_onnx_2dml_2eproto}, {
      &scc_info_TypeProto_Tensor_onnx_2dml_2eproto.base,
      &scc_info_TypeProto_SparseTensor_onnx_2dml_2eproto.base,
      &scc_info_TypeProto_Opaque_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_TypeProto_Opaque_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TypeProto_Opaque_default_instance_;
    new (ptr) ::onnx::TypeProto_Opaque();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TypeProto_Opaque::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TypeProto_Opaque_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_TypeProto_Opaque_onnx_2dml_2eproto}, {}};

static void InitDefaultsscc_info_TypeProto_SparseTensor_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TypeProto_SparseTensor_default_instance_;
    new (ptr) ::onnx::TypeProto_SparseTensor();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TypeProto_SparseTensor::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TypeProto_SparseTensor_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_TypeProto_SparseTensor_onnx_2dml_2eproto}, {
      &scc_info_TensorShapeProto_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_TypeProto_Tensor_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_TypeProto_Tensor_default_instance_;
    new (ptr) ::onnx::TypeProto_Tensor();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::TypeProto_Tensor::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TypeProto_Tensor_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_TypeProto_Tensor_onnx_2dml_2eproto}, {
      &scc_info_TensorShapeProto_onnx_2dml_2eproto.base,}};

static void InitDefaultsscc_info_ValueInfoProto_onnx_2dml_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::onnx::_ValueInfoProto_default_instance_;
    new (ptr) ::onnx::ValueInfoProto();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::onnx::ValueInfoProto::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ValueInfoProto_onnx_2dml_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ValueInfoProto_onnx_2dml_2eproto}, {
      &scc_info_TypeProto_onnx_2dml_2eproto.base,}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_onnx_2dml_2eproto[22];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_onnx_2dml_2eproto[5];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_onnx_2dml_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_onnx_2dml_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, name_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, ref_attr_name_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, doc_string_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, type_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, f_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, i_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, s_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, t_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, g_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, sparse_tensor_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, tp_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, floats_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, ints_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, strings_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, tensors_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, graphs_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, sparse_tensors_),
  PROTOBUF_FIELD_OFFSET(::onnx::AttributeProto, type_protos_),
  0,
  3,
  2,
  10,
  9,
  8,
  1,
  4,
  5,
  7,
  6,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::onnx::ValueInfoProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::ValueInfoProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::ValueInfoProto, name_),
  PROTOBUF_FIELD_OFFSET(::onnx::ValueInfoProto, type_),
  PROTOBUF_FIELD_OFFSET(::onnx::ValueInfoProto, doc_string_),
  0,
  2,
  1,
  PROTOBUF_FIELD_OFFSET(::onnx::NodeProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::NodeProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::NodeProto, input_),
  PROTOBUF_FIELD_OFFSET(::onnx::NodeProto, output_),
  PROTOBUF_FIELD_OFFSET(::onnx::NodeProto, name_),
  PROTOBUF_FIELD_OFFSET(::onnx::NodeProto, op_type_),
  PROTOBUF_FIELD_OFFSET(::onnx::NodeProto, domain_),
  PROTOBUF_FIELD_OFFSET(::onnx::NodeProto, attribute_),
  PROTOBUF_FIELD_OFFSET(::onnx::NodeProto, doc_string_),
  ~0u,
  ~0u,
  0,
  1,
  3,
  ~0u,
  2,
  PROTOBUF_FIELD_OFFSET(::onnx::TrainingInfoProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TrainingInfoProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TrainingInfoProto, initialization_),
  PROTOBUF_FIELD_OFFSET(::onnx::TrainingInfoProto, algorithm_),
  PROTOBUF_FIELD_OFFSET(::onnx::TrainingInfoProto, initialization_binding_),
  PROTOBUF_FIELD_OFFSET(::onnx::TrainingInfoProto, update_binding_),
  0,
  1,
  ~0u,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, ir_version_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, opset_import_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, producer_name_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, producer_version_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, domain_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, model_version_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, doc_string_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, graph_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, metadata_props_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, training_info_),
  PROTOBUF_FIELD_OFFSET(::onnx::ModelProto, functions_),
  5,
  ~0u,
  0,
  1,
  2,
  6,
  3,
  4,
  ~0u,
  ~0u,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::onnx::StringStringEntryProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::StringStringEntryProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::StringStringEntryProto, key_),
  PROTOBUF_FIELD_OFFSET(::onnx::StringStringEntryProto, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::onnx::TensorAnnotation, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorAnnotation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TensorAnnotation, tensor_name_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorAnnotation, quant_parameter_tensor_names_),
  0,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, node_),
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, name_),
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, initializer_),
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, sparse_initializer_),
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, doc_string_),
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, input_),
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, output_),
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, value_info_),
  PROTOBUF_FIELD_OFFSET(::onnx::GraphProto, quantization_annotation_),
  ~0u,
  0,
  ~0u,
  ~0u,
  1,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto_Segment, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto_Segment, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto_Segment, begin_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto_Segment, end_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, dims_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, data_type_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, segment_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, float_data_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, int32_data_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, string_data_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, int64_data_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, name_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, doc_string_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, raw_data_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, external_data_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, data_location_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, double_data_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorProto, uint64_data_),
  ~0u,
  4,
  3,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  0,
  2,
  1,
  ~0u,
  5,
  ~0u,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::onnx::SparseTensorProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::SparseTensorProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::SparseTensorProto, values_),
  PROTOBUF_FIELD_OFFSET(::onnx::SparseTensorProto, indices_),
  PROTOBUF_FIELD_OFFSET(::onnx::SparseTensorProto, dims_),
  0,
  1,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::onnx::TensorShapeProto_Dimension, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorShapeProto_Dimension, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::onnx::TensorShapeProto_Dimension, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::onnx::TensorShapeProto_DimensionDefaultTypeInternal, dim_value_),
  offsetof(::onnx::TensorShapeProto_DimensionDefaultTypeInternal, dim_param_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorShapeProto_Dimension, denotation_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorShapeProto_Dimension, value_),
  ~0u,
  ~0u,
  0,
  PROTOBUF_FIELD_OFFSET(::onnx::TensorShapeProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TensorShapeProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TensorShapeProto, dim_),
  ~0u,
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Tensor, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Tensor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Tensor, elem_type_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Tensor, shape_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Sequence, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Sequence, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Sequence, elem_type_),
  0,
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Map, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Map, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Map, key_type_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Map, value_type_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Optional, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Optional, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Optional, elem_type_),
  0,
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_SparseTensor, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_SparseTensor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_SparseTensor, elem_type_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_SparseTensor, shape_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Opaque, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Opaque, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Opaque, domain_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto_Opaque, name_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::onnx::TypeProtoDefaultTypeInternal, tensor_type_),
  offsetof(::onnx::TypeProtoDefaultTypeInternal, sequence_type_),
  offsetof(::onnx::TypeProtoDefaultTypeInternal, map_type_),
  offsetof(::onnx::TypeProtoDefaultTypeInternal, optional_type_),
  offsetof(::onnx::TypeProtoDefaultTypeInternal, sparse_tensor_type_),
  offsetof(::onnx::TypeProtoDefaultTypeInternal, opaque_type_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto, denotation_),
  PROTOBUF_FIELD_OFFSET(::onnx::TypeProto, value_),
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  0,
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetIdProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetIdProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetIdProto, domain_),
  PROTOBUF_FIELD_OFFSET(::onnx::OperatorSetIdProto, version_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, name_),
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, input_),
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, output_),
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, attribute_),
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, node_),
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, doc_string_),
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, opset_import_),
  PROTOBUF_FIELD_OFFSET(::onnx::FunctionProto, domain_),
  0,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  1,
  ~0u,
  2,
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 23, sizeof(::onnx::AttributeProto)},
  { 41, 49, sizeof(::onnx::ValueInfoProto)},
  { 52, 64, sizeof(::onnx::NodeProto)},
  { 71, 80, sizeof(::onnx::TrainingInfoProto)},
  { 84, 100, sizeof(::onnx::ModelProto)},
  { 111, 118, sizeof(::onnx::StringStringEntryProto)},
  { 120, 127, sizeof(::onnx::TensorAnnotation)},
  { 129, 143, sizeof(::onnx::GraphProto)},
  { 152, 159, sizeof(::onnx::TensorProto_Segment)},
  { 161, 180, sizeof(::onnx::TensorProto)},
  { 194, 202, sizeof(::onnx::SparseTensorProto)},
  { 205, 214, sizeof(::onnx::TensorShapeProto_Dimension)},
  { 217, 223, sizeof(::onnx::TensorShapeProto)},
  { 224, 231, sizeof(::onnx::TypeProto_Tensor)},
  { 233, 239, sizeof(::onnx::TypeProto_Sequence)},
  { 240, 247, sizeof(::onnx::TypeProto_Map)},
  { 249, 255, sizeof(::onnx::TypeProto_Optional)},
  { 256, 263, sizeof(::onnx::TypeProto_SparseTensor)},
  { 265, 272, sizeof(::onnx::TypeProto_Opaque)},
  { 274, 287, sizeof(::onnx::TypeProto)},
  { 294, 301, sizeof(::onnx::OperatorSetIdProto)},
  { 303, 316, sizeof(::onnx::FunctionProto)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_AttributeProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_ValueInfoProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_NodeProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TrainingInfoProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_ModelProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_StringStringEntryProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TensorAnnotation_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_GraphProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TensorProto_Segment_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TensorProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_SparseTensorProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TensorShapeProto_Dimension_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TensorShapeProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TypeProto_Tensor_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TypeProto_Sequence_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TypeProto_Map_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TypeProto_Optional_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TypeProto_SparseTensor_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TypeProto_Opaque_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_TypeProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_OperatorSetIdProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::onnx::_FunctionProto_default_instance_),
};

const char descriptor_table_protodef_onnx_2dml_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\ronnx-ml.proto\022\004onnx\"\314\005\n\016AttributeProto"
  "\022\014\n\004name\030\001 \001(\t\022\025\n\rref_attr_name\030\025 \001(\t\022\022\n"
  "\ndoc_string\030\r \001(\t\0220\n\004type\030\024 \001(\0162\".onnx.A"
  "ttributeProto.AttributeType\022\t\n\001f\030\002 \001(\002\022\t"
  "\n\001i\030\003 \001(\003\022\t\n\001s\030\004 \001(\014\022\034\n\001t\030\005 \001(\0132\021.onnx.T"
  "ensorProto\022\033\n\001g\030\006 \001(\0132\020.onnx.GraphProto\022"
  ".\n\rsparse_tensor\030\026 \001(\0132\027.onnx.SparseTens"
  "orProto\022\033\n\002tp\030\016 \001(\0132\017.onnx.TypeProto\022\016\n\006"
  "floats\030\007 \003(\002\022\014\n\004ints\030\010 \003(\003\022\017\n\007strings\030\t "
  "\003(\014\022\"\n\007tensors\030\n \003(\0132\021.onnx.TensorProto\022"
  " \n\006graphs\030\013 \003(\0132\020.onnx.GraphProto\022/\n\016spa"
  "rse_tensors\030\027 \003(\0132\027.onnx.SparseTensorPro"
  "to\022$\n\013type_protos\030\017 \003(\0132\017.onnx.TypeProto"
  "\"\331\001\n\rAttributeType\022\r\n\tUNDEFINED\020\000\022\t\n\005FLO"
  "AT\020\001\022\007\n\003INT\020\002\022\n\n\006STRING\020\003\022\n\n\006TENSOR\020\004\022\t\n"
  "\005GRAPH\020\005\022\021\n\rSPARSE_TENSOR\020\013\022\016\n\nTYPE_PROT"
  "O\020\r\022\n\n\006FLOATS\020\006\022\010\n\004INTS\020\007\022\013\n\007STRINGS\020\010\022\013"
  "\n\007TENSORS\020\t\022\n\n\006GRAPHS\020\n\022\022\n\016SPARSE_TENSOR"
  "S\020\014\022\017\n\013TYPE_PROTOS\020\016\"Q\n\016ValueInfoProto\022\014"
  "\n\004name\030\001 \001(\t\022\035\n\004type\030\002 \001(\0132\017.onnx.TypePr"
  "oto\022\022\n\ndoc_string\030\003 \001(\t\"\226\001\n\tNodeProto\022\r\n"
  "\005input\030\001 \003(\t\022\016\n\006output\030\002 \003(\t\022\014\n\004name\030\003 \001"
  "(\t\022\017\n\007op_type\030\004 \001(\t\022\016\n\006domain\030\007 \001(\t\022\'\n\ta"
  "ttribute\030\005 \003(\0132\024.onnx.AttributeProto\022\022\n\n"
  "doc_string\030\006 \001(\t\"\326\001\n\021TrainingInfoProto\022("
  "\n\016initialization\030\001 \001(\0132\020.onnx.GraphProto"
  "\022#\n\talgorithm\030\002 \001(\0132\020.onnx.GraphProto\022<\n"
  "\026initialization_binding\030\003 \003(\0132\034.onnx.Str"
  "ingStringEntryProto\0224\n\016update_binding\030\004 "
  "\003(\0132\034.onnx.StringStringEntryProto\"\353\002\n\nMo"
  "delProto\022\022\n\nir_version\030\001 \001(\003\022.\n\014opset_im"
  "port\030\010 \003(\0132\030.onnx.OperatorSetIdProto\022\025\n\r"
  "producer_name\030\002 \001(\t\022\030\n\020producer_version\030"
  "\003 \001(\t\022\016\n\006domain\030\004 \001(\t\022\025\n\rmodel_version\030\005"
  " \001(\003\022\022\n\ndoc_string\030\006 \001(\t\022\037\n\005graph\030\007 \001(\0132"
  "\020.onnx.GraphProto\0224\n\016metadata_props\030\016 \003("
  "\0132\034.onnx.StringStringEntryProto\022.\n\rtrain"
  "ing_info\030\024 \003(\0132\027.onnx.TrainingInfoProto\022"
  "&\n\tfunctions\030\031 \003(\0132\023.onnx.FunctionProto\""
  "4\n\026StringStringEntryProto\022\013\n\003key\030\001 \001(\t\022\r"
  "\n\005value\030\002 \001(\t\"k\n\020TensorAnnotation\022\023\n\013ten"
  "sor_name\030\001 \001(\t\022B\n\034quant_parameter_tensor"
  "_names\030\002 \003(\0132\034.onnx.StringStringEntryPro"
  "to\"\330\002\n\nGraphProto\022\035\n\004node\030\001 \003(\0132\017.onnx.N"
  "odeProto\022\014\n\004name\030\002 \001(\t\022&\n\013initializer\030\005 "
  "\003(\0132\021.onnx.TensorProto\0223\n\022sparse_initial"
  "izer\030\017 \003(\0132\027.onnx.SparseTensorProto\022\022\n\nd"
  "oc_string\030\n \001(\t\022#\n\005input\030\013 \003(\0132\024.onnx.Va"
  "lueInfoProto\022$\n\006output\030\014 \003(\0132\024.onnx.Valu"
  "eInfoProto\022(\n\nvalue_info\030\r \003(\0132\024.onnx.Va"
  "lueInfoProto\0227\n\027quantization_annotation\030"
  "\016 \003(\0132\026.onnx.TensorAnnotation\"\270\005\n\013Tensor"
  "Proto\022\014\n\004dims\030\001 \003(\003\022\021\n\tdata_type\030\002 \001(\005\022*"
  "\n\007segment\030\003 \001(\0132\031.onnx.TensorProto.Segme"
  "nt\022\026\n\nfloat_data\030\004 \003(\002B\002\020\001\022\026\n\nint32_data"
  "\030\005 \003(\005B\002\020\001\022\023\n\013string_data\030\006 \003(\014\022\026\n\nint64"
  "_data\030\007 \003(\003B\002\020\001\022\014\n\004name\030\010 \001(\t\022\022\n\ndoc_str"
  "ing\030\014 \001(\t\022\020\n\010raw_data\030\t \001(\014\0223\n\rexternal_"
  "data\030\r \003(\0132\034.onnx.StringStringEntryProto"
  "\0225\n\rdata_location\030\016 \001(\0162\036.onnx.TensorPro"
  "to.DataLocation\022\027\n\013double_data\030\n \003(\001B\002\020\001"
  "\022\027\n\013uint64_data\030\013 \003(\004B\002\020\001\032%\n\007Segment\022\r\n\005"
  "begin\030\001 \001(\003\022\013\n\003end\030\002 \001(\003\"\332\001\n\010DataType\022\r\n"
  "\tUNDEFINED\020\000\022\t\n\005FLOAT\020\001\022\t\n\005UINT8\020\002\022\010\n\004IN"
  "T8\020\003\022\n\n\006UINT16\020\004\022\t\n\005INT16\020\005\022\t\n\005INT32\020\006\022\t"
  "\n\005INT64\020\007\022\n\n\006STRING\020\010\022\010\n\004BOOL\020\t\022\013\n\007FLOAT"
  "16\020\n\022\n\n\006DOUBLE\020\013\022\n\n\006UINT32\020\014\022\n\n\006UINT64\020\r"
  "\022\r\n\tCOMPLEX64\020\016\022\016\n\nCOMPLEX128\020\017\022\014\n\010BFLOA"
  "T16\020\020\")\n\014DataLocation\022\013\n\007DEFAULT\020\000\022\014\n\010EX"
  "TERNAL\020\001\"h\n\021SparseTensorProto\022!\n\006values\030"
  "\001 \001(\0132\021.onnx.TensorProto\022\"\n\007indices\030\002 \001("
  "\0132\021.onnx.TensorProto\022\014\n\004dims\030\003 \003(\003\"\225\001\n\020T"
  "ensorShapeProto\022-\n\003dim\030\001 \003(\0132 .onnx.Tens"
  "orShapeProto.Dimension\032R\n\tDimension\022\023\n\td"
  "im_value\030\001 \001(\003H\000\022\023\n\tdim_param\030\002 \001(\tH\000\022\022\n"
  "\ndenotation\030\003 \001(\tB\007\n\005value\"\245\005\n\tTypeProto"
  "\022-\n\013tensor_type\030\001 \001(\0132\026.onnx.TypeProto.T"
  "ensorH\000\0221\n\rsequence_type\030\004 \001(\0132\030.onnx.Ty"
  "peProto.SequenceH\000\022\'\n\010map_type\030\005 \001(\0132\023.o"
  "nnx.TypeProto.MapH\000\0221\n\roptional_type\030\t \001"
  "(\0132\030.onnx.TypeProto.OptionalH\000\022:\n\022sparse"
  "_tensor_type\030\010 \001(\0132\034.onnx.TypeProto.Spar"
  "seTensorH\000\022-\n\013opaque_type\030\007 \001(\0132\026.onnx.T"
  "ypeProto.OpaqueH\000\022\022\n\ndenotation\030\006 \001(\t\032B\n"
  "\006Tensor\022\021\n\telem_type\030\001 \001(\005\022%\n\005shape\030\002 \001("
  "\0132\026.onnx.TensorShapeProto\032.\n\010Sequence\022\"\n"
  "\telem_type\030\001 \001(\0132\017.onnx.TypeProto\032<\n\003Map"
  "\022\020\n\010key_type\030\001 \001(\005\022#\n\nvalue_type\030\002 \001(\0132\017"
  ".onnx.TypeProto\032.\n\010Optional\022\"\n\telem_type"
  "\030\001 \001(\0132\017.onnx.TypeProto\032H\n\014SparseTensor\022"
  "\021\n\telem_type\030\001 \001(\005\022%\n\005shape\030\002 \001(\0132\026.onnx"
  ".TensorShapeProto\032&\n\006Opaque\022\016\n\006domain\030\001 "
  "\001(\t\022\014\n\004name\030\002 \001(\tB\007\n\005value\"5\n\022OperatorSe"
  "tIdProto\022\016\n\006domain\030\001 \001(\t\022\017\n\007version\030\002 \001("
  "\003\"\345\001\n\rFunctionProto\022\014\n\004name\030\001 \001(\t\022\r\n\005inp"
  "ut\030\004 \003(\t\022\016\n\006output\030\005 \003(\t\022\021\n\tattribute\030\006 "
  "\003(\t\022\035\n\004node\030\007 \003(\0132\017.onnx.NodeProto\022\022\n\ndo"
  "c_string\030\010 \001(\t\022.\n\014opset_import\030\t \003(\0132\030.o"
  "nnx.OperatorSetIdProto\022\016\n\006domain\030\n \001(\tJ\004"
  "\010\002\020\003J\004\010\003\020\004R\rsince_versionR\006status*\344\001\n\007Ve"
  "rsion\022\022\n\016_START_VERSION\020\000\022\031\n\025IR_VERSION_"
  "2017_10_10\020\001\022\031\n\025IR_VERSION_2017_10_30\020\002\022"
  "\030\n\024IR_VERSION_2017_11_3\020\003\022\030\n\024IR_VERSION_"
  "2019_1_22\020\004\022\030\n\024IR_VERSION_2019_3_18\020\005\022\030\n"
  "\024IR_VERSION_2019_9_19\020\006\022\027\n\023IR_VERSION_20"
  "20_5_8\020\007\022\016\n\nIR_VERSION\020\010*.\n\016OperatorStat"
  "us\022\020\n\014EXPERIMENTAL\020\000\022\n\n\006STABLE\020\001"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_onnx_2dml_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_onnx_2dml_2eproto_sccs[17] = {
  &scc_info_AttributeProto_onnx_2dml_2eproto.base,
  &scc_info_FunctionProto_onnx_2dml_2eproto.base,
  &scc_info_ModelProto_onnx_2dml_2eproto.base,
  &scc_info_OperatorSetIdProto_onnx_2dml_2eproto.base,
  &scc_info_SparseTensorProto_onnx_2dml_2eproto.base,
  &scc_info_StringStringEntryProto_onnx_2dml_2eproto.base,
  &scc_info_TensorAnnotation_onnx_2dml_2eproto.base,
  &scc_info_TensorProto_onnx_2dml_2eproto.base,
  &scc_info_TensorProto_Segment_onnx_2dml_2eproto.base,
  &scc_info_TensorShapeProto_onnx_2dml_2eproto.base,
  &scc_info_TensorShapeProto_Dimension_onnx_2dml_2eproto.base,
  &scc_info_TrainingInfoProto_onnx_2dml_2eproto.base,
  &scc_info_TypeProto_onnx_2dml_2eproto.base,
  &scc_info_TypeProto_Opaque_onnx_2dml_2eproto.base,
  &scc_info_TypeProto_SparseTensor_onnx_2dml_2eproto.base,
  &scc_info_TypeProto_Tensor_onnx_2dml_2eproto.base,
  &scc_info_ValueInfoProto_onnx_2dml_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_onnx_2dml_2eproto_once;
static bool descriptor_table_onnx_2dml_2eproto_initialized = false;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_onnx_2dml_2eproto = {
  &descriptor_table_onnx_2dml_2eproto_initialized, descriptor_table_protodef_onnx_2dml_2eproto, "onnx-ml.proto", 4272,
  &descriptor_table_onnx_2dml_2eproto_once, descriptor_table_onnx_2dml_2eproto_sccs, descriptor_table_onnx_2dml_2eproto_deps, 17, 0,
  schemas, file_default_instances, TableStruct_onnx_2dml_2eproto::offsets,
  file_level_metadata_onnx_2dml_2eproto, 22, file_level_enum_descriptors_onnx_2dml_2eproto, file_level_service_descriptors_onnx_2dml_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_onnx_2dml_2eproto = (  ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_onnx_2dml_2eproto), true);
namespace onnx {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AttributeProto_AttributeType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_onnx_2dml_2eproto);
  return file_level_enum_descriptors_onnx_2dml_2eproto[0];
}
bool AttributeProto_AttributeType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr AttributeProto_AttributeType AttributeProto::UNDEFINED;
constexpr AttributeProto_AttributeType AttributeProto::FLOAT;
constexpr AttributeProto_AttributeType AttributeProto::INT;
constexpr AttributeProto_AttributeType AttributeProto::STRING;
constexpr AttributeProto_AttributeType AttributeProto::TENSOR;
constexpr AttributeProto_AttributeType AttributeProto::GRAPH;
constexpr AttributeProto_AttributeType AttributeProto::SPARSE_TENSOR;
constexpr AttributeProto_AttributeType AttributeProto::TYPE_PROTO;
constexpr AttributeProto_AttributeType AttributeProto::FLOATS;
constexpr AttributeProto_AttributeType AttributeProto::INTS;
constexpr AttributeProto_AttributeType AttributeProto::STRINGS;
constexpr AttributeProto_AttributeType AttributeProto::TENSORS;
constexpr AttributeProto_AttributeType AttributeProto::GRAPHS;
constexpr AttributeProto_AttributeType AttributeProto::SPARSE_TENSORS;
constexpr AttributeProto_AttributeType AttributeProto::TYPE_PROTOS;
constexpr AttributeProto_AttributeType AttributeProto::AttributeType_MIN;
constexpr AttributeProto_AttributeType AttributeProto::AttributeType_MAX;
constexpr int AttributeProto::AttributeType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TensorProto_DataType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_onnx_2dml_2eproto);
  return file_level_enum_descriptors_onnx_2dml_2eproto[1];
}
bool TensorProto_DataType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr TensorProto_DataType TensorProto::UNDEFINED;
constexpr TensorProto_DataType TensorProto::FLOAT;
constexpr TensorProto_DataType TensorProto::UINT8;
constexpr TensorProto_DataType TensorProto::INT8;
constexpr TensorProto_DataType TensorProto::UINT16;
constexpr TensorProto_DataType TensorProto::INT16;
constexpr TensorProto_DataType TensorProto::INT32;
constexpr TensorProto_DataType TensorProto::INT64;
constexpr TensorProto_DataType TensorProto::STRING;
constexpr TensorProto_DataType TensorProto::BOOL;
constexpr TensorProto_DataType TensorProto::FLOAT16;
constexpr TensorProto_DataType TensorProto::DOUBLE;
constexpr TensorProto_DataType TensorProto::UINT32;
constexpr TensorProto_DataType TensorProto::UINT64;
constexpr TensorProto_DataType TensorProto::COMPLEX64;
constexpr TensorProto_DataType TensorProto::COMPLEX128;
constexpr TensorProto_DataType TensorProto::BFLOAT16;
constexpr TensorProto_DataType TensorProto::DataType_MIN;
constexpr TensorProto_DataType TensorProto::DataType_MAX;
constexpr int TensorProto::DataType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TensorProto_DataLocation_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_onnx_2dml_2eproto);
  return file_level_enum_descriptors_onnx_2dml_2eproto[2];
}
bool TensorProto_DataLocation_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr TensorProto_DataLocation TensorProto::DEFAULT;
constexpr TensorProto_DataLocation TensorProto::EXTERNAL;
constexpr TensorProto_DataLocation TensorProto::DataLocation_MIN;
constexpr TensorProto_DataLocation TensorProto::DataLocation_MAX;
constexpr int TensorProto::DataLocation_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Version_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_onnx_2dml_2eproto);
  return file_level_enum_descriptors_onnx_2dml_2eproto[3];
}
bool Version_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OperatorStatus_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_onnx_2dml_2eproto);
  return file_level_enum_descriptors_onnx_2dml_2eproto[4];
}
bool OperatorStatus_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

void AttributeProto::InitAsDefaultInstance() {
  ::onnx::_AttributeProto_default_instance_._instance.get_mutable()->t_ = const_cast< ::onnx::TensorProto*>(
      ::onnx::TensorProto::internal_default_instance());
  ::onnx::_AttributeProto_default_instance_._instance.get_mutable()->g_ = const_cast< ::onnx::GraphProto*>(
      ::onnx::GraphProto::internal_default_instance());
  ::onnx::_AttributeProto_default_instance_._instance.get_mutable()->sparse_tensor_ = const_cast< ::onnx::SparseTensorProto*>(
      ::onnx::SparseTensorProto::internal_default_instance());
  ::onnx::_AttributeProto_default_instance_._instance.get_mutable()->tp_ = const_cast< ::onnx::TypeProto*>(
      ::onnx::TypeProto::internal_default_instance());
}
class AttributeProto::_Internal {
 public:
  using HasBits = decltype(std::declval<AttributeProto>()._has_bits_);
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_ref_attr_name(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_doc_string(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_type(HasBits* has_bits) {
    (*has_bits)[0] |= 1024u;
  }
  static void set_has_f(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static void set_has_i(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_s(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::onnx::TensorProto& t(const AttributeProto* msg);
  static void set_has_t(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static const ::onnx::GraphProto& g(const AttributeProto* msg);
  static void set_has_g(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static const ::onnx::SparseTensorProto& sparse_tensor(const AttributeProto* msg);
  static void set_has_sparse_tensor(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static const ::onnx::TypeProto& tp(const AttributeProto* msg);
  static void set_has_tp(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
};

const ::onnx::TensorProto&
AttributeProto::_Internal::t(const AttributeProto* msg) {
  return *msg->t_;
}
const ::onnx::GraphProto&
AttributeProto::_Internal::g(const AttributeProto* msg) {
  return *msg->g_;
}
const ::onnx::SparseTensorProto&
AttributeProto::_Internal::sparse_tensor(const AttributeProto* msg) {
  return *msg->sparse_tensor_;
}
const ::onnx::TypeProto&
AttributeProto::_Internal::tp(const AttributeProto* msg) {
  return *msg->tp_;
}
AttributeProto::AttributeProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.AttributeProto)
}
AttributeProto::AttributeProto(const AttributeProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      floats_(from.floats_),
      ints_(from.ints_),
      strings_(from.strings_),
      tensors_(from.tensors_),
      graphs_(from.graphs_),
      type_protos_(from.type_protos_),
      sparse_tensors_(from.sparse_tensors_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_name()) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_s()) {
    s_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.s_);
  }
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_doc_string()) {
    doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
  }
  ref_attr_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_ref_attr_name()) {
    ref_attr_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.ref_attr_name_);
  }
  if (from._internal_has_t()) {
    t_ = new ::onnx::TensorProto(*from.t_);
  } else {
    t_ = nullptr;
  }
  if (from._internal_has_g()) {
    g_ = new ::onnx::GraphProto(*from.g_);
  } else {
    g_ = nullptr;
  }
  if (from._internal_has_tp()) {
    tp_ = new ::onnx::TypeProto(*from.tp_);
  } else {
    tp_ = nullptr;
  }
  if (from._internal_has_sparse_tensor()) {
    sparse_tensor_ = new ::onnx::SparseTensorProto(*from.sparse_tensor_);
  } else {
    sparse_tensor_ = nullptr;
  }
  ::memcpy(&i_, &from.i_,
    static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&i_)) + sizeof(type_));
  // @@protoc_insertion_point(copy_constructor:onnx.AttributeProto)
}

void AttributeProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_AttributeProto_onnx_2dml_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ref_attr_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&t_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&t_)) + sizeof(type_));
}

AttributeProto::~AttributeProto() {
  // @@protoc_insertion_point(destructor:onnx.AttributeProto)
  SharedDtor();
}

void AttributeProto::SharedDtor() {
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  s_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ref_attr_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete t_;
  if (this != internal_default_instance()) delete g_;
  if (this != internal_default_instance()) delete tp_;
  if (this != internal_default_instance()) delete sparse_tensor_;
}

void AttributeProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const AttributeProto& AttributeProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_AttributeProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void AttributeProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.AttributeProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  floats_.Clear();
  ints_.Clear();
  strings_.Clear();
  tensors_.Clear();
  graphs_.Clear();
  type_protos_.Clear();
  sparse_tensors_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      name_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      s_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000004u) {
      doc_string_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000008u) {
      ref_attr_name_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000010u) {
      GOOGLE_DCHECK(t_ != nullptr);
      t_->Clear();
    }
    if (cached_has_bits & 0x00000020u) {
      GOOGLE_DCHECK(g_ != nullptr);
      g_->Clear();
    }
    if (cached_has_bits & 0x00000040u) {
      GOOGLE_DCHECK(tp_ != nullptr);
      tp_->Clear();
    }
    if (cached_has_bits & 0x00000080u) {
      GOOGLE_DCHECK(sparse_tensor_ != nullptr);
      sparse_tensor_->Clear();
    }
  }
  if (cached_has_bits & 0x00000700u) {
    ::memset(&i_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&type_) -
        reinterpret_cast<char*>(&i_)) + sizeof(type_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* AttributeProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.AttributeProto.name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional float f = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          _Internal::set_has_f(&has_bits);
          f_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // optional int64 i = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_i(&has_bits);
          i_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bytes s = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_s();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TensorProto t = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_t(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.GraphProto g = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_g(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated float floats = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          ptr -= 1;
          do {
            ptr += 1;
            _internal_add_floats(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
            ptr += sizeof(float);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<61>(ptr));
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_floats(), ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int64 ints = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          ptr -= 1;
          do {
            ptr += 1;
            _internal_add_ints(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<64>(ptr));
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_ints(), ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated bytes strings = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_strings();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.TensorProto tensors = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_tensors(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<82>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.GraphProto graphs = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_graphs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<90>(ptr));
        } else goto handle_unusual;
        continue;
      // optional string doc_string = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          auto str = _internal_mutable_doc_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.AttributeProto.doc_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TypeProto tp = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          ptr = ctx->ParseMessage(_internal_mutable_tp(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.TypeProto type_protos = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_type_protos(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<122>(ptr));
        } else goto handle_unusual;
        continue;
      // optional .onnx.AttributeProto.AttributeType type = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 160)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::onnx::AttributeProto_AttributeType_IsValid(val))) {
            _internal_set_type(static_cast<::onnx::AttributeProto_AttributeType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(20, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string ref_attr_name = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 170)) {
          auto str = _internal_mutable_ref_attr_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.AttributeProto.ref_attr_name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.SparseTensorProto sparse_tensor = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 178)) {
          ptr = ctx->ParseMessage(_internal_mutable_sparse_tensor(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.SparseTensorProto sparse_tensors = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 186)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_sparse_tensors(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<186>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* AttributeProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.AttributeProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string name = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.AttributeProto.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // optional float f = 2;
  if (cached_has_bits & 0x00000200u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_f(), target);
  }

  // optional int64 i = 3;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_i(), target);
  }

  // optional bytes s = 4;
  if (cached_has_bits & 0x00000002u) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_s(), target);
  }

  // optional .onnx.TensorProto t = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::t(this), target, stream);
  }

  // optional .onnx.GraphProto g = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::g(this), target, stream);
  }

  // repeated float floats = 7;
  for (int i = 0, n = this->_internal_floats_size(); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_floats(i), target);
  }

  // repeated int64 ints = 8;
  for (int i = 0, n = this->_internal_ints_size(); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(8, this->_internal_ints(i), target);
  }

  // repeated bytes strings = 9;
  for (int i = 0, n = this->_internal_strings_size(); i < n; i++) {
    const auto& s = this->_internal_strings(i);
    target = stream->WriteBytes(9, s, target);
  }

  // repeated .onnx.TensorProto tensors = 10;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_tensors_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(10, this->_internal_tensors(i), target, stream);
  }

  // repeated .onnx.GraphProto graphs = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_graphs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(11, this->_internal_graphs(i), target, stream);
  }

  // optional string doc_string = 13;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_doc_string().data(), static_cast<int>(this->_internal_doc_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.AttributeProto.doc_string");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_doc_string(), target);
  }

  // optional .onnx.TypeProto tp = 14;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        14, _Internal::tp(this), target, stream);
  }

  // repeated .onnx.TypeProto type_protos = 15;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_type_protos_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(15, this->_internal_type_protos(i), target, stream);
  }

  // optional .onnx.AttributeProto.AttributeType type = 20;
  if (cached_has_bits & 0x00000400u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      20, this->_internal_type(), target);
  }

  // optional string ref_attr_name = 21;
  if (cached_has_bits & 0x00000008u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_ref_attr_name().data(), static_cast<int>(this->_internal_ref_attr_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.AttributeProto.ref_attr_name");
    target = stream->WriteStringMaybeAliased(
        21, this->_internal_ref_attr_name(), target);
  }

  // optional .onnx.SparseTensorProto sparse_tensor = 22;
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        22, _Internal::sparse_tensor(this), target, stream);
  }

  // repeated .onnx.SparseTensorProto sparse_tensors = 23;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_sparse_tensors_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(23, this->_internal_sparse_tensors(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.AttributeProto)
  return target;
}

size_t AttributeProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.AttributeProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated float floats = 7;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_floats_size());
    size_t data_size = 4UL * count;
    total_size += 1 *
                  ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_floats_size());
    total_size += data_size;
  }

  // repeated int64 ints = 8;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->ints_);
    total_size += 1 *
                  ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_ints_size());
    total_size += data_size;
  }

  // repeated bytes strings = 9;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(strings_.size());
  for (int i = 0, n = strings_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      strings_.Get(i));
  }

  // repeated .onnx.TensorProto tensors = 10;
  total_size += 1UL * this->_internal_tensors_size();
  for (const auto& msg : this->tensors_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.GraphProto graphs = 11;
  total_size += 1UL * this->_internal_graphs_size();
  for (const auto& msg : this->graphs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.TypeProto type_protos = 15;
  total_size += 1UL * this->_internal_type_protos_size();
  for (const auto& msg : this->type_protos_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.SparseTensorProto sparse_tensors = 23;
  total_size += 2UL * this->_internal_sparse_tensors_size();
  for (const auto& msg : this->sparse_tensors_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional string name = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

    // optional bytes s = 4;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_s());
    }

    // optional string doc_string = 13;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_doc_string());
    }

    // optional string ref_attr_name = 21;
    if (cached_has_bits & 0x00000008u) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_ref_attr_name());
    }

    // optional .onnx.TensorProto t = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *t_);
    }

    // optional .onnx.GraphProto g = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *g_);
    }

    // optional .onnx.TypeProto tp = 14;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *tp_);
    }

    // optional .onnx.SparseTensorProto sparse_tensor = 22;
    if (cached_has_bits & 0x00000080u) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *sparse_tensor_);
    }

  }
  if (cached_has_bits & 0x00000700u) {
    // optional int64 i = 3;
    if (cached_has_bits & 0x00000100u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_i());
    }

    // optional float f = 2;
    if (cached_has_bits & 0x00000200u) {
      total_size += 1 + 4;
    }

    // optional .onnx.AttributeProto.AttributeType type = 20;
    if (cached_has_bits & 0x00000400u) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AttributeProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.AttributeProto)
  GOOGLE_DCHECK_NE(&from, this);
  const AttributeProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<AttributeProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.AttributeProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.AttributeProto)
    MergeFrom(*source);
  }
}

void AttributeProto::MergeFrom(const AttributeProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.AttributeProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  floats_.MergeFrom(from.floats_);
  ints_.MergeFrom(from.ints_);
  strings_.MergeFrom(from.strings_);
  tensors_.MergeFrom(from.tensors_);
  graphs_.MergeFrom(from.graphs_);
  type_protos_.MergeFrom(from.type_protos_);
  sparse_tensors_.MergeFrom(from.sparse_tensors_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      s_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.s_);
    }
    if (cached_has_bits & 0x00000004u) {
      _has_bits_[0] |= 0x00000004u;
      doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
    }
    if (cached_has_bits & 0x00000008u) {
      _has_bits_[0] |= 0x00000008u;
      ref_attr_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.ref_attr_name_);
    }
    if (cached_has_bits & 0x00000010u) {
      _internal_mutable_t()->::onnx::TensorProto::MergeFrom(from._internal_t());
    }
    if (cached_has_bits & 0x00000020u) {
      _internal_mutable_g()->::onnx::GraphProto::MergeFrom(from._internal_g());
    }
    if (cached_has_bits & 0x00000040u) {
      _internal_mutable_tp()->::onnx::TypeProto::MergeFrom(from._internal_tp());
    }
    if (cached_has_bits & 0x00000080u) {
      _internal_mutable_sparse_tensor()->::onnx::SparseTensorProto::MergeFrom(from._internal_sparse_tensor());
    }
  }
  if (cached_has_bits & 0x00000700u) {
    if (cached_has_bits & 0x00000100u) {
      i_ = from.i_;
    }
    if (cached_has_bits & 0x00000200u) {
      f_ = from.f_;
    }
    if (cached_has_bits & 0x00000400u) {
      type_ = from.type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void AttributeProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.AttributeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AttributeProto::CopyFrom(const AttributeProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.AttributeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AttributeProto::IsInitialized() const {
  return true;
}

void AttributeProto::InternalSwap(AttributeProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  floats_.InternalSwap(&other->floats_);
  ints_.InternalSwap(&other->ints_);
  strings_.InternalSwap(&other->strings_);
  tensors_.InternalSwap(&other->tensors_);
  graphs_.InternalSwap(&other->graphs_);
  type_protos_.InternalSwap(&other->type_protos_);
  sparse_tensors_.InternalSwap(&other->sparse_tensors_);
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  s_.Swap(&other->s_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  doc_string_.Swap(&other->doc_string_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  ref_attr_name_.Swap(&other->ref_attr_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(t_, other->t_);
  swap(g_, other->g_);
  swap(tp_, other->tp_);
  swap(sparse_tensor_, other->sparse_tensor_);
  swap(i_, other->i_);
  swap(f_, other->f_);
  swap(type_, other->type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AttributeProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void ValueInfoProto::InitAsDefaultInstance() {
  ::onnx::_ValueInfoProto_default_instance_._instance.get_mutable()->type_ = const_cast< ::onnx::TypeProto*>(
      ::onnx::TypeProto::internal_default_instance());
}
class ValueInfoProto::_Internal {
 public:
  using HasBits = decltype(std::declval<ValueInfoProto>()._has_bits_);
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::onnx::TypeProto& type(const ValueInfoProto* msg);
  static void set_has_type(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_doc_string(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

const ::onnx::TypeProto&
ValueInfoProto::_Internal::type(const ValueInfoProto* msg) {
  return *msg->type_;
}
ValueInfoProto::ValueInfoProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.ValueInfoProto)
}
ValueInfoProto::ValueInfoProto(const ValueInfoProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_name()) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_doc_string()) {
    doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
  }
  if (from._internal_has_type()) {
    type_ = new ::onnx::TypeProto(*from.type_);
  } else {
    type_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:onnx.ValueInfoProto)
}

void ValueInfoProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ValueInfoProto_onnx_2dml_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  type_ = nullptr;
}

ValueInfoProto::~ValueInfoProto() {
  // @@protoc_insertion_point(destructor:onnx.ValueInfoProto)
  SharedDtor();
}

void ValueInfoProto::SharedDtor() {
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete type_;
}

void ValueInfoProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ValueInfoProto& ValueInfoProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ValueInfoProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void ValueInfoProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.ValueInfoProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      name_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      doc_string_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(type_ != nullptr);
      type_->Clear();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* ValueInfoProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.ValueInfoProto.name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TypeProto type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string doc_string = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_doc_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.ValueInfoProto.doc_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ValueInfoProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.ValueInfoProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string name = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.ValueInfoProto.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // optional .onnx.TypeProto type = 2;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::type(this), target, stream);
  }

  // optional string doc_string = 3;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_doc_string().data(), static_cast<int>(this->_internal_doc_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.ValueInfoProto.doc_string");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_doc_string(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.ValueInfoProto)
  return target;
}

size_t ValueInfoProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.ValueInfoProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    // optional string name = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

    // optional string doc_string = 3;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_doc_string());
    }

    // optional .onnx.TypeProto type = 2;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *type_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ValueInfoProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.ValueInfoProto)
  GOOGLE_DCHECK_NE(&from, this);
  const ValueInfoProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ValueInfoProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.ValueInfoProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.ValueInfoProto)
    MergeFrom(*source);
  }
}

void ValueInfoProto::MergeFrom(const ValueInfoProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.ValueInfoProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_mutable_type()->::onnx::TypeProto::MergeFrom(from._internal_type());
    }
  }
}

void ValueInfoProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.ValueInfoProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ValueInfoProto::CopyFrom(const ValueInfoProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.ValueInfoProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ValueInfoProto::IsInitialized() const {
  return true;
}

void ValueInfoProto::InternalSwap(ValueInfoProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  doc_string_.Swap(&other->doc_string_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(type_, other->type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ValueInfoProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void NodeProto::InitAsDefaultInstance() {
}
class NodeProto::_Internal {
 public:
  using HasBits = decltype(std::declval<NodeProto>()._has_bits_);
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_op_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_domain(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_doc_string(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

NodeProto::NodeProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.NodeProto)
}
NodeProto::NodeProto(const NodeProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      input_(from.input_),
      output_(from.output_),
      attribute_(from.attribute_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_name()) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  op_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_op_type()) {
    op_type_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.op_type_);
  }
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_doc_string()) {
    doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
  }
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_domain()) {
    domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
  }
  // @@protoc_insertion_point(copy_constructor:onnx.NodeProto)
}

void NodeProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_AttributeProto_onnx_2dml_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  op_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

NodeProto::~NodeProto() {
  // @@protoc_insertion_point(destructor:onnx.NodeProto)
  SharedDtor();
}

void NodeProto::SharedDtor() {
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  op_type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  domain_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void NodeProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const NodeProto& NodeProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_AttributeProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void NodeProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.NodeProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_.Clear();
  output_.Clear();
  attribute_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      name_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      op_type_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000004u) {
      doc_string_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000008u) {
      domain_.ClearNonDefaultToEmptyNoArena();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* NodeProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated string input = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_input();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.NodeProto.input");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated string output = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_output();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.NodeProto.output");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      // optional string name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.NodeProto.name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string op_type = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_op_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.NodeProto.op_type");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.AttributeProto attribute = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_attribute(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else goto handle_unusual;
        continue;
      // optional string doc_string = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_doc_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.NodeProto.doc_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string domain = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_domain();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.NodeProto.domain");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* NodeProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.NodeProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string input = 1;
  for (int i = 0, n = this->_internal_input_size(); i < n; i++) {
    const auto& s = this->_internal_input(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.NodeProto.input");
    target = stream->WriteString(1, s, target);
  }

  // repeated string output = 2;
  for (int i = 0, n = this->_internal_output_size(); i < n; i++) {
    const auto& s = this->_internal_output(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.NodeProto.output");
    target = stream->WriteString(2, s, target);
  }

  cached_has_bits = _has_bits_[0];
  // optional string name = 3;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.NodeProto.name");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_name(), target);
  }

  // optional string op_type = 4;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_op_type().data(), static_cast<int>(this->_internal_op_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.NodeProto.op_type");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_op_type(), target);
  }

  // repeated .onnx.AttributeProto attribute = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_attribute_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_attribute(i), target, stream);
  }

  // optional string doc_string = 6;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_doc_string().data(), static_cast<int>(this->_internal_doc_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.NodeProto.doc_string");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_doc_string(), target);
  }

  // optional string domain = 7;
  if (cached_has_bits & 0x00000008u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_domain().data(), static_cast<int>(this->_internal_domain().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.NodeProto.domain");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_domain(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.NodeProto)
  return target;
}

size_t NodeProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.NodeProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string input = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(input_.size());
  for (int i = 0, n = input_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      input_.Get(i));
  }

  // repeated string output = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(output_.size());
  for (int i = 0, n = output_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      output_.Get(i));
  }

  // repeated .onnx.AttributeProto attribute = 5;
  total_size += 1UL * this->_internal_attribute_size();
  for (const auto& msg : this->attribute_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional string name = 3;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

    // optional string op_type = 4;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_op_type());
    }

    // optional string doc_string = 6;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_doc_string());
    }

    // optional string domain = 7;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_domain());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NodeProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.NodeProto)
  GOOGLE_DCHECK_NE(&from, this);
  const NodeProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<NodeProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.NodeProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.NodeProto)
    MergeFrom(*source);
  }
}

void NodeProto::MergeFrom(const NodeProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.NodeProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  input_.MergeFrom(from.input_);
  output_.MergeFrom(from.output_);
  attribute_.MergeFrom(from.attribute_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      op_type_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.op_type_);
    }
    if (cached_has_bits & 0x00000004u) {
      _has_bits_[0] |= 0x00000004u;
      doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
    }
    if (cached_has_bits & 0x00000008u) {
      _has_bits_[0] |= 0x00000008u;
      domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
    }
  }
}

void NodeProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.NodeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NodeProto::CopyFrom(const NodeProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.NodeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NodeProto::IsInitialized() const {
  return true;
}

void NodeProto::InternalSwap(NodeProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  input_.InternalSwap(&other->input_);
  output_.InternalSwap(&other->output_);
  attribute_.InternalSwap(&other->attribute_);
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  op_type_.Swap(&other->op_type_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  doc_string_.Swap(&other->doc_string_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  domain_.Swap(&other->domain_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata NodeProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TrainingInfoProto::InitAsDefaultInstance() {
  ::onnx::_TrainingInfoProto_default_instance_._instance.get_mutable()->initialization_ = const_cast< ::onnx::GraphProto*>(
      ::onnx::GraphProto::internal_default_instance());
  ::onnx::_TrainingInfoProto_default_instance_._instance.get_mutable()->algorithm_ = const_cast< ::onnx::GraphProto*>(
      ::onnx::GraphProto::internal_default_instance());
}
class TrainingInfoProto::_Internal {
 public:
  using HasBits = decltype(std::declval<TrainingInfoProto>()._has_bits_);
  static const ::onnx::GraphProto& initialization(const TrainingInfoProto* msg);
  static void set_has_initialization(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::onnx::GraphProto& algorithm(const TrainingInfoProto* msg);
  static void set_has_algorithm(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

const ::onnx::GraphProto&
TrainingInfoProto::_Internal::initialization(const TrainingInfoProto* msg) {
  return *msg->initialization_;
}
const ::onnx::GraphProto&
TrainingInfoProto::_Internal::algorithm(const TrainingInfoProto* msg) {
  return *msg->algorithm_;
}
TrainingInfoProto::TrainingInfoProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TrainingInfoProto)
}
TrainingInfoProto::TrainingInfoProto(const TrainingInfoProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      initialization_binding_(from.initialization_binding_),
      update_binding_(from.update_binding_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from._internal_has_initialization()) {
    initialization_ = new ::onnx::GraphProto(*from.initialization_);
  } else {
    initialization_ = nullptr;
  }
  if (from._internal_has_algorithm()) {
    algorithm_ = new ::onnx::GraphProto(*from.algorithm_);
  } else {
    algorithm_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:onnx.TrainingInfoProto)
}

void TrainingInfoProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TrainingInfoProto_onnx_2dml_2eproto.base);
  ::memset(&initialization_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&algorithm_) -
      reinterpret_cast<char*>(&initialization_)) + sizeof(algorithm_));
}

TrainingInfoProto::~TrainingInfoProto() {
  // @@protoc_insertion_point(destructor:onnx.TrainingInfoProto)
  SharedDtor();
}

void TrainingInfoProto::SharedDtor() {
  if (this != internal_default_instance()) delete initialization_;
  if (this != internal_default_instance()) delete algorithm_;
}

void TrainingInfoProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TrainingInfoProto& TrainingInfoProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TrainingInfoProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TrainingInfoProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TrainingInfoProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  initialization_binding_.Clear();
  update_binding_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(initialization_ != nullptr);
      initialization_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(algorithm_ != nullptr);
      algorithm_->Clear();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TrainingInfoProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .onnx.GraphProto initialization = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_initialization(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.GraphProto algorithm = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_algorithm(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.StringStringEntryProto initialization_binding = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_initialization_binding(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.StringStringEntryProto update_binding = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_update_binding(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TrainingInfoProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TrainingInfoProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .onnx.GraphProto initialization = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::initialization(this), target, stream);
  }

  // optional .onnx.GraphProto algorithm = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::algorithm(this), target, stream);
  }

  // repeated .onnx.StringStringEntryProto initialization_binding = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_initialization_binding_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_initialization_binding(i), target, stream);
  }

  // repeated .onnx.StringStringEntryProto update_binding = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_update_binding_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_update_binding(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TrainingInfoProto)
  return target;
}

size_t TrainingInfoProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TrainingInfoProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .onnx.StringStringEntryProto initialization_binding = 3;
  total_size += 1UL * this->_internal_initialization_binding_size();
  for (const auto& msg : this->initialization_binding_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.StringStringEntryProto update_binding = 4;
  total_size += 1UL * this->_internal_update_binding_size();
  for (const auto& msg : this->update_binding_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional .onnx.GraphProto initialization = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *initialization_);
    }

    // optional .onnx.GraphProto algorithm = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *algorithm_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TrainingInfoProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TrainingInfoProto)
  GOOGLE_DCHECK_NE(&from, this);
  const TrainingInfoProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TrainingInfoProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TrainingInfoProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TrainingInfoProto)
    MergeFrom(*source);
  }
}

void TrainingInfoProto::MergeFrom(const TrainingInfoProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TrainingInfoProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  initialization_binding_.MergeFrom(from.initialization_binding_);
  update_binding_.MergeFrom(from.update_binding_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_initialization()->::onnx::GraphProto::MergeFrom(from._internal_initialization());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_algorithm()->::onnx::GraphProto::MergeFrom(from._internal_algorithm());
    }
  }
}

void TrainingInfoProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TrainingInfoProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TrainingInfoProto::CopyFrom(const TrainingInfoProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TrainingInfoProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrainingInfoProto::IsInitialized() const {
  return true;
}

void TrainingInfoProto::InternalSwap(TrainingInfoProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  initialization_binding_.InternalSwap(&other->initialization_binding_);
  update_binding_.InternalSwap(&other->update_binding_);
  swap(initialization_, other->initialization_);
  swap(algorithm_, other->algorithm_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TrainingInfoProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void ModelProto::InitAsDefaultInstance() {
  ::onnx::_ModelProto_default_instance_._instance.get_mutable()->graph_ = const_cast< ::onnx::GraphProto*>(
      ::onnx::GraphProto::internal_default_instance());
}
class ModelProto::_Internal {
 public:
  using HasBits = decltype(std::declval<ModelProto>()._has_bits_);
  static void set_has_ir_version(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_producer_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_producer_version(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_domain(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_model_version(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_doc_string(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::onnx::GraphProto& graph(const ModelProto* msg);
  static void set_has_graph(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
};

const ::onnx::GraphProto&
ModelProto::_Internal::graph(const ModelProto* msg) {
  return *msg->graph_;
}
ModelProto::ModelProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.ModelProto)
}
ModelProto::ModelProto(const ModelProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      opset_import_(from.opset_import_),
      metadata_props_(from.metadata_props_),
      training_info_(from.training_info_),
      functions_(from.functions_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  producer_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_producer_name()) {
    producer_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.producer_name_);
  }
  producer_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_producer_version()) {
    producer_version_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.producer_version_);
  }
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_domain()) {
    domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
  }
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_doc_string()) {
    doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
  }
  if (from._internal_has_graph()) {
    graph_ = new ::onnx::GraphProto(*from.graph_);
  } else {
    graph_ = nullptr;
  }
  ::memcpy(&ir_version_, &from.ir_version_,
    static_cast<size_t>(reinterpret_cast<char*>(&model_version_) -
    reinterpret_cast<char*>(&ir_version_)) + sizeof(model_version_));
  // @@protoc_insertion_point(copy_constructor:onnx.ModelProto)
}

void ModelProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ModelProto_onnx_2dml_2eproto.base);
  producer_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  producer_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&graph_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&model_version_) -
      reinterpret_cast<char*>(&graph_)) + sizeof(model_version_));
}

ModelProto::~ModelProto() {
  // @@protoc_insertion_point(destructor:onnx.ModelProto)
  SharedDtor();
}

void ModelProto::SharedDtor() {
  producer_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  producer_version_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  domain_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete graph_;
}

void ModelProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ModelProto& ModelProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ModelProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void ModelProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.ModelProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  opset_import_.Clear();
  metadata_props_.Clear();
  training_info_.Clear();
  functions_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      producer_name_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      producer_version_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000004u) {
      domain_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000008u) {
      doc_string_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000010u) {
      GOOGLE_DCHECK(graph_ != nullptr);
      graph_->Clear();
    }
  }
  if (cached_has_bits & 0x00000060u) {
    ::memset(&ir_version_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&model_version_) -
        reinterpret_cast<char*>(&ir_version_)) + sizeof(model_version_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* ModelProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional int64 ir_version = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_ir_version(&has_bits);
          ir_version_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string producer_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_producer_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.ModelProto.producer_name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string producer_version = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_producer_version();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.ModelProto.producer_version");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string domain = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_domain();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.ModelProto.domain");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int64 model_version = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_model_version(&has_bits);
          model_version_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string doc_string = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_doc_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.ModelProto.doc_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.GraphProto graph = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_graph(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.OperatorSetIdProto opset_import = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_opset_import(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.StringStringEntryProto metadata_props = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_metadata_props(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<114>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.TrainingInfoProto training_info = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 162)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_training_info(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<162>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.FunctionProto functions = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 202)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_functions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<202>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ModelProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.ModelProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int64 ir_version = 1;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_ir_version(), target);
  }

  // optional string producer_name = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_producer_name().data(), static_cast<int>(this->_internal_producer_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.ModelProto.producer_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_producer_name(), target);
  }

  // optional string producer_version = 3;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_producer_version().data(), static_cast<int>(this->_internal_producer_version().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.ModelProto.producer_version");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_producer_version(), target);
  }

  // optional string domain = 4;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_domain().data(), static_cast<int>(this->_internal_domain().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.ModelProto.domain");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_domain(), target);
  }

  // optional int64 model_version = 5;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(5, this->_internal_model_version(), target);
  }

  // optional string doc_string = 6;
  if (cached_has_bits & 0x00000008u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_doc_string().data(), static_cast<int>(this->_internal_doc_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.ModelProto.doc_string");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_doc_string(), target);
  }

  // optional .onnx.GraphProto graph = 7;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::graph(this), target, stream);
  }

  // repeated .onnx.OperatorSetIdProto opset_import = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_opset_import_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(8, this->_internal_opset_import(i), target, stream);
  }

  // repeated .onnx.StringStringEntryProto metadata_props = 14;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_metadata_props_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(14, this->_internal_metadata_props(i), target, stream);
  }

  // repeated .onnx.TrainingInfoProto training_info = 20;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_training_info_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(20, this->_internal_training_info(i), target, stream);
  }

  // repeated .onnx.FunctionProto functions = 25;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_functions_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(25, this->_internal_functions(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.ModelProto)
  return target;
}

size_t ModelProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.ModelProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .onnx.OperatorSetIdProto opset_import = 8;
  total_size += 1UL * this->_internal_opset_import_size();
  for (const auto& msg : this->opset_import_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.StringStringEntryProto metadata_props = 14;
  total_size += 1UL * this->_internal_metadata_props_size();
  for (const auto& msg : this->metadata_props_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.TrainingInfoProto training_info = 20;
  total_size += 2UL * this->_internal_training_info_size();
  for (const auto& msg : this->training_info_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.FunctionProto functions = 25;
  total_size += 2UL * this->_internal_functions_size();
  for (const auto& msg : this->functions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    // optional string producer_name = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_producer_name());
    }

    // optional string producer_version = 3;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_producer_version());
    }

    // optional string domain = 4;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_domain());
    }

    // optional string doc_string = 6;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_doc_string());
    }

    // optional .onnx.GraphProto graph = 7;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *graph_);
    }

    // optional int64 ir_version = 1;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_ir_version());
    }

    // optional int64 model_version = 5;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_model_version());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ModelProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.ModelProto)
  GOOGLE_DCHECK_NE(&from, this);
  const ModelProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ModelProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.ModelProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.ModelProto)
    MergeFrom(*source);
  }
}

void ModelProto::MergeFrom(const ModelProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.ModelProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  opset_import_.MergeFrom(from.opset_import_);
  metadata_props_.MergeFrom(from.metadata_props_);
  training_info_.MergeFrom(from.training_info_);
  functions_.MergeFrom(from.functions_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      producer_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.producer_name_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      producer_version_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.producer_version_);
    }
    if (cached_has_bits & 0x00000004u) {
      _has_bits_[0] |= 0x00000004u;
      domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
    }
    if (cached_has_bits & 0x00000008u) {
      _has_bits_[0] |= 0x00000008u;
      doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
    }
    if (cached_has_bits & 0x00000010u) {
      _internal_mutable_graph()->::onnx::GraphProto::MergeFrom(from._internal_graph());
    }
    if (cached_has_bits & 0x00000020u) {
      ir_version_ = from.ir_version_;
    }
    if (cached_has_bits & 0x00000040u) {
      model_version_ = from.model_version_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ModelProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.ModelProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ModelProto::CopyFrom(const ModelProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.ModelProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelProto::IsInitialized() const {
  return true;
}

void ModelProto::InternalSwap(ModelProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  opset_import_.InternalSwap(&other->opset_import_);
  metadata_props_.InternalSwap(&other->metadata_props_);
  training_info_.InternalSwap(&other->training_info_);
  functions_.InternalSwap(&other->functions_);
  producer_name_.Swap(&other->producer_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  producer_version_.Swap(&other->producer_version_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  domain_.Swap(&other->domain_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  doc_string_.Swap(&other->doc_string_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(graph_, other->graph_);
  swap(ir_version_, other->ir_version_);
  swap(model_version_, other->model_version_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void StringStringEntryProto::InitAsDefaultInstance() {
}
class StringStringEntryProto::_Internal {
 public:
  using HasBits = decltype(std::declval<StringStringEntryProto>()._has_bits_);
  static void set_has_key(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_value(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

StringStringEntryProto::StringStringEntryProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.StringStringEntryProto)
}
StringStringEntryProto::StringStringEntryProto(const StringStringEntryProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_key()) {
    key_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.key_);
  }
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_value()) {
    value_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.value_);
  }
  // @@protoc_insertion_point(copy_constructor:onnx.StringStringEntryProto)
}

void StringStringEntryProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_StringStringEntryProto_onnx_2dml_2eproto.base);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

StringStringEntryProto::~StringStringEntryProto() {
  // @@protoc_insertion_point(destructor:onnx.StringStringEntryProto)
  SharedDtor();
}

void StringStringEntryProto::SharedDtor() {
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StringStringEntryProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const StringStringEntryProto& StringStringEntryProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_StringStringEntryProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void StringStringEntryProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.StringStringEntryProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      key_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      value_.ClearNonDefaultToEmptyNoArena();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* StringStringEntryProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.StringStringEntryProto.key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.StringStringEntryProto.value");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* StringStringEntryProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.StringStringEntryProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string key = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.StringStringEntryProto.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  // optional string value = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_value().data(), static_cast<int>(this->_internal_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.StringStringEntryProto.value");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.StringStringEntryProto)
  return target;
}

size_t StringStringEntryProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.StringStringEntryProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string key = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_key());
    }

    // optional string value = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_value());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void StringStringEntryProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.StringStringEntryProto)
  GOOGLE_DCHECK_NE(&from, this);
  const StringStringEntryProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<StringStringEntryProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.StringStringEntryProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.StringStringEntryProto)
    MergeFrom(*source);
  }
}

void StringStringEntryProto::MergeFrom(const StringStringEntryProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.StringStringEntryProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      key_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.key_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      value_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.value_);
    }
  }
}

void StringStringEntryProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.StringStringEntryProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StringStringEntryProto::CopyFrom(const StringStringEntryProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.StringStringEntryProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StringStringEntryProto::IsInitialized() const {
  return true;
}

void StringStringEntryProto::InternalSwap(StringStringEntryProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  key_.Swap(&other->key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  value_.Swap(&other->value_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata StringStringEntryProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TensorAnnotation::InitAsDefaultInstance() {
}
class TensorAnnotation::_Internal {
 public:
  using HasBits = decltype(std::declval<TensorAnnotation>()._has_bits_);
  static void set_has_tensor_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

TensorAnnotation::TensorAnnotation()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TensorAnnotation)
}
TensorAnnotation::TensorAnnotation(const TensorAnnotation& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      quant_parameter_tensor_names_(from.quant_parameter_tensor_names_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  tensor_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_tensor_name()) {
    tensor_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.tensor_name_);
  }
  // @@protoc_insertion_point(copy_constructor:onnx.TensorAnnotation)
}

void TensorAnnotation::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TensorAnnotation_onnx_2dml_2eproto.base);
  tensor_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

TensorAnnotation::~TensorAnnotation() {
  // @@protoc_insertion_point(destructor:onnx.TensorAnnotation)
  SharedDtor();
}

void TensorAnnotation::SharedDtor() {
  tensor_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TensorAnnotation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TensorAnnotation& TensorAnnotation::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TensorAnnotation_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TensorAnnotation::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TensorAnnotation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  quant_parameter_tensor_names_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    tensor_name_.ClearNonDefaultToEmptyNoArena();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TensorAnnotation::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string tensor_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_tensor_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.TensorAnnotation.tensor_name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.StringStringEntryProto quant_parameter_tensor_names = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_quant_parameter_tensor_names(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TensorAnnotation::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TensorAnnotation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string tensor_name = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_tensor_name().data(), static_cast<int>(this->_internal_tensor_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.TensorAnnotation.tensor_name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_tensor_name(), target);
  }

  // repeated .onnx.StringStringEntryProto quant_parameter_tensor_names = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_quant_parameter_tensor_names_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_quant_parameter_tensor_names(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TensorAnnotation)
  return target;
}

size_t TensorAnnotation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TensorAnnotation)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .onnx.StringStringEntryProto quant_parameter_tensor_names = 2;
  total_size += 1UL * this->_internal_quant_parameter_tensor_names_size();
  for (const auto& msg : this->quant_parameter_tensor_names_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // optional string tensor_name = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_tensor_name());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorAnnotation::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TensorAnnotation)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorAnnotation* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TensorAnnotation>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TensorAnnotation)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TensorAnnotation)
    MergeFrom(*source);
  }
}

void TensorAnnotation::MergeFrom(const TensorAnnotation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TensorAnnotation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  quant_parameter_tensor_names_.MergeFrom(from.quant_parameter_tensor_names_);
  if (from._internal_has_tensor_name()) {
    _has_bits_[0] |= 0x00000001u;
    tensor_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.tensor_name_);
  }
}

void TensorAnnotation::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TensorAnnotation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorAnnotation::CopyFrom(const TensorAnnotation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TensorAnnotation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorAnnotation::IsInitialized() const {
  return true;
}

void TensorAnnotation::InternalSwap(TensorAnnotation* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  quant_parameter_tensor_names_.InternalSwap(&other->quant_parameter_tensor_names_);
  tensor_name_.Swap(&other->tensor_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata TensorAnnotation::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void GraphProto::InitAsDefaultInstance() {
}
class GraphProto::_Internal {
 public:
  using HasBits = decltype(std::declval<GraphProto>()._has_bits_);
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_doc_string(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

GraphProto::GraphProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.GraphProto)
}
GraphProto::GraphProto(const GraphProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      node_(from.node_),
      initializer_(from.initializer_),
      input_(from.input_),
      output_(from.output_),
      value_info_(from.value_info_),
      quantization_annotation_(from.quantization_annotation_),
      sparse_initializer_(from.sparse_initializer_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_name()) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_doc_string()) {
    doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
  }
  // @@protoc_insertion_point(copy_constructor:onnx.GraphProto)
}

void GraphProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_AttributeProto_onnx_2dml_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

GraphProto::~GraphProto() {
  // @@protoc_insertion_point(destructor:onnx.GraphProto)
  SharedDtor();
}

void GraphProto::SharedDtor() {
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GraphProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const GraphProto& GraphProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_AttributeProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void GraphProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.GraphProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_.Clear();
  initializer_.Clear();
  input_.Clear();
  output_.Clear();
  value_info_.Clear();
  quantization_annotation_.Clear();
  sparse_initializer_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      name_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      doc_string_.ClearNonDefaultToEmptyNoArena();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* GraphProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .onnx.NodeProto node = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_node(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      // optional string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.GraphProto.name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.TensorProto initializer = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_initializer(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else goto handle_unusual;
        continue;
      // optional string doc_string = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_doc_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.GraphProto.doc_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.ValueInfoProto input = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_input(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<90>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.ValueInfoProto output = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_output(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<98>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.ValueInfoProto value_info = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_value_info(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<106>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.TensorAnnotation quantization_annotation = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_quantization_annotation(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<114>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.SparseTensorProto sparse_initializer = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_sparse_initializer(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<122>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GraphProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.GraphProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .onnx.NodeProto node = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_node_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_node(i), target, stream);
  }

  cached_has_bits = _has_bits_[0];
  // optional string name = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.GraphProto.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // repeated .onnx.TensorProto initializer = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_initializer_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_initializer(i), target, stream);
  }

  // optional string doc_string = 10;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_doc_string().data(), static_cast<int>(this->_internal_doc_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.GraphProto.doc_string");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_doc_string(), target);
  }

  // repeated .onnx.ValueInfoProto input = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_input_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(11, this->_internal_input(i), target, stream);
  }

  // repeated .onnx.ValueInfoProto output = 12;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_output_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(12, this->_internal_output(i), target, stream);
  }

  // repeated .onnx.ValueInfoProto value_info = 13;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_value_info_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(13, this->_internal_value_info(i), target, stream);
  }

  // repeated .onnx.TensorAnnotation quantization_annotation = 14;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_quantization_annotation_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(14, this->_internal_quantization_annotation(i), target, stream);
  }

  // repeated .onnx.SparseTensorProto sparse_initializer = 15;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_sparse_initializer_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(15, this->_internal_sparse_initializer(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.GraphProto)
  return target;
}

size_t GraphProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.GraphProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .onnx.NodeProto node = 1;
  total_size += 1UL * this->_internal_node_size();
  for (const auto& msg : this->node_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.TensorProto initializer = 5;
  total_size += 1UL * this->_internal_initializer_size();
  for (const auto& msg : this->initializer_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.ValueInfoProto input = 11;
  total_size += 1UL * this->_internal_input_size();
  for (const auto& msg : this->input_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.ValueInfoProto output = 12;
  total_size += 1UL * this->_internal_output_size();
  for (const auto& msg : this->output_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.ValueInfoProto value_info = 13;
  total_size += 1UL * this->_internal_value_info_size();
  for (const auto& msg : this->value_info_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.TensorAnnotation quantization_annotation = 14;
  total_size += 1UL * this->_internal_quantization_annotation_size();
  for (const auto& msg : this->quantization_annotation_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.SparseTensorProto sparse_initializer = 15;
  total_size += 1UL * this->_internal_sparse_initializer_size();
  for (const auto& msg : this->sparse_initializer_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string name = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

    // optional string doc_string = 10;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_doc_string());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.GraphProto)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<GraphProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.GraphProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.GraphProto)
    MergeFrom(*source);
  }
}

void GraphProto::MergeFrom(const GraphProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.GraphProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  node_.MergeFrom(from.node_);
  initializer_.MergeFrom(from.initializer_);
  input_.MergeFrom(from.input_);
  output_.MergeFrom(from.output_);
  value_info_.MergeFrom(from.value_info_);
  quantization_annotation_.MergeFrom(from.quantization_annotation_);
  sparse_initializer_.MergeFrom(from.sparse_initializer_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
    }
  }
}

void GraphProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.GraphProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphProto::CopyFrom(const GraphProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.GraphProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphProto::IsInitialized() const {
  return true;
}

void GraphProto::InternalSwap(GraphProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  node_.InternalSwap(&other->node_);
  initializer_.InternalSwap(&other->initializer_);
  input_.InternalSwap(&other->input_);
  output_.InternalSwap(&other->output_);
  value_info_.InternalSwap(&other->value_info_);
  quantization_annotation_.InternalSwap(&other->quantization_annotation_);
  sparse_initializer_.InternalSwap(&other->sparse_initializer_);
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  doc_string_.Swap(&other->doc_string_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata GraphProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TensorProto_Segment::InitAsDefaultInstance() {
}
class TensorProto_Segment::_Internal {
 public:
  using HasBits = decltype(std::declval<TensorProto_Segment>()._has_bits_);
  static void set_has_begin(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_end(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

TensorProto_Segment::TensorProto_Segment()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TensorProto.Segment)
}
TensorProto_Segment::TensorProto_Segment(const TensorProto_Segment& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&begin_, &from.begin_,
    static_cast<size_t>(reinterpret_cast<char*>(&end_) -
    reinterpret_cast<char*>(&begin_)) + sizeof(end_));
  // @@protoc_insertion_point(copy_constructor:onnx.TensorProto.Segment)
}

void TensorProto_Segment::SharedCtor() {
  ::memset(&begin_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&end_) -
      reinterpret_cast<char*>(&begin_)) + sizeof(end_));
}

TensorProto_Segment::~TensorProto_Segment() {
  // @@protoc_insertion_point(destructor:onnx.TensorProto.Segment)
  SharedDtor();
}

void TensorProto_Segment::SharedDtor() {
}

void TensorProto_Segment::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TensorProto_Segment& TensorProto_Segment::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TensorProto_Segment_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TensorProto_Segment::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TensorProto.Segment)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&begin_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&end_) -
        reinterpret_cast<char*>(&begin_)) + sizeof(end_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TensorProto_Segment::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional int64 begin = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_begin(&has_bits);
          begin_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int64 end = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_end(&has_bits);
          end_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TensorProto_Segment::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TensorProto.Segment)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int64 begin = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_begin(), target);
  }

  // optional int64 end = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_end(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TensorProto.Segment)
  return target;
}

size_t TensorProto_Segment::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TensorProto.Segment)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional int64 begin = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_begin());
    }

    // optional int64 end = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_end());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorProto_Segment::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TensorProto.Segment)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorProto_Segment* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TensorProto_Segment>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TensorProto.Segment)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TensorProto.Segment)
    MergeFrom(*source);
  }
}

void TensorProto_Segment::MergeFrom(const TensorProto_Segment& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TensorProto.Segment)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      begin_ = from.begin_;
    }
    if (cached_has_bits & 0x00000002u) {
      end_ = from.end_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void TensorProto_Segment::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TensorProto.Segment)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorProto_Segment::CopyFrom(const TensorProto_Segment& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TensorProto.Segment)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorProto_Segment::IsInitialized() const {
  return true;
}

void TensorProto_Segment::InternalSwap(TensorProto_Segment* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(begin_, other->begin_);
  swap(end_, other->end_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TensorProto_Segment::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TensorProto::InitAsDefaultInstance() {
  ::onnx::_TensorProto_default_instance_._instance.get_mutable()->segment_ = const_cast< ::onnx::TensorProto_Segment*>(
      ::onnx::TensorProto_Segment::internal_default_instance());
}
class TensorProto::_Internal {
 public:
  using HasBits = decltype(std::declval<TensorProto>()._has_bits_);
  static void set_has_data_type(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static const ::onnx::TensorProto_Segment& segment(const TensorProto* msg);
  static void set_has_segment(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_doc_string(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_raw_data(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_data_location(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
};

const ::onnx::TensorProto_Segment&
TensorProto::_Internal::segment(const TensorProto* msg) {
  return *msg->segment_;
}
TensorProto::TensorProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TensorProto)
}
TensorProto::TensorProto(const TensorProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      dims_(from.dims_),
      float_data_(from.float_data_),
      int32_data_(from.int32_data_),
      string_data_(from.string_data_),
      int64_data_(from.int64_data_),
      double_data_(from.double_data_),
      uint64_data_(from.uint64_data_),
      external_data_(from.external_data_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_name()) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  raw_data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_raw_data()) {
    raw_data_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.raw_data_);
  }
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_doc_string()) {
    doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
  }
  if (from._internal_has_segment()) {
    segment_ = new ::onnx::TensorProto_Segment(*from.segment_);
  } else {
    segment_ = nullptr;
  }
  ::memcpy(&data_type_, &from.data_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&data_location_) -
    reinterpret_cast<char*>(&data_type_)) + sizeof(data_location_));
  // @@protoc_insertion_point(copy_constructor:onnx.TensorProto)
}

void TensorProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TensorProto_onnx_2dml_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  raw_data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&segment_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&data_location_) -
      reinterpret_cast<char*>(&segment_)) + sizeof(data_location_));
}

TensorProto::~TensorProto() {
  // @@protoc_insertion_point(destructor:onnx.TensorProto)
  SharedDtor();
}

void TensorProto::SharedDtor() {
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  raw_data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete segment_;
}

void TensorProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TensorProto& TensorProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TensorProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TensorProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TensorProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  dims_.Clear();
  float_data_.Clear();
  int32_data_.Clear();
  string_data_.Clear();
  int64_data_.Clear();
  double_data_.Clear();
  uint64_data_.Clear();
  external_data_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      name_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      raw_data_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000004u) {
      doc_string_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000008u) {
      GOOGLE_DCHECK(segment_ != nullptr);
      segment_->Clear();
    }
  }
  if (cached_has_bits & 0x00000030u) {
    ::memset(&data_type_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&data_location_) -
        reinterpret_cast<char*>(&data_type_)) + sizeof(data_location_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TensorProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated int64 dims = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ptr -= 1;
          do {
            ptr += 1;
            _internal_add_dims(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<8>(ptr));
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_dims(), ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int32 data_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_data_type(&has_bits);
          data_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TensorProto.Segment segment = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_segment(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated float float_data = 4 [packed = true];
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_float_data(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37) {
          _internal_add_float_data(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // repeated int32 int32_data = 5 [packed = true];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_int32_data(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40) {
          _internal_add_int32_data(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated bytes string_data = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_string_data();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated int64 int64_data = 7 [packed = true];
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_int64_data(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56) {
          _internal_add_int64_data(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string name = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.TensorProto.name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bytes raw_data = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_raw_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated double double_data = 10 [packed = true];
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedDoubleParser(_internal_mutable_double_data(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 81) {
          _internal_add_double_data(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // repeated uint64 uint64_data = 11 [packed = true];
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt64Parser(_internal_mutable_uint64_data(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88) {
          _internal_add_uint64_data(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string doc_string = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          auto str = _internal_mutable_doc_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.TensorProto.doc_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.StringStringEntryProto external_data = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_external_data(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<106>(ptr));
        } else goto handle_unusual;
        continue;
      // optional .onnx.TensorProto.DataLocation data_location = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::onnx::TensorProto_DataLocation_IsValid(val))) {
            _internal_set_data_location(static_cast<::onnx::TensorProto_DataLocation>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(14, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TensorProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TensorProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 dims = 1;
  for (int i = 0, n = this->_internal_dims_size(); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_dims(i), target);
  }

  cached_has_bits = _has_bits_[0];
  // optional int32 data_type = 2;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_data_type(), target);
  }

  // optional .onnx.TensorProto.Segment segment = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::segment(this), target, stream);
  }

  // repeated float float_data = 4 [packed = true];
  if (this->_internal_float_data_size() > 0) {
    target = stream->WriteFixedPacked(4, _internal_float_data(), target);
  }

  // repeated int32 int32_data = 5 [packed = true];
  {
    int byte_size = _int32_data_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          5, _internal_int32_data(), byte_size, target);
    }
  }

  // repeated bytes string_data = 6;
  for (int i = 0, n = this->_internal_string_data_size(); i < n; i++) {
    const auto& s = this->_internal_string_data(i);
    target = stream->WriteBytes(6, s, target);
  }

  // repeated int64 int64_data = 7 [packed = true];
  {
    int byte_size = _int64_data_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          7, _internal_int64_data(), byte_size, target);
    }
  }

  // optional string name = 8;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.TensorProto.name");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_name(), target);
  }

  // optional bytes raw_data = 9;
  if (cached_has_bits & 0x00000002u) {
    target = stream->WriteBytesMaybeAliased(
        9, this->_internal_raw_data(), target);
  }

  // repeated double double_data = 10 [packed = true];
  if (this->_internal_double_data_size() > 0) {
    target = stream->WriteFixedPacked(10, _internal_double_data(), target);
  }

  // repeated uint64 uint64_data = 11 [packed = true];
  {
    int byte_size = _uint64_data_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt64Packed(
          11, _internal_uint64_data(), byte_size, target);
    }
  }

  // optional string doc_string = 12;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_doc_string().data(), static_cast<int>(this->_internal_doc_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.TensorProto.doc_string");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_doc_string(), target);
  }

  // repeated .onnx.StringStringEntryProto external_data = 13;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_external_data_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(13, this->_internal_external_data(i), target, stream);
  }

  // optional .onnx.TensorProto.DataLocation data_location = 14;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      14, this->_internal_data_location(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TensorProto)
  return target;
}

size_t TensorProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TensorProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int64 dims = 1;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->dims_);
    total_size += 1 *
                  ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_dims_size());
    total_size += data_size;
  }

  // repeated float float_data = 4 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_float_data_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _float_data_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 int32_data = 5 [packed = true];
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->int32_data_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _int32_data_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated bytes string_data = 6;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(string_data_.size());
  for (int i = 0, n = string_data_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      string_data_.Get(i));
  }

  // repeated int64 int64_data = 7 [packed = true];
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->int64_data_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _int64_data_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated double double_data = 10 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_double_data_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _double_data_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated uint64 uint64_data = 11 [packed = true];
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt64Size(this->uint64_data_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _uint64_data_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated .onnx.StringStringEntryProto external_data = 13;
  total_size += 1UL * this->_internal_external_data_size();
  for (const auto& msg : this->external_data_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    // optional string name = 8;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

    // optional bytes raw_data = 9;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_raw_data());
    }

    // optional string doc_string = 12;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_doc_string());
    }

    // optional .onnx.TensorProto.Segment segment = 3;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *segment_);
    }

    // optional int32 data_type = 2;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
          this->_internal_data_type());
    }

    // optional .onnx.TensorProto.DataLocation data_location = 14;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_data_location());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TensorProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TensorProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TensorProto)
    MergeFrom(*source);
  }
}

void TensorProto::MergeFrom(const TensorProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  dims_.MergeFrom(from.dims_);
  float_data_.MergeFrom(from.float_data_);
  int32_data_.MergeFrom(from.int32_data_);
  string_data_.MergeFrom(from.string_data_);
  int64_data_.MergeFrom(from.int64_data_);
  double_data_.MergeFrom(from.double_data_);
  uint64_data_.MergeFrom(from.uint64_data_);
  external_data_.MergeFrom(from.external_data_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      raw_data_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.raw_data_);
    }
    if (cached_has_bits & 0x00000004u) {
      _has_bits_[0] |= 0x00000004u;
      doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
    }
    if (cached_has_bits & 0x00000008u) {
      _internal_mutable_segment()->::onnx::TensorProto_Segment::MergeFrom(from._internal_segment());
    }
    if (cached_has_bits & 0x00000010u) {
      data_type_ = from.data_type_;
    }
    if (cached_has_bits & 0x00000020u) {
      data_location_ = from.data_location_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void TensorProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorProto::CopyFrom(const TensorProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorProto::IsInitialized() const {
  return true;
}

void TensorProto::InternalSwap(TensorProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  dims_.InternalSwap(&other->dims_);
  float_data_.InternalSwap(&other->float_data_);
  int32_data_.InternalSwap(&other->int32_data_);
  string_data_.InternalSwap(&other->string_data_);
  int64_data_.InternalSwap(&other->int64_data_);
  double_data_.InternalSwap(&other->double_data_);
  uint64_data_.InternalSwap(&other->uint64_data_);
  external_data_.InternalSwap(&other->external_data_);
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  raw_data_.Swap(&other->raw_data_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  doc_string_.Swap(&other->doc_string_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(segment_, other->segment_);
  swap(data_type_, other->data_type_);
  swap(data_location_, other->data_location_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TensorProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void SparseTensorProto::InitAsDefaultInstance() {
  ::onnx::_SparseTensorProto_default_instance_._instance.get_mutable()->values_ = const_cast< ::onnx::TensorProto*>(
      ::onnx::TensorProto::internal_default_instance());
  ::onnx::_SparseTensorProto_default_instance_._instance.get_mutable()->indices_ = const_cast< ::onnx::TensorProto*>(
      ::onnx::TensorProto::internal_default_instance());
}
class SparseTensorProto::_Internal {
 public:
  using HasBits = decltype(std::declval<SparseTensorProto>()._has_bits_);
  static const ::onnx::TensorProto& values(const SparseTensorProto* msg);
  static void set_has_values(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::onnx::TensorProto& indices(const SparseTensorProto* msg);
  static void set_has_indices(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

const ::onnx::TensorProto&
SparseTensorProto::_Internal::values(const SparseTensorProto* msg) {
  return *msg->values_;
}
const ::onnx::TensorProto&
SparseTensorProto::_Internal::indices(const SparseTensorProto* msg) {
  return *msg->indices_;
}
SparseTensorProto::SparseTensorProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.SparseTensorProto)
}
SparseTensorProto::SparseTensorProto(const SparseTensorProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      dims_(from.dims_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from._internal_has_values()) {
    values_ = new ::onnx::TensorProto(*from.values_);
  } else {
    values_ = nullptr;
  }
  if (from._internal_has_indices()) {
    indices_ = new ::onnx::TensorProto(*from.indices_);
  } else {
    indices_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:onnx.SparseTensorProto)
}

void SparseTensorProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_SparseTensorProto_onnx_2dml_2eproto.base);
  ::memset(&values_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&indices_) -
      reinterpret_cast<char*>(&values_)) + sizeof(indices_));
}

SparseTensorProto::~SparseTensorProto() {
  // @@protoc_insertion_point(destructor:onnx.SparseTensorProto)
  SharedDtor();
}

void SparseTensorProto::SharedDtor() {
  if (this != internal_default_instance()) delete values_;
  if (this != internal_default_instance()) delete indices_;
}

void SparseTensorProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const SparseTensorProto& SparseTensorProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_SparseTensorProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void SparseTensorProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.SparseTensorProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  dims_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(values_ != nullptr);
      values_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(indices_ != nullptr);
      indices_->Clear();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* SparseTensorProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .onnx.TensorProto values = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_values(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TensorProto indices = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_indices(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int64 dims = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ptr -= 1;
          do {
            ptr += 1;
            _internal_add_dims(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<24>(ptr));
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_dims(), ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SparseTensorProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.SparseTensorProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .onnx.TensorProto values = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::values(this), target, stream);
  }

  // optional .onnx.TensorProto indices = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::indices(this), target, stream);
  }

  // repeated int64 dims = 3;
  for (int i = 0, n = this->_internal_dims_size(); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_dims(i), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.SparseTensorProto)
  return target;
}

size_t SparseTensorProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.SparseTensorProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int64 dims = 3;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->dims_);
    total_size += 1 *
                  ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_dims_size());
    total_size += data_size;
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional .onnx.TensorProto values = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *values_);
    }

    // optional .onnx.TensorProto indices = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *indices_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SparseTensorProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.SparseTensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  const SparseTensorProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<SparseTensorProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.SparseTensorProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.SparseTensorProto)
    MergeFrom(*source);
  }
}

void SparseTensorProto::MergeFrom(const SparseTensorProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.SparseTensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  dims_.MergeFrom(from.dims_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_values()->::onnx::TensorProto::MergeFrom(from._internal_values());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_indices()->::onnx::TensorProto::MergeFrom(from._internal_indices());
    }
  }
}

void SparseTensorProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.SparseTensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SparseTensorProto::CopyFrom(const SparseTensorProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.SparseTensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SparseTensorProto::IsInitialized() const {
  return true;
}

void SparseTensorProto::InternalSwap(SparseTensorProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  dims_.InternalSwap(&other->dims_);
  swap(values_, other->values_);
  swap(indices_, other->indices_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SparseTensorProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TensorShapeProto_Dimension::InitAsDefaultInstance() {
  ::onnx::_TensorShapeProto_Dimension_default_instance_.dim_value_ = PROTOBUF_LONGLONG(0);
  ::onnx::_TensorShapeProto_Dimension_default_instance_.dim_param_.UnsafeSetDefault(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
class TensorShapeProto_Dimension::_Internal {
 public:
  using HasBits = decltype(std::declval<TensorShapeProto_Dimension>()._has_bits_);
  static void set_has_denotation(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

TensorShapeProto_Dimension::TensorShapeProto_Dimension()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TensorShapeProto.Dimension)
}
TensorShapeProto_Dimension::TensorShapeProto_Dimension(const TensorShapeProto_Dimension& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  denotation_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_denotation()) {
    denotation_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.denotation_);
  }
  clear_has_value();
  switch (from.value_case()) {
    case kDimValue: {
      _internal_set_dim_value(from._internal_dim_value());
      break;
    }
    case kDimParam: {
      _internal_set_dim_param(from._internal_dim_param());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:onnx.TensorShapeProto.Dimension)
}

void TensorShapeProto_Dimension::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TensorShapeProto_Dimension_onnx_2dml_2eproto.base);
  denotation_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  clear_has_value();
}

TensorShapeProto_Dimension::~TensorShapeProto_Dimension() {
  // @@protoc_insertion_point(destructor:onnx.TensorShapeProto.Dimension)
  SharedDtor();
}

void TensorShapeProto_Dimension::SharedDtor() {
  denotation_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (has_value()) {
    clear_value();
  }
}

void TensorShapeProto_Dimension::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TensorShapeProto_Dimension& TensorShapeProto_Dimension::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TensorShapeProto_Dimension_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TensorShapeProto_Dimension::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:onnx.TensorShapeProto.Dimension)
  switch (value_case()) {
    case kDimValue: {
      // No need to clear
      break;
    }
    case kDimParam: {
      value_.dim_param_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void TensorShapeProto_Dimension::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TensorShapeProto.Dimension)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    denotation_.ClearNonDefaultToEmptyNoArena();
  }
  clear_value();
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TensorShapeProto_Dimension::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional int64 dim_value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _internal_set_dim_value(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string dim_param = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_dim_param();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.TensorShapeProto.Dimension.dim_param");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string denotation = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_denotation();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.TensorShapeProto.Dimension.denotation");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TensorShapeProto_Dimension::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TensorShapeProto.Dimension)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (value_case()) {
    case kDimValue: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_dim_value(), target);
      break;
    }
    case kDimParam: {
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
        this->_internal_dim_param().data(), static_cast<int>(this->_internal_dim_param().length()),
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
        "onnx.TensorShapeProto.Dimension.dim_param");
      target = stream->WriteStringMaybeAliased(
          2, this->_internal_dim_param(), target);
      break;
    }
    default: ;
  }
  cached_has_bits = _has_bits_[0];
  // optional string denotation = 3;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_denotation().data(), static_cast<int>(this->_internal_denotation().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.TensorShapeProto.Dimension.denotation");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_denotation(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TensorShapeProto.Dimension)
  return target;
}

size_t TensorShapeProto_Dimension::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TensorShapeProto.Dimension)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string denotation = 3;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_denotation());
  }

  switch (value_case()) {
    // optional int64 dim_value = 1;
    case kDimValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_dim_value());
      break;
    }
    // optional string dim_param = 2;
    case kDimParam: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_dim_param());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorShapeProto_Dimension::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TensorShapeProto.Dimension)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorShapeProto_Dimension* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TensorShapeProto_Dimension>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TensorShapeProto.Dimension)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TensorShapeProto.Dimension)
    MergeFrom(*source);
  }
}

void TensorShapeProto_Dimension::MergeFrom(const TensorShapeProto_Dimension& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TensorShapeProto.Dimension)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_denotation()) {
    _has_bits_[0] |= 0x00000001u;
    denotation_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.denotation_);
  }
  switch (from.value_case()) {
    case kDimValue: {
      _internal_set_dim_value(from._internal_dim_value());
      break;
    }
    case kDimParam: {
      _internal_set_dim_param(from._internal_dim_param());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
}

void TensorShapeProto_Dimension::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TensorShapeProto.Dimension)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorShapeProto_Dimension::CopyFrom(const TensorShapeProto_Dimension& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TensorShapeProto.Dimension)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorShapeProto_Dimension::IsInitialized() const {
  return true;
}

void TensorShapeProto_Dimension::InternalSwap(TensorShapeProto_Dimension* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  denotation_.Swap(&other->denotation_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata TensorShapeProto_Dimension::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TensorShapeProto::InitAsDefaultInstance() {
}
class TensorShapeProto::_Internal {
 public:
  using HasBits = decltype(std::declval<TensorShapeProto>()._has_bits_);
};

TensorShapeProto::TensorShapeProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TensorShapeProto)
}
TensorShapeProto::TensorShapeProto(const TensorShapeProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      dim_(from.dim_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:onnx.TensorShapeProto)
}

void TensorShapeProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TensorShapeProto_onnx_2dml_2eproto.base);
}

TensorShapeProto::~TensorShapeProto() {
  // @@protoc_insertion_point(destructor:onnx.TensorShapeProto)
  SharedDtor();
}

void TensorShapeProto::SharedDtor() {
}

void TensorShapeProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TensorShapeProto& TensorShapeProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TensorShapeProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TensorShapeProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TensorShapeProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  dim_.Clear();
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TensorShapeProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .onnx.TensorShapeProto.Dimension dim = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_dim(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TensorShapeProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TensorShapeProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .onnx.TensorShapeProto.Dimension dim = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_dim_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_dim(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TensorShapeProto)
  return target;
}

size_t TensorShapeProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TensorShapeProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .onnx.TensorShapeProto.Dimension dim = 1;
  total_size += 1UL * this->_internal_dim_size();
  for (const auto& msg : this->dim_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorShapeProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TensorShapeProto)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorShapeProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TensorShapeProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TensorShapeProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TensorShapeProto)
    MergeFrom(*source);
  }
}

void TensorShapeProto::MergeFrom(const TensorShapeProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TensorShapeProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  dim_.MergeFrom(from.dim_);
}

void TensorShapeProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TensorShapeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorShapeProto::CopyFrom(const TensorShapeProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TensorShapeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorShapeProto::IsInitialized() const {
  return true;
}

void TensorShapeProto::InternalSwap(TensorShapeProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  dim_.InternalSwap(&other->dim_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TensorShapeProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TypeProto_Tensor::InitAsDefaultInstance() {
  ::onnx::_TypeProto_Tensor_default_instance_._instance.get_mutable()->shape_ = const_cast< ::onnx::TensorShapeProto*>(
      ::onnx::TensorShapeProto::internal_default_instance());
}
class TypeProto_Tensor::_Internal {
 public:
  using HasBits = decltype(std::declval<TypeProto_Tensor>()._has_bits_);
  static void set_has_elem_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::onnx::TensorShapeProto& shape(const TypeProto_Tensor* msg);
  static void set_has_shape(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::onnx::TensorShapeProto&
TypeProto_Tensor::_Internal::shape(const TypeProto_Tensor* msg) {
  return *msg->shape_;
}
TypeProto_Tensor::TypeProto_Tensor()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TypeProto.Tensor)
}
TypeProto_Tensor::TypeProto_Tensor(const TypeProto_Tensor& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from._internal_has_shape()) {
    shape_ = new ::onnx::TensorShapeProto(*from.shape_);
  } else {
    shape_ = nullptr;
  }
  elem_type_ = from.elem_type_;
  // @@protoc_insertion_point(copy_constructor:onnx.TypeProto.Tensor)
}

void TypeProto_Tensor::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TypeProto_Tensor_onnx_2dml_2eproto.base);
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&elem_type_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(elem_type_));
}

TypeProto_Tensor::~TypeProto_Tensor() {
  // @@protoc_insertion_point(destructor:onnx.TypeProto.Tensor)
  SharedDtor();
}

void TypeProto_Tensor::SharedDtor() {
  if (this != internal_default_instance()) delete shape_;
}

void TypeProto_Tensor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TypeProto_Tensor& TypeProto_Tensor::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TypeProto_Tensor_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TypeProto_Tensor::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TypeProto.Tensor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(shape_ != nullptr);
    shape_->Clear();
  }
  elem_type_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TypeProto_Tensor::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional int32 elem_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_elem_type(&has_bits);
          elem_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TensorShapeProto shape = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_shape(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TypeProto_Tensor::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TypeProto.Tensor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 elem_type = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_elem_type(), target);
  }

  // optional .onnx.TensorShapeProto shape = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::shape(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TypeProto.Tensor)
  return target;
}

size_t TypeProto_Tensor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TypeProto.Tensor)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional .onnx.TensorShapeProto shape = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *shape_);
    }

    // optional int32 elem_type = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
          this->_internal_elem_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TypeProto_Tensor::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TypeProto.Tensor)
  GOOGLE_DCHECK_NE(&from, this);
  const TypeProto_Tensor* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TypeProto_Tensor>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TypeProto.Tensor)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TypeProto.Tensor)
    MergeFrom(*source);
  }
}

void TypeProto_Tensor::MergeFrom(const TypeProto_Tensor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TypeProto.Tensor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_shape()->::onnx::TensorShapeProto::MergeFrom(from._internal_shape());
    }
    if (cached_has_bits & 0x00000002u) {
      elem_type_ = from.elem_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void TypeProto_Tensor::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TypeProto.Tensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TypeProto_Tensor::CopyFrom(const TypeProto_Tensor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TypeProto.Tensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TypeProto_Tensor::IsInitialized() const {
  return true;
}

void TypeProto_Tensor::InternalSwap(TypeProto_Tensor* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(shape_, other->shape_);
  swap(elem_type_, other->elem_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TypeProto_Tensor::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TypeProto_Sequence::InitAsDefaultInstance() {
  ::onnx::_TypeProto_Sequence_default_instance_._instance.get_mutable()->elem_type_ = const_cast< ::onnx::TypeProto*>(
      ::onnx::TypeProto::internal_default_instance());
}
class TypeProto_Sequence::_Internal {
 public:
  using HasBits = decltype(std::declval<TypeProto_Sequence>()._has_bits_);
  static const ::onnx::TypeProto& elem_type(const TypeProto_Sequence* msg);
  static void set_has_elem_type(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::onnx::TypeProto&
TypeProto_Sequence::_Internal::elem_type(const TypeProto_Sequence* msg) {
  return *msg->elem_type_;
}
TypeProto_Sequence::TypeProto_Sequence()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TypeProto.Sequence)
}
TypeProto_Sequence::TypeProto_Sequence(const TypeProto_Sequence& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from._internal_has_elem_type()) {
    elem_type_ = new ::onnx::TypeProto(*from.elem_type_);
  } else {
    elem_type_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:onnx.TypeProto.Sequence)
}

void TypeProto_Sequence::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TypeProto_onnx_2dml_2eproto.base);
  elem_type_ = nullptr;
}

TypeProto_Sequence::~TypeProto_Sequence() {
  // @@protoc_insertion_point(destructor:onnx.TypeProto.Sequence)
  SharedDtor();
}

void TypeProto_Sequence::SharedDtor() {
  if (this != internal_default_instance()) delete elem_type_;
}

void TypeProto_Sequence::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TypeProto_Sequence& TypeProto_Sequence::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TypeProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TypeProto_Sequence::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TypeProto.Sequence)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(elem_type_ != nullptr);
    elem_type_->Clear();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TypeProto_Sequence::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .onnx.TypeProto elem_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_elem_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TypeProto_Sequence::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TypeProto.Sequence)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .onnx.TypeProto elem_type = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::elem_type(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TypeProto.Sequence)
  return target;
}

size_t TypeProto_Sequence::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TypeProto.Sequence)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional .onnx.TypeProto elem_type = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *elem_type_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TypeProto_Sequence::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TypeProto.Sequence)
  GOOGLE_DCHECK_NE(&from, this);
  const TypeProto_Sequence* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TypeProto_Sequence>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TypeProto.Sequence)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TypeProto.Sequence)
    MergeFrom(*source);
  }
}

void TypeProto_Sequence::MergeFrom(const TypeProto_Sequence& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TypeProto.Sequence)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_elem_type()) {
    _internal_mutable_elem_type()->::onnx::TypeProto::MergeFrom(from._internal_elem_type());
  }
}

void TypeProto_Sequence::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TypeProto.Sequence)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TypeProto_Sequence::CopyFrom(const TypeProto_Sequence& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TypeProto.Sequence)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TypeProto_Sequence::IsInitialized() const {
  return true;
}

void TypeProto_Sequence::InternalSwap(TypeProto_Sequence* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(elem_type_, other->elem_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TypeProto_Sequence::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TypeProto_Map::InitAsDefaultInstance() {
  ::onnx::_TypeProto_Map_default_instance_._instance.get_mutable()->value_type_ = const_cast< ::onnx::TypeProto*>(
      ::onnx::TypeProto::internal_default_instance());
}
class TypeProto_Map::_Internal {
 public:
  using HasBits = decltype(std::declval<TypeProto_Map>()._has_bits_);
  static void set_has_key_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::onnx::TypeProto& value_type(const TypeProto_Map* msg);
  static void set_has_value_type(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::onnx::TypeProto&
TypeProto_Map::_Internal::value_type(const TypeProto_Map* msg) {
  return *msg->value_type_;
}
TypeProto_Map::TypeProto_Map()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TypeProto.Map)
}
TypeProto_Map::TypeProto_Map(const TypeProto_Map& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from._internal_has_value_type()) {
    value_type_ = new ::onnx::TypeProto(*from.value_type_);
  } else {
    value_type_ = nullptr;
  }
  key_type_ = from.key_type_;
  // @@protoc_insertion_point(copy_constructor:onnx.TypeProto.Map)
}

void TypeProto_Map::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TypeProto_onnx_2dml_2eproto.base);
  ::memset(&value_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&key_type_) -
      reinterpret_cast<char*>(&value_type_)) + sizeof(key_type_));
}

TypeProto_Map::~TypeProto_Map() {
  // @@protoc_insertion_point(destructor:onnx.TypeProto.Map)
  SharedDtor();
}

void TypeProto_Map::SharedDtor() {
  if (this != internal_default_instance()) delete value_type_;
}

void TypeProto_Map::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TypeProto_Map& TypeProto_Map::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TypeProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TypeProto_Map::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TypeProto.Map)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(value_type_ != nullptr);
    value_type_->Clear();
  }
  key_type_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TypeProto_Map::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional int32 key_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_key_type(&has_bits);
          key_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TypeProto value_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_value_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TypeProto_Map::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TypeProto.Map)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 key_type = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_key_type(), target);
  }

  // optional .onnx.TypeProto value_type = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::value_type(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TypeProto.Map)
  return target;
}

size_t TypeProto_Map::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TypeProto.Map)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional .onnx.TypeProto value_type = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_type_);
    }

    // optional int32 key_type = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
          this->_internal_key_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TypeProto_Map::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TypeProto.Map)
  GOOGLE_DCHECK_NE(&from, this);
  const TypeProto_Map* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TypeProto_Map>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TypeProto.Map)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TypeProto.Map)
    MergeFrom(*source);
  }
}

void TypeProto_Map::MergeFrom(const TypeProto_Map& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TypeProto.Map)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_value_type()->::onnx::TypeProto::MergeFrom(from._internal_value_type());
    }
    if (cached_has_bits & 0x00000002u) {
      key_type_ = from.key_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void TypeProto_Map::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TypeProto.Map)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TypeProto_Map::CopyFrom(const TypeProto_Map& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TypeProto.Map)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TypeProto_Map::IsInitialized() const {
  return true;
}

void TypeProto_Map::InternalSwap(TypeProto_Map* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(value_type_, other->value_type_);
  swap(key_type_, other->key_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TypeProto_Map::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TypeProto_Optional::InitAsDefaultInstance() {
  ::onnx::_TypeProto_Optional_default_instance_._instance.get_mutable()->elem_type_ = const_cast< ::onnx::TypeProto*>(
      ::onnx::TypeProto::internal_default_instance());
}
class TypeProto_Optional::_Internal {
 public:
  using HasBits = decltype(std::declval<TypeProto_Optional>()._has_bits_);
  static const ::onnx::TypeProto& elem_type(const TypeProto_Optional* msg);
  static void set_has_elem_type(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::onnx::TypeProto&
TypeProto_Optional::_Internal::elem_type(const TypeProto_Optional* msg) {
  return *msg->elem_type_;
}
TypeProto_Optional::TypeProto_Optional()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TypeProto.Optional)
}
TypeProto_Optional::TypeProto_Optional(const TypeProto_Optional& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from._internal_has_elem_type()) {
    elem_type_ = new ::onnx::TypeProto(*from.elem_type_);
  } else {
    elem_type_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:onnx.TypeProto.Optional)
}

void TypeProto_Optional::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TypeProto_onnx_2dml_2eproto.base);
  elem_type_ = nullptr;
}

TypeProto_Optional::~TypeProto_Optional() {
  // @@protoc_insertion_point(destructor:onnx.TypeProto.Optional)
  SharedDtor();
}

void TypeProto_Optional::SharedDtor() {
  if (this != internal_default_instance()) delete elem_type_;
}

void TypeProto_Optional::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TypeProto_Optional& TypeProto_Optional::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TypeProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TypeProto_Optional::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TypeProto.Optional)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(elem_type_ != nullptr);
    elem_type_->Clear();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TypeProto_Optional::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .onnx.TypeProto elem_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_elem_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TypeProto_Optional::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TypeProto.Optional)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .onnx.TypeProto elem_type = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::elem_type(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TypeProto.Optional)
  return target;
}

size_t TypeProto_Optional::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TypeProto.Optional)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional .onnx.TypeProto elem_type = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *elem_type_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TypeProto_Optional::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TypeProto.Optional)
  GOOGLE_DCHECK_NE(&from, this);
  const TypeProto_Optional* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TypeProto_Optional>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TypeProto.Optional)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TypeProto.Optional)
    MergeFrom(*source);
  }
}

void TypeProto_Optional::MergeFrom(const TypeProto_Optional& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TypeProto.Optional)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_elem_type()) {
    _internal_mutable_elem_type()->::onnx::TypeProto::MergeFrom(from._internal_elem_type());
  }
}

void TypeProto_Optional::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TypeProto.Optional)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TypeProto_Optional::CopyFrom(const TypeProto_Optional& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TypeProto.Optional)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TypeProto_Optional::IsInitialized() const {
  return true;
}

void TypeProto_Optional::InternalSwap(TypeProto_Optional* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(elem_type_, other->elem_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TypeProto_Optional::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TypeProto_SparseTensor::InitAsDefaultInstance() {
  ::onnx::_TypeProto_SparseTensor_default_instance_._instance.get_mutable()->shape_ = const_cast< ::onnx::TensorShapeProto*>(
      ::onnx::TensorShapeProto::internal_default_instance());
}
class TypeProto_SparseTensor::_Internal {
 public:
  using HasBits = decltype(std::declval<TypeProto_SparseTensor>()._has_bits_);
  static void set_has_elem_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::onnx::TensorShapeProto& shape(const TypeProto_SparseTensor* msg);
  static void set_has_shape(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::onnx::TensorShapeProto&
TypeProto_SparseTensor::_Internal::shape(const TypeProto_SparseTensor* msg) {
  return *msg->shape_;
}
TypeProto_SparseTensor::TypeProto_SparseTensor()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TypeProto.SparseTensor)
}
TypeProto_SparseTensor::TypeProto_SparseTensor(const TypeProto_SparseTensor& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from._internal_has_shape()) {
    shape_ = new ::onnx::TensorShapeProto(*from.shape_);
  } else {
    shape_ = nullptr;
  }
  elem_type_ = from.elem_type_;
  // @@protoc_insertion_point(copy_constructor:onnx.TypeProto.SparseTensor)
}

void TypeProto_SparseTensor::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TypeProto_SparseTensor_onnx_2dml_2eproto.base);
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&elem_type_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(elem_type_));
}

TypeProto_SparseTensor::~TypeProto_SparseTensor() {
  // @@protoc_insertion_point(destructor:onnx.TypeProto.SparseTensor)
  SharedDtor();
}

void TypeProto_SparseTensor::SharedDtor() {
  if (this != internal_default_instance()) delete shape_;
}

void TypeProto_SparseTensor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TypeProto_SparseTensor& TypeProto_SparseTensor::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TypeProto_SparseTensor_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TypeProto_SparseTensor::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TypeProto.SparseTensor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(shape_ != nullptr);
    shape_->Clear();
  }
  elem_type_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TypeProto_SparseTensor::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional int32 elem_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_elem_type(&has_bits);
          elem_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TensorShapeProto shape = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_shape(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TypeProto_SparseTensor::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TypeProto.SparseTensor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 elem_type = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_elem_type(), target);
  }

  // optional .onnx.TensorShapeProto shape = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::shape(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TypeProto.SparseTensor)
  return target;
}

size_t TypeProto_SparseTensor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TypeProto.SparseTensor)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional .onnx.TensorShapeProto shape = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *shape_);
    }

    // optional int32 elem_type = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
          this->_internal_elem_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TypeProto_SparseTensor::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TypeProto.SparseTensor)
  GOOGLE_DCHECK_NE(&from, this);
  const TypeProto_SparseTensor* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TypeProto_SparseTensor>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TypeProto.SparseTensor)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TypeProto.SparseTensor)
    MergeFrom(*source);
  }
}

void TypeProto_SparseTensor::MergeFrom(const TypeProto_SparseTensor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TypeProto.SparseTensor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_shape()->::onnx::TensorShapeProto::MergeFrom(from._internal_shape());
    }
    if (cached_has_bits & 0x00000002u) {
      elem_type_ = from.elem_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void TypeProto_SparseTensor::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TypeProto.SparseTensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TypeProto_SparseTensor::CopyFrom(const TypeProto_SparseTensor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TypeProto.SparseTensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TypeProto_SparseTensor::IsInitialized() const {
  return true;
}

void TypeProto_SparseTensor::InternalSwap(TypeProto_SparseTensor* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(shape_, other->shape_);
  swap(elem_type_, other->elem_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TypeProto_SparseTensor::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TypeProto_Opaque::InitAsDefaultInstance() {
}
class TypeProto_Opaque::_Internal {
 public:
  using HasBits = decltype(std::declval<TypeProto_Opaque>()._has_bits_);
  static void set_has_domain(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

TypeProto_Opaque::TypeProto_Opaque()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TypeProto.Opaque)
}
TypeProto_Opaque::TypeProto_Opaque(const TypeProto_Opaque& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_domain()) {
    domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_name()) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  // @@protoc_insertion_point(copy_constructor:onnx.TypeProto.Opaque)
}

void TypeProto_Opaque::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TypeProto_Opaque_onnx_2dml_2eproto.base);
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

TypeProto_Opaque::~TypeProto_Opaque() {
  // @@protoc_insertion_point(destructor:onnx.TypeProto.Opaque)
  SharedDtor();
}

void TypeProto_Opaque::SharedDtor() {
  domain_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TypeProto_Opaque::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TypeProto_Opaque& TypeProto_Opaque::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TypeProto_Opaque_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TypeProto_Opaque::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TypeProto.Opaque)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      domain_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      name_.ClearNonDefaultToEmptyNoArena();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TypeProto_Opaque::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string domain = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_domain();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.TypeProto.Opaque.domain");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.TypeProto.Opaque.name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TypeProto_Opaque::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TypeProto.Opaque)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string domain = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_domain().data(), static_cast<int>(this->_internal_domain().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.TypeProto.Opaque.domain");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_domain(), target);
  }

  // optional string name = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.TypeProto.Opaque.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TypeProto.Opaque)
  return target;
}

size_t TypeProto_Opaque::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TypeProto.Opaque)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string domain = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_domain());
    }

    // optional string name = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TypeProto_Opaque::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TypeProto.Opaque)
  GOOGLE_DCHECK_NE(&from, this);
  const TypeProto_Opaque* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TypeProto_Opaque>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TypeProto.Opaque)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TypeProto.Opaque)
    MergeFrom(*source);
  }
}

void TypeProto_Opaque::MergeFrom(const TypeProto_Opaque& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TypeProto.Opaque)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
    }
  }
}

void TypeProto_Opaque::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TypeProto.Opaque)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TypeProto_Opaque::CopyFrom(const TypeProto_Opaque& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TypeProto.Opaque)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TypeProto_Opaque::IsInitialized() const {
  return true;
}

void TypeProto_Opaque::InternalSwap(TypeProto_Opaque* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  domain_.Swap(&other->domain_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata TypeProto_Opaque::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TypeProto::InitAsDefaultInstance() {
  ::onnx::_TypeProto_default_instance_.tensor_type_ = const_cast< ::onnx::TypeProto_Tensor*>(
      ::onnx::TypeProto_Tensor::internal_default_instance());
  ::onnx::_TypeProto_default_instance_.sequence_type_ = const_cast< ::onnx::TypeProto_Sequence*>(
      ::onnx::TypeProto_Sequence::internal_default_instance());
  ::onnx::_TypeProto_default_instance_.map_type_ = const_cast< ::onnx::TypeProto_Map*>(
      ::onnx::TypeProto_Map::internal_default_instance());
  ::onnx::_TypeProto_default_instance_.optional_type_ = const_cast< ::onnx::TypeProto_Optional*>(
      ::onnx::TypeProto_Optional::internal_default_instance());
  ::onnx::_TypeProto_default_instance_.sparse_tensor_type_ = const_cast< ::onnx::TypeProto_SparseTensor*>(
      ::onnx::TypeProto_SparseTensor::internal_default_instance());
  ::onnx::_TypeProto_default_instance_.opaque_type_ = const_cast< ::onnx::TypeProto_Opaque*>(
      ::onnx::TypeProto_Opaque::internal_default_instance());
}
class TypeProto::_Internal {
 public:
  using HasBits = decltype(std::declval<TypeProto>()._has_bits_);
  static const ::onnx::TypeProto_Tensor& tensor_type(const TypeProto* msg);
  static const ::onnx::TypeProto_Sequence& sequence_type(const TypeProto* msg);
  static const ::onnx::TypeProto_Map& map_type(const TypeProto* msg);
  static const ::onnx::TypeProto_Optional& optional_type(const TypeProto* msg);
  static const ::onnx::TypeProto_SparseTensor& sparse_tensor_type(const TypeProto* msg);
  static const ::onnx::TypeProto_Opaque& opaque_type(const TypeProto* msg);
  static void set_has_denotation(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::onnx::TypeProto_Tensor&
TypeProto::_Internal::tensor_type(const TypeProto* msg) {
  return *msg->value_.tensor_type_;
}
const ::onnx::TypeProto_Sequence&
TypeProto::_Internal::sequence_type(const TypeProto* msg) {
  return *msg->value_.sequence_type_;
}
const ::onnx::TypeProto_Map&
TypeProto::_Internal::map_type(const TypeProto* msg) {
  return *msg->value_.map_type_;
}
const ::onnx::TypeProto_Optional&
TypeProto::_Internal::optional_type(const TypeProto* msg) {
  return *msg->value_.optional_type_;
}
const ::onnx::TypeProto_SparseTensor&
TypeProto::_Internal::sparse_tensor_type(const TypeProto* msg) {
  return *msg->value_.sparse_tensor_type_;
}
const ::onnx::TypeProto_Opaque&
TypeProto::_Internal::opaque_type(const TypeProto* msg) {
  return *msg->value_.opaque_type_;
}
void TypeProto::set_allocated_tensor_type(::onnx::TypeProto_Tensor* tensor_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (tensor_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      tensor_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_type, submessage_arena);
    }
    set_has_tensor_type();
    value_.tensor_type_ = tensor_type;
  }
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.tensor_type)
}
void TypeProto::set_allocated_sequence_type(::onnx::TypeProto_Sequence* sequence_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (sequence_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      sequence_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sequence_type, submessage_arena);
    }
    set_has_sequence_type();
    value_.sequence_type_ = sequence_type;
  }
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.sequence_type)
}
void TypeProto::set_allocated_map_type(::onnx::TypeProto_Map* map_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (map_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      map_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, map_type, submessage_arena);
    }
    set_has_map_type();
    value_.map_type_ = map_type;
  }
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.map_type)
}
void TypeProto::set_allocated_optional_type(::onnx::TypeProto_Optional* optional_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (optional_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      optional_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optional_type, submessage_arena);
    }
    set_has_optional_type();
    value_.optional_type_ = optional_type;
  }
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.optional_type)
}
void TypeProto::set_allocated_sparse_tensor_type(::onnx::TypeProto_SparseTensor* sparse_tensor_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (sparse_tensor_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      sparse_tensor_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sparse_tensor_type, submessage_arena);
    }
    set_has_sparse_tensor_type();
    value_.sparse_tensor_type_ = sparse_tensor_type;
  }
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.sparse_tensor_type)
}
void TypeProto::set_allocated_opaque_type(::onnx::TypeProto_Opaque* opaque_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (opaque_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      opaque_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, opaque_type, submessage_arena);
    }
    set_has_opaque_type();
    value_.opaque_type_ = opaque_type;
  }
  // @@protoc_insertion_point(field_set_allocated:onnx.TypeProto.opaque_type)
}
TypeProto::TypeProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.TypeProto)
}
TypeProto::TypeProto(const TypeProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  denotation_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_denotation()) {
    denotation_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.denotation_);
  }
  clear_has_value();
  switch (from.value_case()) {
    case kTensorType: {
      _internal_mutable_tensor_type()->::onnx::TypeProto_Tensor::MergeFrom(from._internal_tensor_type());
      break;
    }
    case kSequenceType: {
      _internal_mutable_sequence_type()->::onnx::TypeProto_Sequence::MergeFrom(from._internal_sequence_type());
      break;
    }
    case kMapType: {
      _internal_mutable_map_type()->::onnx::TypeProto_Map::MergeFrom(from._internal_map_type());
      break;
    }
    case kOptionalType: {
      _internal_mutable_optional_type()->::onnx::TypeProto_Optional::MergeFrom(from._internal_optional_type());
      break;
    }
    case kSparseTensorType: {
      _internal_mutable_sparse_tensor_type()->::onnx::TypeProto_SparseTensor::MergeFrom(from._internal_sparse_tensor_type());
      break;
    }
    case kOpaqueType: {
      _internal_mutable_opaque_type()->::onnx::TypeProto_Opaque::MergeFrom(from._internal_opaque_type());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:onnx.TypeProto)
}

void TypeProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TypeProto_onnx_2dml_2eproto.base);
  denotation_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  clear_has_value();
}

TypeProto::~TypeProto() {
  // @@protoc_insertion_point(destructor:onnx.TypeProto)
  SharedDtor();
}

void TypeProto::SharedDtor() {
  denotation_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (has_value()) {
    clear_value();
  }
}

void TypeProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TypeProto& TypeProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TypeProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void TypeProto::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:onnx.TypeProto)
  switch (value_case()) {
    case kTensorType: {
      delete value_.tensor_type_;
      break;
    }
    case kSequenceType: {
      delete value_.sequence_type_;
      break;
    }
    case kMapType: {
      delete value_.map_type_;
      break;
    }
    case kOptionalType: {
      delete value_.optional_type_;
      break;
    }
    case kSparseTensorType: {
      delete value_.sparse_tensor_type_;
      break;
    }
    case kOpaqueType: {
      delete value_.opaque_type_;
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void TypeProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.TypeProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    denotation_.ClearNonDefaultToEmptyNoArena();
  }
  clear_value();
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* TypeProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .onnx.TypeProto.Tensor tensor_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_tensor_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TypeProto.Sequence sequence_type = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_sequence_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TypeProto.Map map_type = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_map_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string denotation = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_denotation();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.TypeProto.denotation");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TypeProto.Opaque opaque_type = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_opaque_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TypeProto.SparseTensor sparse_tensor_type = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_sparse_tensor_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .onnx.TypeProto.Optional optional_type = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_optional_type(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TypeProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.TypeProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (value_case()) {
    case kTensorType: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(
          1, _Internal::tensor_type(this), target, stream);
      break;
    }
    case kSequenceType: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(
          4, _Internal::sequence_type(this), target, stream);
      break;
    }
    case kMapType: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(
          5, _Internal::map_type(this), target, stream);
      break;
    }
    default: ;
  }
  cached_has_bits = _has_bits_[0];
  // optional string denotation = 6;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_denotation().data(), static_cast<int>(this->_internal_denotation().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.TypeProto.denotation");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_denotation(), target);
  }

  switch (value_case()) {
    case kOpaqueType: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(
          7, _Internal::opaque_type(this), target, stream);
      break;
    }
    case kSparseTensorType: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(
          8, _Internal::sparse_tensor_type(this), target, stream);
      break;
    }
    case kOptionalType: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(
          9, _Internal::optional_type(this), target, stream);
      break;
    }
    default: ;
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.TypeProto)
  return target;
}

size_t TypeProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.TypeProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string denotation = 6;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_denotation());
  }

  switch (value_case()) {
    // optional .onnx.TypeProto.Tensor tensor_type = 1;
    case kTensorType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.tensor_type_);
      break;
    }
    // optional .onnx.TypeProto.Sequence sequence_type = 4;
    case kSequenceType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.sequence_type_);
      break;
    }
    // optional .onnx.TypeProto.Map map_type = 5;
    case kMapType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.map_type_);
      break;
    }
    // optional .onnx.TypeProto.Optional optional_type = 9;
    case kOptionalType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.optional_type_);
      break;
    }
    // optional .onnx.TypeProto.SparseTensor sparse_tensor_type = 8;
    case kSparseTensorType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.sparse_tensor_type_);
      break;
    }
    // optional .onnx.TypeProto.Opaque opaque_type = 7;
    case kOpaqueType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.opaque_type_);
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TypeProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.TypeProto)
  GOOGLE_DCHECK_NE(&from, this);
  const TypeProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TypeProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.TypeProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.TypeProto)
    MergeFrom(*source);
  }
}

void TypeProto::MergeFrom(const TypeProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.TypeProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_denotation()) {
    _has_bits_[0] |= 0x00000001u;
    denotation_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.denotation_);
  }
  switch (from.value_case()) {
    case kTensorType: {
      _internal_mutable_tensor_type()->::onnx::TypeProto_Tensor::MergeFrom(from._internal_tensor_type());
      break;
    }
    case kSequenceType: {
      _internal_mutable_sequence_type()->::onnx::TypeProto_Sequence::MergeFrom(from._internal_sequence_type());
      break;
    }
    case kMapType: {
      _internal_mutable_map_type()->::onnx::TypeProto_Map::MergeFrom(from._internal_map_type());
      break;
    }
    case kOptionalType: {
      _internal_mutable_optional_type()->::onnx::TypeProto_Optional::MergeFrom(from._internal_optional_type());
      break;
    }
    case kSparseTensorType: {
      _internal_mutable_sparse_tensor_type()->::onnx::TypeProto_SparseTensor::MergeFrom(from._internal_sparse_tensor_type());
      break;
    }
    case kOpaqueType: {
      _internal_mutable_opaque_type()->::onnx::TypeProto_Opaque::MergeFrom(from._internal_opaque_type());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
}

void TypeProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.TypeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TypeProto::CopyFrom(const TypeProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.TypeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TypeProto::IsInitialized() const {
  return true;
}

void TypeProto::InternalSwap(TypeProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  denotation_.Swap(&other->denotation_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata TypeProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void OperatorSetIdProto::InitAsDefaultInstance() {
}
class OperatorSetIdProto::_Internal {
 public:
  using HasBits = decltype(std::declval<OperatorSetIdProto>()._has_bits_);
  static void set_has_domain(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_version(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

OperatorSetIdProto::OperatorSetIdProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.OperatorSetIdProto)
}
OperatorSetIdProto::OperatorSetIdProto(const OperatorSetIdProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_domain()) {
    domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
  }
  version_ = from.version_;
  // @@protoc_insertion_point(copy_constructor:onnx.OperatorSetIdProto)
}

void OperatorSetIdProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_OperatorSetIdProto_onnx_2dml_2eproto.base);
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  version_ = PROTOBUF_LONGLONG(0);
}

OperatorSetIdProto::~OperatorSetIdProto() {
  // @@protoc_insertion_point(destructor:onnx.OperatorSetIdProto)
  SharedDtor();
}

void OperatorSetIdProto::SharedDtor() {
  domain_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void OperatorSetIdProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const OperatorSetIdProto& OperatorSetIdProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_OperatorSetIdProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void OperatorSetIdProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.OperatorSetIdProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    domain_.ClearNonDefaultToEmptyNoArena();
  }
  version_ = PROTOBUF_LONGLONG(0);
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* OperatorSetIdProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string domain = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_domain();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.OperatorSetIdProto.domain");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int64 version = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_version(&has_bits);
          version_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* OperatorSetIdProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.OperatorSetIdProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string domain = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_domain().data(), static_cast<int>(this->_internal_domain().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.OperatorSetIdProto.domain");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_domain(), target);
  }

  // optional int64 version = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_version(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.OperatorSetIdProto)
  return target;
}

size_t OperatorSetIdProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.OperatorSetIdProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string domain = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_domain());
    }

    // optional int64 version = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_version());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OperatorSetIdProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.OperatorSetIdProto)
  GOOGLE_DCHECK_NE(&from, this);
  const OperatorSetIdProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<OperatorSetIdProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.OperatorSetIdProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.OperatorSetIdProto)
    MergeFrom(*source);
  }
}

void OperatorSetIdProto::MergeFrom(const OperatorSetIdProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.OperatorSetIdProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
    }
    if (cached_has_bits & 0x00000002u) {
      version_ = from.version_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void OperatorSetIdProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.OperatorSetIdProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OperatorSetIdProto::CopyFrom(const OperatorSetIdProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.OperatorSetIdProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OperatorSetIdProto::IsInitialized() const {
  return true;
}

void OperatorSetIdProto::InternalSwap(OperatorSetIdProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  domain_.Swap(&other->domain_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(version_, other->version_);
}

::PROTOBUF_NAMESPACE_ID::Metadata OperatorSetIdProto::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void FunctionProto::InitAsDefaultInstance() {
}
class FunctionProto::_Internal {
 public:
  using HasBits = decltype(std::declval<FunctionProto>()._has_bits_);
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_doc_string(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_domain(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

FunctionProto::FunctionProto()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:onnx.FunctionProto)
}
FunctionProto::FunctionProto(const FunctionProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      input_(from.input_),
      output_(from.output_),
      attribute_(from.attribute_),
      node_(from.node_),
      opset_import_(from.opset_import_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_name()) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_doc_string()) {
    doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
  }
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_domain()) {
    domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
  }
  // @@protoc_insertion_point(copy_constructor:onnx.FunctionProto)
}

void FunctionProto::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_FunctionProto_onnx_2dml_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  domain_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

FunctionProto::~FunctionProto() {
  // @@protoc_insertion_point(destructor:onnx.FunctionProto)
  SharedDtor();
}

void FunctionProto::SharedDtor() {
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  doc_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  domain_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void FunctionProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const FunctionProto& FunctionProto::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_FunctionProto_onnx_2dml_2eproto.base);
  return *internal_default_instance();
}


void FunctionProto::Clear() {
// @@protoc_insertion_point(message_clear_start:onnx.FunctionProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_.Clear();
  output_.Clear();
  attribute_.Clear();
  node_.Clear();
  opset_import_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      name_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000002u) {
      doc_string_.ClearNonDefaultToEmptyNoArena();
    }
    if (cached_has_bits & 0x00000004u) {
      domain_.ClearNonDefaultToEmptyNoArena();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

const char* FunctionProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.FunctionProto.name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string input = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_input();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.FunctionProto.input");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated string output = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_output();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.FunctionProto.output");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated string attribute = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_attribute();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.FunctionProto.attribute");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .onnx.NodeProto node = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_node(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else goto handle_unusual;
        continue;
      // optional string doc_string = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_doc_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.FunctionProto.doc_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .onnx.OperatorSetIdProto opset_import = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_opset_import(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else goto handle_unusual;
        continue;
      // optional string domain = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_domain();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "onnx.FunctionProto.domain");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* FunctionProto::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:onnx.FunctionProto)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string name = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.FunctionProto.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // repeated string input = 4;
  for (int i = 0, n = this->_internal_input_size(); i < n; i++) {
    const auto& s = this->_internal_input(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.FunctionProto.input");
    target = stream->WriteString(4, s, target);
  }

  // repeated string output = 5;
  for (int i = 0, n = this->_internal_output_size(); i < n; i++) {
    const auto& s = this->_internal_output(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.FunctionProto.output");
    target = stream->WriteString(5, s, target);
  }

  // repeated string attribute = 6;
  for (int i = 0, n = this->_internal_attribute_size(); i < n; i++) {
    const auto& s = this->_internal_attribute(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.FunctionProto.attribute");
    target = stream->WriteString(6, s, target);
  }

  // repeated .onnx.NodeProto node = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_node_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, this->_internal_node(i), target, stream);
  }

  // optional string doc_string = 8;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_doc_string().data(), static_cast<int>(this->_internal_doc_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.FunctionProto.doc_string");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_doc_string(), target);
  }

  // repeated .onnx.OperatorSetIdProto opset_import = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_opset_import_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(9, this->_internal_opset_import(i), target, stream);
  }

  // optional string domain = 10;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_domain().data(), static_cast<int>(this->_internal_domain().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "onnx.FunctionProto.domain");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_domain(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:onnx.FunctionProto)
  return target;
}

size_t FunctionProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:onnx.FunctionProto)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string input = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(input_.size());
  for (int i = 0, n = input_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      input_.Get(i));
  }

  // repeated string output = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(output_.size());
  for (int i = 0, n = output_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      output_.Get(i));
  }

  // repeated string attribute = 6;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(attribute_.size());
  for (int i = 0, n = attribute_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      attribute_.Get(i));
  }

  // repeated .onnx.NodeProto node = 7;
  total_size += 1UL * this->_internal_node_size();
  for (const auto& msg : this->node_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .onnx.OperatorSetIdProto opset_import = 9;
  total_size += 1UL * this->_internal_opset_import_size();
  for (const auto& msg : this->opset_import_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    // optional string name = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

    // optional string doc_string = 8;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_doc_string());
    }

    // optional string domain = 10;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_domain());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FunctionProto::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:onnx.FunctionProto)
  GOOGLE_DCHECK_NE(&from, this);
  const FunctionProto* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<FunctionProto>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:onnx.FunctionProto)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:onnx.FunctionProto)
    MergeFrom(*source);
  }
}

void FunctionProto::MergeFrom(const FunctionProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:onnx.FunctionProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  input_.MergeFrom(from.input_);
  output_.MergeFrom(from.output_);
  attribute_.MergeFrom(from.attribute_);
  node_.MergeFrom(from.node_);
  opset_import_.MergeFrom(from.opset_import_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _has_bits_[0] |= 0x00000001u;
      name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
    }
    if (cached_has_bits & 0x00000002u) {
      _has_bits_[0] |= 0x00000002u;
      doc_string_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.doc_string_);
    }
    if (cached_has_bits & 0x00000004u) {
      _has_bits_[0] |= 0x00000004u;
      domain_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.domain_);
    }
  }
}

void FunctionProto::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:onnx.FunctionProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FunctionProto::CopyFrom(const FunctionProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:onnx.FunctionProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FunctionProto::IsInitialized() const {
  return true;
}

void FunctionProto::InternalSwap(FunctionProto* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  input_.InternalSwap(&other->input_);
  output_.InternalSwap(&other->output_);
  attribute_.InternalSwap(&other->attribute_);
  node_.InternalSwap(&other->node_);
  opset_import_.InternalSwap(&other->opset_import_);
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  doc_string_.Swap(&other->doc_string_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  domain_.Swap(&other->domain_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata FunctionProto::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace onnx
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::onnx::AttributeProto* Arena::CreateMaybeMessage< ::onnx::AttributeProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::AttributeProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::ValueInfoProto* Arena::CreateMaybeMessage< ::onnx::ValueInfoProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::ValueInfoProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::NodeProto* Arena::CreateMaybeMessage< ::onnx::NodeProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::NodeProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TrainingInfoProto* Arena::CreateMaybeMessage< ::onnx::TrainingInfoProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TrainingInfoProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::ModelProto* Arena::CreateMaybeMessage< ::onnx::ModelProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::ModelProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::StringStringEntryProto* Arena::CreateMaybeMessage< ::onnx::StringStringEntryProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::StringStringEntryProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TensorAnnotation* Arena::CreateMaybeMessage< ::onnx::TensorAnnotation >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TensorAnnotation >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::GraphProto* Arena::CreateMaybeMessage< ::onnx::GraphProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::GraphProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TensorProto_Segment* Arena::CreateMaybeMessage< ::onnx::TensorProto_Segment >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TensorProto_Segment >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TensorProto* Arena::CreateMaybeMessage< ::onnx::TensorProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TensorProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::SparseTensorProto* Arena::CreateMaybeMessage< ::onnx::SparseTensorProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::SparseTensorProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TensorShapeProto_Dimension* Arena::CreateMaybeMessage< ::onnx::TensorShapeProto_Dimension >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TensorShapeProto_Dimension >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TensorShapeProto* Arena::CreateMaybeMessage< ::onnx::TensorShapeProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TensorShapeProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TypeProto_Tensor* Arena::CreateMaybeMessage< ::onnx::TypeProto_Tensor >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TypeProto_Tensor >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TypeProto_Sequence* Arena::CreateMaybeMessage< ::onnx::TypeProto_Sequence >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TypeProto_Sequence >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TypeProto_Map* Arena::CreateMaybeMessage< ::onnx::TypeProto_Map >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TypeProto_Map >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TypeProto_Optional* Arena::CreateMaybeMessage< ::onnx::TypeProto_Optional >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TypeProto_Optional >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TypeProto_SparseTensor* Arena::CreateMaybeMessage< ::onnx::TypeProto_SparseTensor >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TypeProto_SparseTensor >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TypeProto_Opaque* Arena::CreateMaybeMessage< ::onnx::TypeProto_Opaque >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TypeProto_Opaque >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::TypeProto* Arena::CreateMaybeMessage< ::onnx::TypeProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::TypeProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::OperatorSetIdProto* Arena::CreateMaybeMessage< ::onnx::OperatorSetIdProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::OperatorSetIdProto >(arena);
}
template<> PROTOBUF_NOINLINE ::onnx::FunctionProto* Arena::CreateMaybeMessage< ::onnx::FunctionProto >(Arena* arena) {
  return Arena::CreateInternal< ::onnx::FunctionProto >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

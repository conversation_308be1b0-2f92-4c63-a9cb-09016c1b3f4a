### Release/1.0.0 目录结构
```
Release/1.0.0/
├── config.json  # 参数配置文件（重点修改模型加载路径）
├── libbirdtrack.so  # 算法动态库
├── pro  算法测试程序，用于环境调试
├── trtmodel/  # 模型权重
│   ├── best_ckpt_airplane_IR_v2.3_512x640.FP16.trtmodel
│   ├── drone_bird.FP16.trtmodel
│   ├── ground_ir.FP16.trtmodel
│   ├── ground_vl.FP16.trtmodel
│   └── yoloe-11s.FP16.trtmodel
└── videos/  # 测试视频
    └── fly1.mp4
```


### v1.0.0
1. 完成3: bird 4:UAV 7：空飘物三个类别的检测，4060GPU单帧单路耗时约10ms